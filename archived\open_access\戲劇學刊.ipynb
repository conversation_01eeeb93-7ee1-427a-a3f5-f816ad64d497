{"cells": [{"cell_type": "code", "execution_count": null, "id": "79300976", "metadata": {}, "outputs": [], "source": ["!pip install -U openpyxl selenium beautifulsoup4 lxml requests tika"]}, {"cell_type": "code", "execution_count": null, "id": "b836552a", "metadata": {}, "outputs": [], "source": ["'''\n", "注意事項:\n", "下載對應的 ChromeDriver (web driver) 到程式檔案同一個目錄下後解壓縮，下載前記得對應版本編號。\n", "連結: https://chromedriver.chromium.org/downloads\n", "\n", "參考網頁:\n", "[1] 戲劇學刊\n", "http://1www.tnua.edu.tw/~TNUA_THEATRE/ttj/super_pages.php?ID=ttj3\n", "[2] sqlite3 --- SQLite 数据库 DB-API 2.0 接口模块\n", "https://docs.python.org/zh-tw/3/library/sqlite3.html\n", "'''\n", "\n", "\n", "'''\n", "匯入套件\n", "'''\n", "# 網路請求工具\n", "import requests as req\n", "\n", "# HTML parser\n", "from bs4 import BeautifulSoup as bs\n", "\n", "# 操作 browser 的 API\n", "from selenium import webdriver\n", "\n", "# 處理逾時例外的工具\n", "from selenium.common.exceptions import TimeoutException\n", "\n", "# 面對動態網頁，等待某個元素出現的工具，通常與 exptected_conditions 搭配\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "\n", "# 搭配 WebDriverWait 使用，對元素狀態的一種期待條件，若條件發生，則等待結束，往下一行執行\n", "from selenium.webdriver.support import expected_conditions as EC\n", "\n", "# 期待元素出現要透過什麼方式指定，通常與 EC、WebDriverWait 一起使用\n", "from selenium.webdriver.common.by import By\n", "\n", "# 處理下拉式選單的工具\n", "from selenium.webdriver.support.ui import Select\n", "\n", "# 強制等待 (執行期間休息一下)\n", "from time import sleep\n", "\n", "# pretty-print\n", "from pprint import pprint\n", "\n", "# 隨機\n", "from random import randint\n", "\n", "# 計時\n", "import time\n", "\n", "# 整理 json 使用的工具\n", "import json\n", "\n", "# 執行 shell command 的時候用的\n", "import os\n", "\n", "# 取得錯誤訊息\n", "import sys, traceback\n", "\n", "# 子處理程序，用來取代 os.system 的功能\n", "import subprocess\n", "\n", "# 正規表達式\n", "import re\n", "\n", "# 編碼\n", "from urllib.parse import quote\n", "\n", "# SQLite 資料庫\n", "import sqlite3\n", "\n", "# 存取 Excel 的工具\n", "from openpyxl import load_workbook\n", "from openpyxl import Workbook\n", "\n", "# 取得系統時間的工具\n", "from datetime import datetime\n", "\n", "# 引入 hashlib 模組\n", "import hashlib\n", "\n", "# 高階文件操作工具\n", "import shutil\n", "\n", "# 檔案剖析工具\n", "from tika import parser"]}, {"cell_type": "code", "execution_count": null, "id": "9533fcf7", "metadata": {}, "outputs": [], "source": ["# 啟動瀏覽器工具的選項\n", "my_options = webdriver.ChromeOptions()\n", "# my_options.add_argument(\"--headless\")             #不開啟實體瀏覽器背景執行\n", "my_options.add_argument(\"--start-maximized\")        #最大化視窗\n", "my_options.add_argument(\"--incognito\")              #開啟無痕模式\n", "my_options.add_argument(\"--disable-popup-blocking\") #禁用彈出攔截\n", "my_options.add_argument(\"--disable-notifications\")  #取消通知\n", "my_options.add_argument(\"--lang=zh-TW\")  #設定為正體中文\n", "my_options.add_argument('--disable-gpu')\n", "my_options.add_argument('--disable-software-rasterizer')\n", "my_options.add_argument('--user-agent=\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Safari/537.36\"')\n", "\n", "# 指定 chromedriver 檔案的路徑\n", "driver_exec_path = './chromedriver.exe'\n", "\n", "# 給 web driver 用的變數\n", "driver = None\n", "\n", "# 來源首頁\n", "root_url = 'http://1www.tnua.edu.tw'\n", "prefix_url = root_url + '/~TNUA_THEATRE'\n", "path_url = prefix_url + '/ttj/super_pages.php'\n", "url = path_url + '?ID=ttj3'\n", "\n", "# 指定 sheet name\n", "folderName = sheetName = '1www_tnua_edu_tw'\n", "\n", "# 指定 json 檔名\n", "jsonFileName = f'{folderName}.json'\n", "\n", "# 建立儲存檔案用的資料夾\n", "folderPath = f'./{folderName}'\n", "if not os.path.exists(folderPath):\n", "    os.makedirs(folderPath)\n", "    \n", "# 設定 Chrome 下載路徑 (需要絕對路徑)\n", "fullDownloadPath = os.getcwd() + '\\\\' + folderName\n", "\n", "#預設下載路徑\n", "my_options.add_experimental_option(\"prefs\", {\n", "    \"download.default_directory\": fullDownloadPath,\n", "    \"download.prompt_for_download\": <PERSON>als<PERSON>,\n", "    \"download.directory_upgrade\": True,\n", "    \"safebrowsing_for_trusted_sources_enabled\": False,\n", "    \"safebrowsing.enabled\": <PERSON><PERSON><PERSON>,\n", "    \"plugins.always_open_pdf_externally\": True\n", "})\n", "\n", "# 請求標頭\n", "my_headers = {\n", "    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.54 Safari/537.36'\n", "}\n", "\n", "# 放置爬取的資料\n", "listData = []\n", "\n", "# 建立一個 Set 物件，準備 add 所有 tuple，這些 tuple 裡面都有 dict_items 物件\n", "mySet = set()"]}, {"cell_type": "code", "execution_count": null, "id": "04a414d2", "metadata": {}, "outputs": [], "source": ["'''\n", "函式\n", "'''\n", "# md5 (用來為每一筆資料建立唯一代號)\n", "def md5(string):\n", "    m = hashlib.md5()\n", "    m.update(string.encode(\"utf-8\"))\n", "    return m.hexdigest()\n", "\n", "# 初始化設定\n", "def init():\n", "    global listData, mySet\n", "    listData = []\n", "    mySet = set()\n", "    \n", "# 走訪頁面\n", "def visit():\n", "    # 取得 html 並轉成 soup 物件\n", "    res = req.get(url = url, headers = my_headers)\n", "    res.encoding = 'utf-8'\n", "    soup = bs(res.text, 'lxml')\n", "    \n", "    # 取得期號與出版日期\n", "    regexPublish = r'第(\\d{1,2})期\\((\\d{4}\\.\\d{1,2})\\)'\n", "    \n", "    # 有期刊超連結相關資訊，便整理起來\n", "    if len(soup.select('div#super_pages_list ul li a[href]')) > 0:\n", "        a_elms = soup.select('div#super_pages_list ul li a[href]')\n", "        for a in a_elms:\n", "            # 取得連結名稱\n", "            str_title = a.get_text()\n", "            \n", "            # 取得刊號與出版日期\n", "            publish_num = publish_date = ''\n", "            matchPublish = re.search(regexPublish, str_title)\n", "            if matchPublish != None:\n", "                publish_num = matchPublish[1]\n", "                publish_date = matchPublish[2]\n", "            \n", "            listData.append({\n", "                'journal_title': '戲劇學刊',\n", "                'journal_sub_title': str_title,\n", "                'publish_num': publish_num,\n", "                'publish_date': publish_date,\n", "                'link': path_url + a['href'],\n", "                'sub': []\n", "            })\n", "    \n", "    \n", "# 剖析元素資料\n", "def parse():\n", "    global listData\n", "    \n", "    # 走訪內頁\n", "    for index, myDict in enumerate(listData):\n", "        # 取得 html 與 soup 物件\n", "        res = req.get(url = myDict['link'], headers = my_headers)\n", "        res.encoding = 'utf-8'\n", "        soup = bs(res.text, 'lxml')\n", "\n", "        # 變數初始化\n", "        journal_name = pdf_link = author = ''\n", "        \n", "        # 取得有期刊的元素\n", "        if len( soup.select('div#pages_area table tbody tr') ) > 0:\n", "            # 網頁是舊版本，則使用 tr 來尋找資料\n", "            for tr in soup.select('div#pages_area table tbody tr'):\n", "                # 取得 tr 當中的 td\n", "                td_elms = tr.select('td')\n", "\n", "                # 取得 journal_name 與 pdf_link\n", "                if len( td_elms[0].select('a') ) > 0:\n", "                    # 取得 journal_name\n", "                    journal_name = td_elms[0].select_one('a').get_text().strip()\n", "                    \n", "                    # 取得 pdf_link\n", "                    if 'dropbox' not in td_elms[0].select_one('a')['href']:\n", "                        pdf_link = root_url + td_elms[0].select_one('a')['href']\n", "                    else:\n", "                        pdf_link = td_elms[0].select_one('a')['href']\n", "                        pdf_link = pdf_link.replace('?dl=0', '?dl=1')\n", "\n", "                    # 取得 author\n", "                    if len( td_elms[1].select('a') ) > 0:\n", "                        author = td_elms[1].select_one('a').get_text().strip()\n", "                    else:\n", "                        author = td_elms[1].get_text().strip()\n", "                        \n", "                    # 資料是否已經有寫入 set 當中，有則忽略\n", "                    if md5(pdf_link) not in mySet:\n", "                        # 整理資料\n", "                        listData[index]['sub'].append({\n", "                            'id': md5(pdf_link),\n", "                            'journal_name': journal_name,\n", "                            'pdf_link': pdf_link,\n", "                            'author': author\n", "                        })\n", "                        \n", "                        # 將 id 加入 set，以便後續確認是否重複\n", "                        mySet.add(md5(pdf_link))\n", "                \n", "        else:\n", "            # 網頁是新版本，則各別解析元素內容\n", "            regexPaper_step_1 = r'<a\\shref=\\\".+?\\\">.+?<\\/a>.+?<br\\s?\\/?>'\n", "            regexPaper_step_2 = r'(<a\\shref=\\\".+?\\\">.+?<\\/a>)(.+?)<br\\s?\\/?>'\n", "            regexPaper_step_3 = r'\\u3000|\\d{1,3}(-\\d{1,3})?|<a.+?>|<\\/a>|\\s+'\n", "            str_html = str( soup.select_one('div#pages_area div#editor') )\n", "            listPaper = re.findall(regexPaper_step_1, str_html)\n", "            for html in listPaper:\n", "                str_purified_content = re.sub(r'\\xa0', '', html)\n", "                match_iter = re.finditer(regexPaper_step_2, str_purified_content)\n", "                if match_iter != None:\n", "                    for match in match_iter:\n", "                        # 取得 a 元素\n", "                        str_a_tag = match[1]\n", "                        soup_a = bs(str_a_tag, 'lxml')\n", "                        a_elm = soup_a.select_one('a')\n", "                        \n", "                        # 取得 journal_name 與 pdf_link\n", "                        journal_name = a_elm.get_text()\n", "                        if 'dropbox' not in a_elm['href']:\n", "                            if root_url not in a_elm['href']:\n", "                                pdf_link = root_url + a_elm['href']\n", "                            else:\n", "                                pdf_link = a_elm['href']\n", "                        else:\n", "                            pdf_link = a_elm['href']\n", "                            pdf_link = pdf_link.replace('?dl=0', '?dl=1')\n", "                        \n", "                        # 取得 author\n", "                        author = match[2]\n", "                        author = re.sub(regexPaper_step_3, '', author)\n", "                        \n", "                        # 資料是否已經有寫入 set 當中，有則忽略\n", "                        if md5(pdf_link) not in mySet:\n", "                            # 整理資料\n", "                            listData[index]['sub'].append({\n", "                                'id': md5(pdf_link),\n", "                                'journal_name': journal_name,\n", "                                'pdf_link': pdf_link,\n", "                                'author': author\n", "                            })\n", "                            \n", "                            # 將 id 加入 set，以便後續確認是否重複\n", "                            mySet.add(md5(pdf_link))\n", "        \n", "# 儲存成 json\n", "def save<PERSON><PERSON>():\n", "    global listData\n", "    with open(f\"{folderPath}/{jsonFileName}\", \"w\", encoding=\"utf-8\") as file:\n", "        file.write( json.dumps( listData, ensure_ascii=False, indent=4 ) )\n", "\n", "# 建立 .db\n", "def makeDB():\n", "    conn = sqlite3.connect(f\"{folderPath}/{folderName}.db\")\n", "    cursor = conn.cursor()\n", "    sql = '''\n", "    CREATE TABLE \"journals\" (\n", "        \"sn\"    INTEGER,\n", "        \"id\"    TEXT UNIQUE,\n", "        \"journal_title\"    TEXT,\n", "        \"journal_sub_title\"    TEXT,\n", "        \"publish_num\"    TEXT,\n", "        \"publish_date\"    TEXT,\n", "        \"journal_name\"    TEXT,\n", "        \"author\"    <PERSON><PERSON><PERSON>,\n", "        \"link\"    TEXT,\n", "        \"pdf_link\"    TEXT,\n", "        \"is_downloaded\"    INTEGER,\n", "        \"created_at\"    TEXT,\n", "        \"updated_at\"    TEXT,\n", "        PRIMARY KEY(\"sn\" AUTOINCREMENT)\n", "    );\n", "    '''\n", "    cursor.execute(sql)\n", "    conn.commit()\n", "    \n", "    # 關閉 sqlite\n", "    conn.close()\n", "    \n", "    \n", "# 儲存 .db\n", "def saveDB():\n", "    with open(f\"{folderPath}/{jsonFileName}\", \"r\", encoding=\"utf-8\") as file:      \n", "        # 取得 json 內容\n", "        strJson = file.read()\n", "        \n", "        # 將 json 轉成 list\n", "        listJson = json.loads(strJson)\n", "        \n", "    # 寫入對話記錄\n", "    conn = sqlite3.connect(f\"{folderPath}/{folderName}.db\")\n", "    \n", "    # 建立 cursor 物件\n", "    cursor = conn.cursor()\n", "\n", "    # 執行 SQL 語法\n", "    try:\n", "        # 查詢特定資料，看看是否已經存在於資料表當中\n", "        sql_query = f'''\n", "        SELECT 1\n", "        FROM journals\n", "        WHERE id = ?\n", "        '''\n", "        \n", "        # 寫入資料\n", "        sql_insert = f'''\n", "        INSERT INTO journals (\n", "            id, journal_title, journal_sub_title, publish_num, publish_date, \n", "            journal_name, author, link, pdf_link, is_downloaded, \n", "            created_at, updated_at\n", "        ) VALUES ( \n", "            ?,?,?,?,?,\n", "            ?,?,?,?,?,\n", "            ?,?\n", "        )\n", "        '''\n", "        \n", "        # 放置準備寫入的資料\n", "        list_insert = []\n", "        \n", "        # 將 json 資料一筆一筆找出來\n", "        for index, myDict in enumerate(listJson):\n", "            for d in listJson[index]['sub']:\n", "                # 如果資料庫沒有這筆資料(透過 id)，則將資料以 tuple 格式放到 list 當中，方便新增 bulk 資料\n", "                if cursor.execute(sql_query, (d[\"id\"],)).fetchone() == None:\n", "                    # 整合所有需要寫入的資料\n", "                    list_insert.append((\n", "                        d['id'],\n", "                        myDict['journal_title'],\n", "                        myDict['journal_sub_title'],\n", "                        myDict['publish_num'],\n", "                        myDict['publish_date'],\n", "                        d['journal_name'],\n", "                        d['author'],\n", "                        myDict['link'],\n", "                        d['pdf_link'],\n", "                        0,\n", "                        datetime.today().strftime(\"%Y-%m-%d %H-%M-%S\"),\n", "                        datetime.today().strftime(\"%Y-%m-%d %H-%M-%S\")\n", "                    ))\n", "        \n", "        # 新增資料到資料庫當中\n", "        cursor.executemany(sql_insert, list_insert)\n", "        \n", "        # 執行 SQL 語法\n", "        conn.commit()\n", "    except sqlite3.<PERSON>rror as err: \n", "        # 回滾\n", "        conn.rollback()\n", "\n", "        # SQLite3 例外處理\n", "        exc_type, exc_value, exc_tb = sys.exc_info()\n", "        strErrorMsg = f'''SQLite error: {' '.join(err.args)}\\n\\n\n", "        SQLite traceback: {traceback.format_exception(exc_type, exc_value, exc_tb)}\n", "        '''\n", "        print(strErrorMsg)\n", "    finally:\n", "        # 關閉 sqlite\n", "        conn.close()\n", "        \n", "# 下載\n", "def download():\n", "    # 寫入對話記錄\n", "    conn = sqlite3.connect(f\"{folderPath}/{folderName}.db\")\n", "    \n", "    # 將查詢出來的結果 (tuple)，變成 key-value 型式 (dict)\n", "    conn.row_factory = sqlite3.Row\n", "    \n", "    # 建立 cursor 物件\n", "    cursor = conn.cursor()\n", "\n", "    # 執行 SQL 語法\n", "    try:\n", "        # 查詢尚未下載的資料\n", "        sql_query = f'''\n", "        SELECT sn, id, pdf_link\n", "        FROM journals\n", "        WHERE `is_downloaded` = 0\n", "        '''\n", "        \n", "        # 更新資料的欄位(是否下載過)\n", "        sql_update = f'''\n", "        UPDATE `journals` \n", "        SET \n", "            `is_downloaded` = 1 ,\n", "            `updated_at` = ?\n", "        WHERE `id` = ?\n", "        '''\n", "            \n", "        # 取得所有未下載的資料\n", "        for myDict in cursor.execute(sql_query).fetchall():\n", "            # 等待\n", "            sleep(randint(1,2))\n", "\n", "            # 下載 pdf\n", "            cmd = [\n", "                'curl', \n", "                '-k', '-L', \n", "                '-H', 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Safari/537.36',\n", "                myDict[\"pdf_link\"], \n", "                '-o', f'{folderPath}/{myDict[\"sn\"]}_{myDict[\"id\"]}.pdf'\n", "            ]\n", "            result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT)\n", "            pprint(result.stdout)\n", "            print(\"=\" * 50)\n", "\n", "            # 將 is_downloaded 改成 1，代表已下載過\n", "            cursor.execute(sql_update, (datetime.today().strftime(\"%Y-%m-%d %H-%M-%S\"), myDict[\"id\"],))\n", "                \n", "        conn.commit()\n", "    except sqlite3.<PERSON>rror as err: \n", "        # 回滾\n", "        conn.rollback()\n", "\n", "        # SQLite3 例外處理\n", "        exc_type, exc_value, exc_tb = sys.exc_info()\n", "        strErrorMsg = f'''SQLite error: {' '.join(err.args)}\\n\\n\n", "        SQLite traceback: {traceback.format_exception(exc_type, exc_value, exc_tb)}\n", "        '''\n", "        print(strErrorMsg)\n", "    finally:\n", "        # 關閉 sqlite\n", "        conn.close()"]}, {"cell_type": "code", "execution_count": null, "id": "9a79d6b1", "metadata": {}, "outputs": [], "source": ["init()"]}, {"cell_type": "code", "execution_count": null, "id": "61c563e1", "metadata": {"scrolled": false}, "outputs": [], "source": ["visit()"]}, {"cell_type": "code", "execution_count": null, "id": "1b885451", "metadata": {"scrolled": true}, "outputs": [], "source": ["parse()"]}, {"cell_type": "code", "execution_count": null, "id": "1eb59bb9", "metadata": {}, "outputs": [], "source": ["<PERSON><PERSON><PERSON>()"]}, {"cell_type": "code", "execution_count": null, "id": "fd2ee16d", "metadata": {}, "outputs": [], "source": ["makeDB()"]}, {"cell_type": "code", "execution_count": null, "id": "fc157d48", "metadata": {}, "outputs": [], "source": ["saveDB()"]}, {"cell_type": "code", "execution_count": null, "id": "009a0bef", "metadata": {"scrolled": true}, "outputs": [], "source": ["download()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}}, "nbformat": 4, "nbformat_minor": 5}