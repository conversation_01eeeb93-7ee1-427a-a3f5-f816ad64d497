{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install beautifulsoup4 requests pandas OpenCC"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import requests as req\n", "from bs4 import BeautifulSoup as bs\n", "import opencc\n", "from urllib.parse import quote, unquote\n", "import re, os\n", "\n", "# OpenCC\n", "converter = opencc.OpenCC('s2tw.json')\n", "\n", "# 建立存放資料的資料夾\n", "path_folder = 'wikisource'\n", "if not os.path.exists(path_folder):\n", "    os.makedirs(path_folder)\n", "\n", "# 網頁來源的前綴\n", "prefix = 'https://zh.wikisource.org'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = pd.read_csv('./wikisource.csv')\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 存放內文的 list\n", "li_content = []\n", "\n", "# 逐一取得內文\n", "for idx, row in df.iterrows():\n", "    # 請求網頁內容\n", "    url = row['網址']\n", "    res = req.get(url, timeout=10)\n", "\n", "    # 解析網頁內容\n", "    soup = bs(res.text, 'lxml')\n", "\n", "    # 取得元素\n", "    content_element = soup.select_one('div#mw-content-text')\n", "\n", "    # 檢視內文\n", "    # print( converter.convert(content.get_text()) )\n", "    \n", "    # 取得內文，並轉為繁體中文\n", "    content = converter.convert(content_element.get_text())\n", "    \n", "    # 加入 list，之後整合到 DataFrame\n", "    li_content.append(content)\n", "\n", "    # 取得分類連結\n", "    # alinks = soup.select('div#catlinks ul li a')\n", "    # if len(alinks) > 0:\n", "    #     for a in alinks:\n", "    #         print(f'{a.get_text()}: {prefix}{unquote(a[\"href\"])}')\n", "\n", "\n", "\n", "# 將內文加入 DataFrame\n", "df['內文'] = li_content"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["# 儲存成 Excel 檔\n", "df.to_excel(f'{path_folder}/wikisource.xlsx', index=False)"]}], "metadata": {"kernelspec": {"display_name": "test", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 2}