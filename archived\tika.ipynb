{"cells": [{"cell_type": "code", "execution_count": null, "id": "6f2ab3a6", "metadata": {}, "outputs": [], "source": ["# 安裝 tika\n", "!pip install -U tika"]}, {"cell_type": "code", "execution_count": null, "id": "db754896", "metadata": {"scrolled": false}, "outputs": [], "source": ["# 匯入套件\n", "from tika import parser\n", "from pprint import pprint\n", "\n", "# 開啟 pdf 檔案\n", "parsed_pdf = parser.from_file(\"./你的檔案.pdf\")\n", "\n", "# 輸出 pdf 內容\n", "print( parsed_pdf['content'] )"]}, {"cell_type": "markdown", "id": "28d23c9e", "metadata": {}, "source": ["# 參考資料\n", "1. [Apache Tika - a content analysis toolkit](https://tika.apache.org/ \"Apache Tika - a content analysis toolkit\")\n", "2. [Parsing PDFs in Python with Tika](https://www.geeksforgeeks.org/parsing-pdfs-in-python-with-tika/ \"Parsing PDFs in Python with Tika\")\n", "3. [tika 1.24 - Apache Tika Python library](https://pypi.org/project/tika/ \"tika 1.24 - Apache Tika Python library\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}