{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["'''匯入套件'''\n", "\n", "# 操作 browser 的 API\n", "from selenium import webdriver\n", "\n", "# 處理逾時例外的工具\n", "from selenium.common.exceptions import TimeoutException\n", "\n", "# 面對動態網頁，等待某個元素出現的工具，通常與 exptected_conditions 搭配\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "\n", "# 搭配 WebDriverWait 使用，對元素狀態的一種期待條件，若條件發生，則等待結束，往下一行執行\n", "from selenium.webdriver.support import expected_conditions as EC\n", "\n", "# 期待元素出現要透過什麼方式指定，通常與 EC、WebDriverWait 一起使用\n", "from selenium.webdriver.common.by import By\n", "\n", "# 強制等待 (執行期間休息一下)\n", "from time import sleep\n", "\n", "# 整理 json 使用的工具\n", "import json\n", "\n", "# 隨機產生\n", "import random\n", "\n", "# 正規表達式\n", "import re\n", "\n", "# 數學\n", "import math\n", "\n", "# 系統資訊\n", "import sys, traceback, os\n", "\n", "\n", "\n", "\n", "'''全域變數初始化'''\n", "\n", "# 自定義搜尋語法(一定要在實際的 Web of Science 網站執行過，確定檢索過程語法無誤、核心合輯結果數跟預期一樣)\n", "arrQueryString = [\n", "    \"TI=meta-analysis\",\n", "    \"TI=evidence review\",\n", "    \"TI=systematic review\",\n", "    \"SO=Cochrane Database of Systematic Reviews\",\n", "    \"AK=meta-analysis\",\n", "    \"AK=evidence review\",\n", "    \"AK=systematic review\",\n", "    \"KP=meta-analysis\",\n", "    \"KP=evidence review\",\n", "    \"KP=systematic review\",\n", "    \"#1 OR #2 OR #3 OR #4 OR #5 OR #6 OR #7 OR #8 OR #9 OR #10\",\n", "    \"PY=2010-2020\",\n", "    \"#11 AND #12\"\n", "]\n", "\n", "# 文件類型 (留空代表不用選擇文件類型)\n", "strDocType = ''\n", "\n", "# 隨機睡眠最大時間\n", "sleepMaxSeconds = 3 \n", "\n", "# 隨機睡眠最小時間\n", "sleepMinSeconds = 2 \n", "\n", "# [ [1, 500], [501, 1000], ... ] 等下載頁數範圍\n", "arrRecords = [] \n", "\n", "# 下載資料夾 (在 unix-like 環境要用 / 這個斜線)\n", "download_path = os.getcwd() + \"\\\\\" + \"wos_records\" \n", "\n", "# chrome 執行檔路徑 (在 unix-like 環境要用 / 這個斜線)\n", "executable_path = os.getcwd() + \"\\\\\" + \"chromedriver.exe\" \n", "\n", "# screenshot 的路徑\n", "screenshot_path = os.getcwd() + \"\\\\\" + \"screenshot.png\"\n", "\n", "# Web Of Science 頁面連結\n", "urlWoS = \"http://apps.webofknowledge.com/WOS_GeneralSearch_input.do?product=WOS&search_mode=GeneralSearch\"\n", "\n", "\n", "\n", "\n", "'''自訂函式'''\n", "\n", "# 回傳隨機秒數，協助元素等待機制\n", "def _sleepRandomSeconds():\n", "    sleep( random.randint(sleepMinSeconds, sleepMaxSeconds) )\n", "\n", "# 初始化設定\n", "def init():\n", "    #新增預設下載目錄，如果不存在，就新增資料夾\n", "    if not os.path.exists(download_path):\n", "        os.makedirs(download_path, exist_ok = True)\n", "\n", "# 點選「進階檢索」連結\n", "def _goToAdvancedSearchPage():\n", "    #前往 Web of Science 頁面\n", "    driver.get(urlWoS)\n", "    \n", "    #確認即將點選的「進階檢索」連結，所放置的網頁元素在不在\n", "    WebDriverWait(driver, 30).until(\n", "        EC.presence_of_element_located(\n", "            (By.CSS_SELECTOR, \"ul.searchtype-nav\")\n", "        )\n", "    )\n", "    \n", "    #按下「進階檢索」\n", "    li = driver.find_elements(By.CSS_SELECTOR, \"ul.searchtype-nav li\")\n", "    li[3].find_element(By.CSS_SELECTOR, 'a').click()\n", "\n", "# 整理搜尋用的字串\n", "def _setFilterCondition(strQuery):\n", "    #確認 textarea 元素是否存在\n", "    WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located(\n", "            (By.CSS_SELECTOR, \"div.AdvSearchBox textarea\")\n", "        )\n", "    )\n", "    \n", "    #清空 textarea\n", "    driver.find_element(By.CSS_SELECTOR, \"div.AdvSearchBox textarea\").clear()\n", "    \n", "    #將檢索語法的字串，放到 textarea 當中\n", "    driver.find_element(By.CSS_SELECTOR, \"div.AdvSearchBox textarea\").send_keys(strQuery)\n", "\n", "    #休閒一段時間 (依情況複製多次)\n", "    _sleepRandomSeconds()\n", "        \n", "#選擇文件類型\n", "def _setDocumentType(_type):\n", "    # 讓文件類型指定 type 的字串值來選擇 select option 元素\n", "    js_script = '''(\n", "        function () {{\n", "            let option = document.querySelector('select[name=\"value(input3)\"] option[value=\"{}\"]');\n", "            option.setAttribute('selected', true);\n", "        }}\n", "    )();\n", "    '''.format(_type)\n", "    \n", "    #執行 javsascript 語法\n", "    driver.execute_script(js_script)\n", "        \n", "# 清除編號的歷史記錄\n", "def _clearSerialNumberHistory():\n", "    #前往 WoS「進階檢索」連結的頁面\n", "    _goToAdvancedSearchPage()\n", "\n", "    #等待「進階搜尋」的連結出現，再按下該連結\n", "    WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located(\n", "            (By.CSS_SELECTOR, \"div.AdvSearchBox textarea\")\n", "        )\n", "    )\n", "    \n", "    #頁面快照\n", "    _screenshot()\n", "\n", "    #清空 textarea (輸入檢索語法的那個文字欄位)\n", "    driver.find_element(By.CSS_SELECTOR, \"div.AdvSearchBox textarea\").clear()\n", "    \n", "    #按下 History 表格右側「刪除檢索集」下面的「全選」\n", "    driver.find_element(By.CSS_SELECTOR, \"button#selectallTop\").click()\n", "    \n", "    #按下 History 表格右側「刪除檢索集」下面的「刪除」\n", "    driver.find_element(By.CSS_SELECTOR, \"button#deleteTop\").click()\n", "\n", "# 前往檢索結果的連結\n", "def _goToSearchResultPage():\n", "    #判斷查詢後歷史記錄編號，是否低於最高檢索數\n", "    objIndexNum = driver.find_elements(By.CSS_SELECTOR, \"table tbody tr[id^='set_'] td div.historyResults\")\n", "    \n", "    #取得當前檢索結果的小計\n", "    div = driver.find_element(By.CSS_SELECTOR, \"table tbody tr[id^='set_\" + str(len(objIndexNum)) + \"'] td div.historyResults\")\n", "    numResult = div.text\n", "    numResult = int(re.sub(r\"\\s|,\", \"\", numResult))\n", "    \n", "    #檢索小計除以500，例如 2819 / 500 = 5.638\n", "    _set = math.floor(numResult / 500)\n", "    \n", "    #組數除完的餘數，用來加在最後一組\n", "    _remainder = numResult % 500\n", "    \n", "    #建立例如 [ [1,500], [501, 1000], [1001, 1500], [1501, 2000], [2001, 2500] , [2501, 2819] ]\n", "    for i in range(0, _set + 1):\n", "        if i == _set:\n", "            arrRecords.append([i * 500 + 1, (i * 500) + _remainder])\n", "        else:\n", "            arrRecords.append([i * 500 + 1, (i + 1) * 500])\n", "    \n", "    print(f\"Web Of Science 核心合輯結果數: {numResult}\")\n", "    \n", "    print(\"數量切割結果(每 500 筆一組):\")\n", "    print(arrRecords)\n", "    \n", "    #按下檢索小計的連結，例如 #6\n", "    div.find_element(By.CSS_SELECTOR, \"a\").click()\n", "        \n", "# 下載期刊資訊\n", "def _downloadJournalPlaneTextFile():\n", "    #用來確認是否按過 Export 鈕（之後會變成其它按鈕元素，所以會用 More 來按）\n", "    firstExportFlag = False\n", "    \n", "    #按下匯出鈕，跳出選單(網頁上有重複的元素，例如表格頭尾都有匯出鈕，這裡選擇一個來按)\n", "    WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located(\n", "            (By.CSS_SELECTOR, \"button#exportTypeName\")\n", "        )\n", "    )\n", "    buttonsExport = driver.find_elements(By.CSS_SELECTOR, \"button#exportTypeName\")\n", "    buttonsExport[0].click()\n", "\n", "    #走訪選單連結文字，找到合適字串，就點按該連結，並跳出迴圈\n", "    li_elms = driver.find_elements(By.CSS_SELECTOR, \"ul#saveToMenu li.subnav-item\")\n", "    for li in li_elms:\n", "        listSubString = ['Other File Formats', '其他檔案格式']\n", "        subString = li.find_element(By.CSS_SELECTOR, \"a\").get_attribute('innerText')\n", "        if any( subString in s for s in listSubString ):\n", "            li.find_element(By.CSS_SELECTOR, \"a\").click()\n", "            break\n", "\n", "    for i in range(0, len(arrRecords)):\n", "        #確認是否執行第一次匯出，已匯出，之後都按 More 按鈕\n", "        if firstExportFlag == True:\n", "            #按下 More 鈕，跳出選單(網頁上有重複的元素，例如表格頭尾都有匯出鈕，這裡選擇一個來按)\n", "            buttonsExportMore = driver.find_elements(By.CSS_SELECTOR, \"button#exportMoreOptions\")\n", "            buttonsExportMore[0].click()\n", "\n", "            #走訪選單連結文字，找到合適字串，就點按該連結，並跳出迴圈\n", "            li_elms = driver.find_elements(By.CSS_SELECTOR, \"ul#saveToMenu li.subnav-item\")\n", "            for li in li_elms:\n", "                listSubString = ['Other File Formats', '其他檔案格式']\n", "                subString = li.find_element(By.CSS_SELECTOR, \"a\").get_attribute('innerText')\n", "                if any( subString in s for s in listSubString ):\n", "                    li.find_element(By.CSS_SELECTOR, \"a\").click()\n", "                    break\n", "        \n", "        #選擇匯出檔案的資料筆數(Records)，一次不能超過 500 筆\n", "        driver.find_element(By.CSS_SELECTOR, 'input#numberOfRecordsRange[type=\"radio\"]').click()\n", "        driver.find_element(By.CSS_SELECTOR, 'input#markFrom[type=\"text\"]').clear()\n", "        driver.find_element(By.CSS_SELECTOR, 'input#markFrom[type=\"text\"]').send_keys(arrRecords[i][0])\n", "        driver.find_element(By.CSS_SELECTOR, 'input#markTo[type=\"text\"]').clear()\n", "        driver.find_element(By.CSS_SELECTOR, 'input#markTo[type=\"text\"]').send_keys(arrRecords[i][1])\n", "\n", "        #選擇「記錄內容」\n", "        selectBibFieldsOptions = driver.find_elements(By.CSS_SELECTOR, \"select#bib_fields option\")\n", "        selectBibFieldsOptions[3].click()\n", "\n", "        #選擇「檔案格式」\n", "        selectSaveOptions = driver.find_elements(By.CSS_SELECTOR, \"select#saveOptions option\")\n", "        selectSaveOptions[3].click()\n", "\n", "        #按下匯出鈕，此時等待下載，直到開始下載，才會往程式下一行執行\n", "        driver.find_element(By.CSS_SELECTOR, \"button#exportButton\").click()\n", "        \n", "        #休閒一段時間 (依情況複製多次，網路不穩或速度慢，影響下載，可以多複製幾個)\n", "        _sleepRandomSeconds()\n", "        _sleepRandomSeconds()\n", "        \n", "        #第一次匯出已完成，之後不按 Export 鈕按，改為 More 按鈕（按下後，可以選擇 Other File Formats）\n", "        firstExportFlag = True\n", "        \n", "\n", "#畫面快照\n", "def _screenshot():    \n", "    #對 History 表格元素進行快照\n", "    elms_table = driver.find_elements(By.CSS_SELECTOR, 'table[width=\"100%\"]')\n", "    elms_table[8].screenshot(screenshot_path)\n", "    \n", "# 主程式\n", "def main():\n", "    #點選「進階檢索」連結\n", "    _goToAdvancedSearchPage()\n", "\n", "    #迭代自訂期刊\n", "    for i in range(0, len(arrQueryString)):\n", "        #整理搜尋用的字串\n", "        _setFilterCondition(arrQueryString[i])\n", "\n", "        #選擇文件類型（目前設定 Article）\n", "        if strDocType != '':\n", "            _setDocumentType('Article')\n", "\n", "        #按下搜尋\n", "        driver.find_element(By.CSS_SELECTOR, \"button#search-button\").click()\n", "\n", "        #若是查詢到最後，產生新的 #編號，則按下 #編號旁邊的數字超連結，進入下載階段\n", "        if (i + 1) == len(arrQueryString):\n", "            '''\n", "            查詢 len(arrQueryString) 個語法，並取得最後查詢的期刊總數，再進入期刊總數的連結\n", "            '''\n", "            #前往檢索結果的連結\n", "            _goToSearchResultPage()\n", "\n", "            #迭代下載資料(這邊會執行比較久)\n", "            _downloadJournalPlaneTextFile()\n", "\n", "            #刪除搜尋的歷史記錄\n", "            _clearSerialNumberHistory()\n", "                \n", "#關閉 chrome\n", "def close():\n", "    driver.quit()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": false}, "outputs": [], "source": ["# 啟動瀏覽器工具的選項\n", "options = webdriver.ChromeOptions()\n", "# options.add_argument(\"--headless\")                #不開啟實體瀏覽器，在背景執行\n", "options.add_argument(\"--start-maximized\")         #最大化視窗\n", "options.add_argument(\"--incognito\")               #開啟無痕模式\n", "options.add_argument(\"--disable-popup-blocking \") #禁用彈出攔截\n", "options.add_argument(\"--disable-notifications\")   #取消通知\n", "options.add_experimental_option(\"prefs\", {\n", "  \"download.default_directory\": download_path #預設下載路徑\n", "})\n", "\n", "# 使用 Chrome 的 WebDriver (含 options)\n", "driver = webdriver.Chrome( \n", "    options = options, #啟動瀏覽器工具的選項\n", "    executable_path = executable_path #chrome driver 主程式放置的路徑\n", ")\n", "\n", "# 主程式\n", "if __name__ == '__main__':\n", "    init()\n", "    main()\n", "    close()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}}, "nbformat": 4, "nbformat_minor": 4}