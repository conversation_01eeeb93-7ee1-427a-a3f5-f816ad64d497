{"cells": [{"cell_type": "code", "execution_count": null, "id": "0bfbdca0", "metadata": {}, "outputs": [], "source": ["'''匯入套件'''\n", "import json, os, pprint, time, re\n", "from urllib import parse\n", "import requests as req\n", "from bs4 import BeautifulSoup as bs\n", "\n", "# 隨機取得 User-Agent\n", "'''\n", "參考連結:\n", "https://pypi.org/project/fake-useragent/\n", "'''\n", "from fake_useragent import UserAgent\n", "ua = UserAgent()\n", "\n", "'''放置 金庸小說 metadata 的資訊'''\n", "listData = []\n", "\n", "'''小庸小說的網址'''\n", "url = 'https://www.bookwormzz.com'\n", "suffix = '/ky'\n", "\n", "'''設定標頭'''\n", "headers = {\n", "    'user-agent': ua.random\n", "}\n", "\n", "# 沒有放置 txt 檔的資料夾，就建立起來\n", "folderPath = 'jinyong'\n", "if not os.path.exists(folderPath):\n", "    os.makedirs(folderPath)"]}, {"cell_type": "code", "execution_count": null, "id": "9d0f8199", "metadata": {}, "outputs": [], "source": ["# 取得小說的主要連結\n", "def getMainLinks():\n", "    # 走訪首頁\n", "    res = req.get(url + suffix, headers = headers)\n", "    soup = bs(res.text, \"lxml\")\n", "    \n", "    # 取得主要連結\n", "    a_elms = soup.select('a[data-ajax=\"false\"]')\n", "    \n", "    # 整理主要連結資訊\n", "    for a in a_elms:\n", "        listData.append({\n", "            \"title\": a.get_text(),\n", "            \"link\": url + parse.unquote( a['href'].replace('..', '') ) + '#book_toc',\n", "            \"sub\": [] # 為了放置各個章回小說的內頁資料，下一個步驟會用到\n", "        })\n", "\n", "# 取得所有小說的獨立連結\n", "def getSubLinks():\n", "    for i in range( len(listData) ):\n", "        # 走訪章回小說內頁\n", "        res = req.get(listData[i]['link'], headers = headers, allow_redirects = False)\n", "        soup = bs(res.text, \"lxml\")\n", "        a_elms = soup.select('div[data-theme=\"b\"][data-content-theme=\"c\"] a[rel=\"external\"]')\n", "        \n", "        # 若是走訪網頁時，選擇不到特定的元素，視為沒有資料，continue 到 for 的下一個 index 去\n", "        if len(a_elms) > 0:\n", "            for a in a_elms:\n", "                listData[i]['sub'].append({\n", "                    \"sub_title\": a.get_text(),\n", "                    \"sub_link\": url + parse.unquote( a['href'] )\n", "                })\n", "        else:\n", "            continue\n", "\n", "# 建立金庸小說的 json 檔\n", "def save<PERSON><PERSON>():\n", "    with open(f\"{folderPath}/jinyong.json\", \"w\", encoding=\"utf-8\") as file:\n", "        file.write( json.dumps(listData, ensure_ascii=False, indent=4) )\n", "\n", "# 將金庸小說所有章回的內容，各自寫到 txt 與 json 中\n", "def writeTxt():\n", "    # 稍候建立 train.json 前的程式變數\n", "    listContent = []\n", "\n", "    # 開啟 金庸小說 metadata 的 json 檔\n", "    with open(f\"{folderPath}/jinyong.json\", \"r\", encoding=\"utf-8\") as file:\n", "        strJson = file.read()\n", "\n", "    # 走訪所有章回的小說文字內容\n", "    listResult = json.loads(strJson)\n", "    for i in range(len(listResult)):\n", "        for j in range(len(listResult[i]['sub'])):\n", "            # 取得回應\n", "            res = req.get(listResult[i]['sub'][j]['sub_link'], headers = headers, allow_redirects=False)\n", "            \n", "            # 先將 <br> 改成 換行符號\n", "            strContent = re.sub(r\"<br\\s?\\/?>\", '\\n', str(res.text))\n", "            \n", "            # 建立 soup 物件\n", "            soup = bs(strContent, \"lxml\")\n", "\n", "            # 去掉內文標題後，取得內文\n", "            div = soup.select_one('div#html > div')\n", "            strContent = div.get_text()\n", "            \n", "            # 去除不必要的文字\n", "            # strContent = re.sub(r\" |\\r|\\n|　|\\s\", '', strContent)\n", "\n", "            # 決定 txt 的檔案名稱\n", "            fileName = f\"{listResult[i]['title']}_{listResult[i]['sub'][j]['sub_title']}.txt\"\n", "            \n", "            # 將小說內容存到 txt 中\n", "            with open(f\"{folderPath}/{fileName}\", \"w\", encoding=\"utf-8\") as file:\n", "                file.write(strContent)\n", "                \n", "            # 去除不必要的文字\n", "            strContent = re.sub(r\" |\\r|\\n|　|\\s\", '', strContent)\n", "\n", "            # 額外將小說內容放到 list 當中，建立 train.json\n", "            listContent.append(strContent)\n", "\n", "    # 延伸之後的教學，在此建立訓練資料\n", "    with open(f\"{folderPath}/train.json\", \"w\", encoding=\"utf-8\") as file:\n", "        file.write( json.dumps(listContent, ensure_ascii=False, indent=4) )"]}, {"cell_type": "code", "execution_count": null, "id": "917b963a", "metadata": {}, "outputs": [], "source": ["# 主程式\n", "if __name__ == \"__main__\":\n", "    time1 = time.time()\n", "    getMainLinks()\n", "    getSubLinks()\n", "    <PERSON><PERSON><PERSON>()\n", "    writeTxt()\n", "    print(f\"執行總花費時間: {time.time() - time1}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}, "vscode": {"interpreter": {"hash": "585a938ec471c889bf0cce0aed741a99eaf47ca09c0fa8393793bc5bfe77ba11"}}}, "nbformat": 4, "nbformat_minor": 5}