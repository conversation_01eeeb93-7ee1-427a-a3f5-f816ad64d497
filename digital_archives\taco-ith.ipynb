{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["'''\n", "匯入套件\n", "'''\n", "# 操作 browser 的 API\n", "from selenium import webdriver\n", "\n", "# 處理逾時例外的工具\n", "from selenium.common.exceptions import TimeoutException\n", "\n", "# 面對動態網頁，等待某個元素出現的工具，通常與 exptected_conditions 搭配\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "\n", "# 搭配 WebDriverWait 使用，對元素狀態的一種期待條件，若條件發生，則等待結束，往下一行執行\n", "from selenium.webdriver.support import expected_conditions as EC\n", "\n", "# 期待元素出現要透過什麼方式指定，通常與 EC、WebDriverWait 一起使用\n", "from selenium.webdriver.common.by import By\n", "\n", "# 強制等待 (執行期間休息一下)\n", "from time import sleep\n", "\n", "# 整理 json 使用的工具\n", "import json\n", "\n", "# 執行 command 的時候用的\n", "import os\n", "\n", "# 子處理程序，用來取代 os.system 的功能\n", "import subprocess\n", "\n", "# 取得隨機數\n", "import random\n", "\n", "from urllib.parse import quote, unquote\n", "from pprint import pprint\n", "from time import sleep\n", "from random import randint\n", "import json\n", "import pandas as pd\n", "\n", "# 啟動瀏覽器工具的選項\n", "my_options = webdriver.ChromeOptions()\n", "my_options.add_argument(\"--start-maximized\") #最大化視窗\n", "\n", "# 使用 Chrome 的 WebDriver\n", "driver = webdriver.Chrome(\n", "    options = my_options,\n", ")\n", "\n", "# 建立儲存圖片、影片的資料夾\n", "folderPath = 'taco-ith'\n", "if not os.path.exists(folderPath):\n", "    os.makedirs(folderPath)\n", "\n", "# 放置爬取的資料\n", "list_data = []"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["def visit():\n", "    # 瀏覽器打開爬取頁面\n", "    url = 'https://taco.ith.sinica.edu.tw/tdk/index.php?title=特殊:用戶登錄&returnto=熱蘭遮城日誌'\n", "    driver.get(url)\n", "\n", "def login():\n", "    driver.find_element(By.CSS_SELECTOR, '#wpName1').send_keys('帳號')\n", "    driver.find_element(By.CSS_SELECTOR, '#wpPassword1').send_keys('密碼')\n", "    driver.find_element(By.CSS_SELECTOR, '#wpLoginAttempt').click()\n", "\n", "def homepage():\n", "    url = 'https://taco.ith.sinica.edu.tw/tdk/熱蘭遮城日誌'\n", "    driver.get(url)\n", "\n", "def get_main_links():\n", "    a_elms = driver.find_elements(By.CSS_SELECTOR, 'div#mw-content-text > big > table[cellspacing][cellpadding] a[href][title]')\n", "    for a in a_elms:\n", "        list_data.append({\n", "            'title': a.get_attribute('title'),\n", "            'href': a.get_attribute('href'),\n", "        })\n", "\n", "def get_subpage_links():\n", "    for i, d in enumerate(list_data):\n", "        sleep(randint(1,4))\n", "        driver.get(d['href'])\n", "        a_elms = driver.find_elements(By.CSS_SELECTOR, 'div#mw-content-text big > ul > li > a[href][title]')\n", "        list_data[i]['subpages'] = []\n", "        for a in a_elms:\n", "            list_data[i]['subpages'].append({\n", "                'title': a.get_attribute('title'),\n", "                'href': a.get_attribute('href'),\n", "            })\n", "\n", "    with open(f\"{folderPath}/taco-ith.json\", \"w\", encoding=\"utf8\") as f:\n", "        json.dump(list_data, f, ensure_ascii=False, indent=4)\n", "\n", "\n", "def get_detail_links():\n", "    with open(f\"{folderPath}/taco-ith.json\", \"r\", encoding=\"utf8\") as f:\n", "        list_data = json.load(f)\n", "\n", "    for i, d in enumerate(list_data):\n", "        for j, subpage in enumerate(d['subpages']):\n", "            sleep(randint(1,4))\n", "            driver.get(subpage['href'])\n", "            a_elms = driver.find_elements(By.CSS_SELECTOR, 'div#mw-content-text big > ul > li > a[href][title]')\n", "            list_data[i]['subpages'][j]['details'] = []\n", "            for a in a_elms:\n", "                list_data[i]['subpages'][j]['details'].append({\n", "                    'title': a.get_attribute('title'),\n", "                    'href': a.get_attribute('href'),\n", "                })\n", "\n", "    with open(f\"{folderPath}/taco-ith.json\", \"w\", encoding=\"utf8\") as f:\n", "        json.dump(list_data, f, ensure_ascii=False, indent=4)\n", "\n", "def get_content():\n", "    with open(f\"{folderPath}/taco-ith.json\", \"r\", encoding=\"utf8\") as f:\n", "        list_data = json.load(f)\n", "\n", "    list_records = []\n", "    count = 0\n", "    columns = ['main_title', 'sub_title', 'detail_title', 'detail_href', 'content', 'references']\n", "\n", "    for i, d in enumerate(list_data):\n", "        for j, subpage in enumerate(d['subpages']):\n", "            for k, detail in enumerate(subpage['details']):\n", "                count += 1\n", "                # sleep(0.2)\n", "                driver.get(detail['href'])\n", "                content = driver.find_element(By.CSS_SELECTOR, 'div#mw-content-text table[width] td[class=itharticle]').get_attribute('innerText')\n", "                references = []\n", "                if len(driver.find_elements(By.CSS_SELECTOR, 'div#mw-content-text > ol[class=references]')) > 0:\n", "                    for span in driver.find_elements(By.CSS_SELECTOR, 'div#mw-content-text > ol[class=references] > li[id^=cite] > span[class=reference-text]'):\n", "                        references.append(span.get_attribute('innerText'))\n", "                list_records.append([\n", "                    list_data[i]['title'],\n", "                    list_data[i]['subpages'][j]['title'],\n", "                    list_data[i]['subpages'][j]['details'][k]['title'],\n", "                    list_data[i]['subpages'][j]['details'][k]['href'],\n", "                    content,\n", "                    '\\n'.join(references)\n", "                ])\n", "            \n", "        print(\"=\" * 80)\n", "        if not os.path.exists(f\"{folderPath}/taco-ith.xlsx\"):\n", "            df = pd.DataFrame(list_records, columns=columns)\n", "            df.to_excel(f\"{folderPath}/taco-ith.xlsx\", index=False)\n", "            del df\n", "            print('建立 taco-ith.xlsx')\n", "        else:\n", "            df = pd.read_excel(f\"{folderPath}/taco-ith.xlsx\")\n", "            df2 = pd.DataFrame(list_records, columns=columns)\n", "            df = pd.concat([df, df2], ignore_index=True)\n", "            df.to_excel(f\"{folderPath}/taco-ith.xlsx\", index=False)\n", "            del df, df2\n", "            print('更新 taco-ith.xlsx')\n", "        \n", "        list_records.clear()\n", "\n", "    print(\"=\" * 80)\n", "    print(count)\n", "\n", "def close():\n", "    driver.quit()\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["visit()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["login()"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["homepage()"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["get_main_links()"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["get_subpage_links()"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["get_detail_links()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================================================================\n", "建立 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "更新 taco-ith.xlsx\n", "已寫入 taco-ith.xlsx\n", "================================================================================\n", "5443\n"]}], "source": ["get_content()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["close()"]}], "metadata": {"kernelspec": {"display_name": "test", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 2}