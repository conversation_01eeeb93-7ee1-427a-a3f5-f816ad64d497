{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install openpyxl"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from openpyxl import load_workbook\n", "from openpyxl import Workbook\n", "\n", "# 動態新增檔案\n", "# workbook = Workbook()\n", "# worksheet = workbook.create_sheet(\"students\", 0)\n", "\n", "# 讀取已存在的 excel 檔案\n", "importFile = \"read.xlsx\"\n", "workbook = load_workbook(filename = importFile)\n", "\n", "# 顯示所有工作表\n", "print(workbook.sheetnames)\n", "\n", "# 取得主要的 sheet\n", "worksheet = workbook['students']\n", "\n", "# 新增標題\n", "worksheet['C1'] = 'phone_number'\n", "\n", "# 新增資料\n", "worksheet['C2'] = \"0911111111\"\n", "worksheet['C3'] = \"0922222222\"\n", "worksheet['C4'] = \"0933333333\"\n", "worksheet['C5'] = \"0944444444\"\n", "\n", "# 自訂學生清單 dictionaries in list\n", "listStudents = [\n", "    {\"name\": \"<PERSON>\", \"age\": 48, \"phone_number\": \"0955555555\"},\n", "    {\"name\": \"<PERSON>\", \"age\": 27, \"phone_number\": \"0966666666\"},\n", "]\n", "\n", "# 將學生清單寫入 excel (各別寫入名單的尾端)\n", "position = 6\n", "for student in listStudents:\n", "    worksheet['A' + str(position)] = student[\"name\"]\n", "    worksheet['B' + str(position)] = student[\"age\"]\n", "    worksheet['C' + str(position)] = student[\"phone_number\"]\n", "    position += 1\n", "\n", "# 儲存 workbook\n", "exportFile = \"read_export.xlsx\"\n", "workbook.save(exportFile)\n", "\n", "# 關閉 workbook\n", "workbook.close()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}}, "nbformat": 4, "nbformat_minor": 4}