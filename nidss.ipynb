{"cells": [{"cell_type": "markdown", "id": "d53b0f01-a3a3-4c6d-b5de-57e1398aef8b", "metadata": {}, "source": ["# Selenium 版本"]}, {"cell_type": "code", "execution_count": null, "id": "4283f4bb", "metadata": {}, "outputs": [], "source": ["import os\n", "from time import sleep\n", "from selenium import webdriver\n", "from selenium.webdriver.common.by import By"]}, {"cell_type": "code", "execution_count": 2, "id": "0ecf9320-4070-42cd-b736-48eaf27af845", "metadata": {}, "outputs": [], "source": ["# 新增資料夾\n", "folderName = 'nidss'\n", "folderPath = f'./{folderName}'\n", "if not os.path.exists(folderPath):\n", "    os.makedirs(folderPath)\n", "\n", "# 設定下載檔案的路徑 (覆寫 Chrome 預設下載路徑)\n", "fullDownloadPath = os.getcwd() + '\\\\' + folderName\n", "\n", "# Chrome 設定\n", "my_options = webdriver.ChromeOptions()\n", "my_options.add_argument(\"--start-maximized\")\n", "my_options.add_experimental_option(\"prefs\", {\n", "    \"download.default_directory\": fullDownloadPath,\n", "    \"download.prompt_for_download\": <PERSON>als<PERSON>,\n", "    \"download.directory_upgrade\": True,\n", "    \"safebrowsing_for_trusted_sources_enabled\": False,\n", "    \"safebrowsing.enabled\": <PERSON><PERSON><PERSON>,\n", "    \"plugins.always_open_pdf_externally\": True\n", "})\n", "\n", "# 開啟自動化工具(瀏覽器)\n", "driver = webdriver.Chrome(options = my_options)"]}, {"cell_type": "code", "execution_count": 3, "id": "ea960884-aecf-4cdf-a2f6-2fb0f7fafaf1", "metadata": {}, "outputs": [], "source": ["# 全國 嚴重特殊傳染性肺炎(112/3/19以前病例定義版本)\n", "driver.get(\"https://nidss.cdc.gov.tw/nndss/disease?id=19CoV\")"]}, {"cell_type": "code", "execution_count": 4, "id": "e7cf8caf-29af-43eb-a2d6-4d3fd0952a4c", "metadata": {}, "outputs": [], "source": ["# 點選下載圖示\n", "driver.find_element(By.CSS_SELECTOR, \"g.highcharts-exporting-group\").click()\n", "\n", "# 等待 2 秒\n", "sleep(2)\n", "\n", "# 按下 Download CSV，會自動下載到覆寫後的 Chrome 下載路徑\n", "driver.find_elements(By.CSS_SELECTOR, \"li.highcharts-menu-item\")[6].click()"]}, {"cell_type": "code", "execution_count": 5, "id": "d91faa21-e533-4489-9363-0014adb0f083", "metadata": {}, "outputs": [], "source": ["# 關閉瀏覽器\n", "driver.quit()"]}, {"cell_type": "markdown", "id": "1c880e1d", "metadata": {}, "source": ["# Requests 版本"]}, {"cell_type": "code", "execution_count": 2, "id": "48c74fc6", "metadata": {}, "outputs": [], "source": ["import requests as req\n", "import os, json, re\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 3, "id": "31799492", "metadata": {}, "outputs": [], "source": ["# 新增資料夾\n", "folderPath = f'./nidss'\n", "if not os.path.exists(folderPath):\n", "    os.makedirs(folderPath)"]}, {"cell_type": "code", "execution_count": 4, "id": "b1b348c5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["381\n", "381\n"]}, {"data": {"image/png": "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******************************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*********************************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", "text/plain": ["<Figure size 5000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 發出請求，取得 html\n", "url = \"https://nidss.cdc.gov.tw/nndss/disease?id=19CoV\"\n", "res = req.get(url=url)\n", "\n", "# 放置 json 字串的變數\n", "str_json = \"\"\n", "\n", "# 過濾出來需要的資料\n", "regex = r\"hcJson\\.push\\((.+)\\)\"\n", "match = re.search(regex, res.text)\n", "if match != None:\n", "    str_json = match[1]\n", "\n", "# 儲存 json\n", "with open(f\"{folderPath}/nidss.json\", \"w\", encoding=\"utf-8\") as file:\n", "    file.write(str_json)\n", "\n", "# 將 json 轉成變數\n", "dict_json = json.loads(str_json)\n", "\n", "# 取得日期 (可以作為標題)\n", "list_dates = dict_json['xAxis_categories']\n", "\n", "# 取得確診數量 (對應 日期)\n", "list_cases = dict_json['series'][0]['data']\n", "\n", "# 確認資料數量是否一置\n", "print(len(list_dates))\n", "print(len(list_cases))\n", "\n", "# 建立圖表\n", "plt.figure(figsize=(50, 6)) # 寬度 50 inches 和 高度 6 inches \n", "plt.bar(list_dates, list_cases, 0.3)\n", "plt.title(\"19CoV\")\n", "plt.xlabel(\"Dates\")\n", "plt.ylabel(\"Cases\")\n", "plt.xticks(rotation=90)\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python3@dip", "language": "python", "name": "dip"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 5}