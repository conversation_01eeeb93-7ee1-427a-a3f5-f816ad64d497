<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>測試 LLM 串流傳輸效果</title>
    <style>
        div.chatroom { position: fixed; bottom: 0; width: 95%; padding-bottom: 10px;}
        div.chatroom button { width: 100px; background: rgb(154, 158, 159); border: none; padding: 10px; }
        div.chatroom ul#msg { list-style-type: none; margin: 0; padding: 20px; }
        div.chatroom ul#msg li { padding: 5px; font-family: 'Times New Roman', Times, serif; font-size: 20px; }
        textarea#txt { width: 100%; height: 100px; font-family: 'Times New Roman', Times, serif; font-size: 20px; }
    </style>
</head>
<body>
    <div class="chatroom">
        <!-- 放置互動訊息 -->
        <ul id="msg"></ul>

        <!-- 輸入範例: 請以 python 撰寫 9 9 乘法表，只要告訴我範例程式碼就好，不用詳細說明。 -->
        <textarea id="txt" placeholder="請輸入您的訊息"></textarea>
        <br>
        <button type="submit" id="smb">送出</button>
    </div>

    <!-- 引入 marked.js：這是一個 markdown 轉換工具，可以將 markdown 轉換成 HTML -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>

    <!-- 透過自訂 JavaScript 來處理互動訊息 -->
    <script>
        // 取得網頁元素
        const msg = document.querySelector('ul#msg');
        const smb = document.querySelector('button#smb');
        const txt = document.querySelector('textarea#txt');
        
        // sn 用來標記每一個 li 的 id
        let sn = 0;

        // 當點擊送出鈕、將表單送出時，觸發事件
        smb.addEventListener("click", async (e) => {
            // 防止表單預設送出行為
            e.preventDefault();

            // 暫時取消送出鈕功能，以免重複送出
            smb.disabled = true;

            // 在 ul 當中額外新增一個 li，放置 ai 生成的內容
            msg.innerHTML += `<li"><div>${txt.value}</div></li>`;

            // 送出請求的內容 (使用者的輸入)
            let data = txt.value;

            // 清空使用者輸入的文字 (optional)
            txt.value = '';

            // 發送 POST 請求
            const response = await fetch("http://127.0.0.1:5000/chat", {
                method: "POST",
                headers: {"Content-Type": "application/json"}, //透過 request headers，告訴後端我們傳送的資料類型為 JSON
                body: JSON.stringify({
                    session_id: "sess_55663312", //這裡可以自訂，包括數字也可以亂寫，sess_ 開頭代表這是 session id，但不一定要這樣命名
                    message: data //使用者輸入的訊息
                })
            });

            // 讀取 response body，getReader() 會回傳一個 ReadableStreamDefaultReader 物件
            const reader = response.body.getReader();

            // output 用來儲存 response body 的內容
            let output = "";

            // 在 ul 當中額外新增一個 li，放置 ai 生成的內容
            msg.innerHTML += `<li"><div id="sn_${sn}"></div></li>`;

            // 透過 while 迴圈，持續讀取 response body 的內容
            while (true) {
                // reader.read() 會回傳一個 promise，當讀取完成時，會回傳一個物件，包含 done 和 value 兩個屬性
                const { done, value } = await reader.read();

                // 將讀取到的 value 轉換成文字，並加到 output 變數中
                output += new TextDecoder().decode(value);

                // 將 response body 的內容轉換成 HTML，並顯示在網頁上
                document.querySelector(`div#sn_${sn}`).innerHTML = marked.parse(output);

                // 如果讀取完成，則跳出迴圈
                if (done) {
                    // 遞增 sn 變數，用來標記每一個 li 的 id
                    sn++;

                    // 將送出鈕功能恢復
                    smb.disabled = false;

                    // 透過 return 來結束迴圈。這是一種特殊的 return，可以結束 async function 的執行
                    return;
                }
            }

        })
    </script>
</body>
</html>