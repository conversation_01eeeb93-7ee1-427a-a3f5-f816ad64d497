{"cells": [{"cell_type": "code", "execution_count": 27, "id": "8f7c1fdd", "metadata": {"scrolled": false}, "outputs": [], "source": ["'''\n", "臺灣大學學術期刊資料庫\n", "http://ejournal.press.ntu.edu.tw/index.php\n", "'''\n", "\n", "'''\n", "匯入套件\n", "'''\n", "# 操作 browser 的 API\n", "from selenium import webdriver\n", "\n", "# 處理逾時例外的工具\n", "from selenium.common.exceptions import TimeoutException\n", "\n", "# 面對動態網頁，等待某個元素出現的工具，通常與 exptected_conditions 搭配\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "\n", "# 搭配 WebDriverWait 使用，對元素狀態的一種期待條件，若條件發生，則等待結束，往下一行執行\n", "from selenium.webdriver.support import expected_conditions as EC\n", "\n", "# 期待元素出現要透過什麼方式指定，通常與 EC、WebDriverWait 一起使用\n", "from selenium.webdriver.common.by import By\n", "\n", "# 處理下拉式選單的工具\n", "from selenium.webdriver.support.ui import Select\n", "\n", "# 加入行為鍊 ActionChain (在 WebDriver 中模擬滑鼠移動、點繫、拖曳、按右鍵出現選單，以及鍵盤輸入文字、按下鍵盤上的按鈕等)\n", "from selenium.webdriver.common.action_chains import ActionChains\n", "\n", "# 取得系統時間的工具\n", "from datetime import datetime\n", "\n", "# 強制等待 (執行期間休息一下)\n", "from time import sleep\n", "\n", "# 整理 json 使用的工具\n", "import json\n", "\n", "# 執行 command 的時候用的\n", "import os\n", "\n", "# regular expression 工具\n", "import re\n", "\n", "# 子處理程序，用來取代 os.system 的功能\n", "import subprocess\n", "\n", "# 隨機亂數(整數)\n", "from random import randint\n", "\n", "# 格式化字串輸出\n", "from pprint import pprint as pp\n", "\n", "# 圖形使用者介面: 在電腦桌面模擬人類行為來控制滑鼠跟鍵籃\n", "import pyautogui\n", "\n", "'''\n", "[1] Selenium with Python 中文翻譯文檔\n", "參考網頁：https://selenium-python-zh.readthedocs.io/en/latest/index.html\n", "[2] selenium 啓動 Chrome 的進階配置參數\n", "參考網址：https://stackoverflow.max-everyday.com/2019/12/selenium-chrome-options/\n", "[3] Mouse Hover Action in Selenium\n", "參考網址：https://python-selenium-zh.readthedocs.io/zh_CN/latest/7.2%20%E8%A1%8C%E4%B8%BA%E9%93%BE/\n", "[4] How to select a drop-down menu value with Selenium using Python?\n", "參考網址：https://stackoverflow.com/questions/7867537/how-to-select-a-drop-down-menu-value-with-selenium-using-python\n", "[5] PyAutoGUI : 使用Python控制電腦\n", "https://yanwei-liu.medium.com/pyautogui-%E4%BD%BF%E7%94%A8python%E6%93%8D%E6%8E%A7%E9%9B%BB%E8%85%A6-662cc3b18b80\n", "'''\n", "\n", "'''\n", "設定\n", "'''\n", "# 建立儲存檔案的資料夾，不存在就新增\n", "folderPath = 'ntu_journal_db'\n", "if not os.path.exists(folderPath):\n", "    os.makedirs(folderPath)\n", "\n", "# 啟動瀏覽器工具的選項\n", "my_options = webdriver.ChromeOptions()\n", "# my_options.add_argument(\"--headless\")                #不開啟實體瀏覽器背景執行\n", "my_options.add_argument(\"--start-maximized\")         #最大化視窗\n", "my_options.add_argument(\"--incognito\")               #開啟無痕模式\n", "my_options.add_argument(\"--disable-popup-blocking\") #禁用彈出攔截\n", "my_options.add_argument(\"--disable-notifications\")  #取消通知\n", "my_options.add_argument('--user-agent=\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36\"')  #請求標頭中的使用者代理資訊\n", "\n", "# 指定 chromedriver 檔案的路徑\n", "my_executable_path = './chromedriver.exe'\n", "\n", "# Web Driver 變數\n", "driver = None\n", "\n", "# 走訪首頁\n", "url = \"http://ejournal.press.ntu.edu.tw/index.php\"\n", "\n", "# 放置資料的變數\n", "listData = []\n", "\n", "# JSON 存檔路徑\n", "json_path = \"./臺灣大學學術期刊資料庫.json\"\n", "\n", "\n", "'''程式區域'''\n", "# web driver 初始化\n", "def init():\n", "    global driver\n", "    \n", "    # 使用 Chrome 的 WebDriver\n", "    driver = webdriver.Chrome( \n", "        options = my_options, \n", "        executable_path = my_executable_path\n", "    )\n", "    \n", "# 走訪首頁\n", "def visitHomePage():\n", "    # 進入首頁\n", "    driver.get(url)\n", "    \n", "    # 按下 中文 按鈕\n", "    driver.find_element(By.CSS_SELECTOR, 'input[type=\"submit\"][value=\"中文\"]').click()\n", "\n", "# 打開所有隱藏連結\n", "def getSideBarLinks():\n", "    try:\n", "        # 等待特定元素出現\n", "        WebDriverWait(driver, 10).until(\n", "            EC.presence_of_element_located(\n", "                (By.CSS_SELECTOR, \"div.level_browse\")\n", "            )\n", "        )\n", "        \n", "        # 建立行為鍊\n", "        actions = <PERSON><PERSON><PERSON><PERSON>(driver)\n", "        \n", "        # 開啟所有子列表(尋找 a)\n", "        a_plus_elms = driver.find_elements(By.PARTIAL_LINK_TEXT, '+')\n", "        for a in a_plus_elms:\n", "            actions.click(on_element=a)\n", "        actions.perform()\n", "         \n", "        # 取得期刊號碼的超連結\n", "        a_link_elms = driver.find_elements(By.CSS_SELECTOR, 'a[href^=\"query.php\"]')\n", "        for a_link in a_link_elms:\n", "            listData.append({\n", "                \"title\": a_link.get_attribute('innerText'),\n", "                \"link\": a_link.get_attribute('href')\n", "            })\n", "\n", "    except TimeoutException:\n", "        print(\"元素讀取逾時，即將關閉瀏覽器\")\n", "        close()\n", "        \n", "# 走訪各期頁面\n", "def getMainPageLinks():     \n", "    # 取得主要頁面的 regex\n", "    regex_link = r\"https?:\\/\\/.+&lvbw=\\S\\d{5}\"\n", "    \n", "    # 取得篇數的 regex\n", "    regex_total = r\".+\\((\\d+)\\)\"\n", "    \n", "    # 預估期數與篇數\n", "    count_title = 0\n", "    count_total = 0\n", "    \n", "    for index, _dict in enumerate(listData):\n", "        # 沒有 sub 屬性，則建立，為了放置細節頁的內容\n", "        if \"sub\" not in listData[index]:\n", "            listData[index]['sub'] = []\n", "            \n", "        # 可能有 PDF 資訊的網頁連結才走訪\n", "        if re.search(regex_link, _dict['link']) != None:\n", "            # 累加期數\n", "            count_title += 1\n", "            \n", "            # 累加篇數\n", "            num = re.search(regex_total, _dict['title'])[1]\n", "            count_total += int(num)\n", "\n", "            \n", "            # 走訪每一個有 PDF 連結的頁面\n", "            driver.get(_dict['link'])\n", "\n", "            try:\n", "                # 等待特定元素出現\n", "                WebDriverWait(driver, 10).until(\n", "                    EC.presence_of_element_located(\n", "                        (By.CSS_SELECTOR, \"div.DataList\")\n", "                    )\n", "                )\n", "\n", "                # 取得所有超連結\n", "                div_elms = driver.find_elements(By.CSS_SELECTOR, \"div.DataList\")\n", "                for div in div_elms:\n", "                    # 取得放置出處、作者、電子檔等資訊的元素\n", "                    div_field_value_elms = div.find_elements(By.CSS_SELECTOR, \"div.class_area > div.field_value\")\n", "\n", "                    # 出處\n", "                    source = div_field_value_elms[0].get_attribute('innerText')\n", "\n", "                    # 作者\n", "                    author = div_field_value_elms[1].get_attribute('innerText')\n", "\n", "                    # 連結\n", "                    if len( div_field_value_elms[2].find_elements(By.CSS_SELECTOR, \"a\") ) > 0:\n", "                        link = div_field_value_elms[2].find_element(By.CSS_SELECTOR, \"a\").get_attribute('href')\n", "                    else:\n", "                        link = None\n", "\n", "                    # 整理資料\n", "                    listData[index]['sub'].append({\n", "                        \"source\": source,\n", "                        \"author\": author,\n", "                        \"link\": link\n", "                    })\n", "            except TimeoutException:\n", "                print(f\"連結[{_dict['link']}]找不到 div.DataList 元素\")\n", "                continue\n", "                \n", "            # 強制等待\n", "            sleep( randint(2,5) )\n", "            \n", "# 關閉瀏覽器\n", "def close():\n", "    driver.quit()\n", "\n", "# 儲存 JSON\n", "def save<PERSON><PERSON><PERSON><PERSON>():\n", "    global listData\n", "    with open(json_path, \"w\", encoding='utf-8') as fp:\n", "        fp.write( json.dumps(listData, ensure_ascii=False, indent=4) )\n", "\n", "# 讀取 JSON\n", "def loadJson():\n", "    global listData\n", "    with open(json_path, \"r\", encoding=\"utf-8\") as fp:\n", "        listData = json.loads( fp.read() )        \n", "    \n", "# 測試單一頁面行為鍊功能\n", "def test():\n", "    # 等待\n", "    sleep(2)\n", "    \n", "    # 進入頁面\n", "    driver.get(\"http://ejournal.press.ntu.edu.tw/query.php?Action_From=level&lvbw=F10101\")\n", "    \n", "    # 等待\n", "    sleep(2)\n", "    \n", "    # 等待特定元素出現\n", "    WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located(\n", "            (By.CSS_SELECTOR, \"div.DataList\")\n", "        )\n", "    )\n", "\n", "    # 取得所有超連結\n", "    div_elms = driver.find_elements(By.CSS_SELECTOR, \"div.DataList\")\n", "    \n", "    # 測試連結\n", "    link = ''\n", "    \n", "    for div in div_elms:\n", "        # 取得放置出處、作者、電子檔等資訊的元素\n", "        div_field_value_elms = div.find_elements(By.CSS_SELECTOR, \"div.class_area > div.field_value\")\n", "\n", "        # 出處\n", "        source = div_field_value_elms[0].get_attribute('innerText')\n", "\n", "        # 作者\n", "        author = div_field_value_elms[1].get_attribute('innerText')\n", "\n", "        # 連結\n", "        if len( div_field_value_elms[2].find_elements(By.CSS_SELECTOR, \"a\") ) > 0:\n", "            link = div_field_value_elms[2].find_element(By.CSS_SELECTOR, \"a\").get_attribute('href')\n", "        else:\n", "            link = None\n", "        break\n", "\n", "\n", "    # 進入頁面\n", "    driver.get(link)\n", "    \n", "    # 等待特定元素出現\n", "    WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located(\n", "            (By.CSS_SELECTOR, \"div#PDF_View\")\n", "        )\n", "    )\n", "    \n", "    # 建立行為鍊\n", "    actions = <PERSON><PERSON><PERSON><PERSON>(driver)\n", "    actions.move_by_offset(900, 205)\n", "    actions.pause(3)\n", "    actions.click()\n", "    actions.perform()\n", "    \n", "    sleep(3)\n", "    \n", "    # 透過 screen 環境來按下 enter\n", "    pyautogui.press('enter')\n", "    \n", "    sleep(2)\n", "    \n", "\n", "'''執行區域'''\n", "if __name__ == \"__main__\":\n", "    # 開始\n", "    init()\n", "    visitHomePage()\n", "    getSideBarLinks()\n", "    getMainPageLinks()\n", "    save<PERSON><PERSON><PERSON><PERSON>()\n", "\n", "    # 測試\n", "#     test()\n", "    \n", "    # 結束\n", "    close()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}}, "nbformat": 4, "nbformat_minor": 5}