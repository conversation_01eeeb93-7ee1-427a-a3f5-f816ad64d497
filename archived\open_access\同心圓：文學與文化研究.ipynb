{"cells": [{"cell_type": "markdown", "id": "1cc23b48", "metadata": {}, "source": ["# 套件安裝"]}, {"cell_type": "code", "execution_count": null, "id": "2766c5fd", "metadata": {}, "outputs": [], "source": ["!pip install -U openpyxl selenium beautifulsoup4 lxml requests"]}, {"cell_type": "markdown", "id": "e4e3c8fe", "metadata": {}, "source": ["# 設定初始化"]}, {"cell_type": "code", "execution_count": null, "id": "c8dce9f1", "metadata": {}, "outputs": [], "source": ["'''\n", "注意事項:\n", "下載對應的 ChromeDriver (web driver) 到程式檔案同一個目錄下後解壓縮，下載前記得對應版本編號。\n", "連結: https://chromedriver.chromium.org/downloads\n", "\n", "參考網頁:\n", "[1] Concentric: Literary and Cultural Studies（同心圓：文學與文化研究）\n", "http://www.concentric-literature.url.tw/\n", "[2] sqlite3 --- SQLite 数据库 DB-API 2.0 接口模块\n", "https://docs.python.org/zh-tw/3/library/sqlite3.html\n", "'''\n", "\n", "\n", "'''\n", "匯入套件\n", "'''\n", "# HTML parser\n", "from bs4 import BeautifulSoup as bs\n", "\n", "# 操作 browser 的 API\n", "from selenium import webdriver\n", "\n", "# 處理逾時例外的工具\n", "from selenium.common.exceptions import TimeoutException\n", "\n", "# 面對動態網頁，等待某個元素出現的工具，通常與 exptected_conditions 搭配\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "\n", "# 搭配 WebDriverWait 使用，對元素狀態的一種期待條件，若條件發生，則等待結束，往下一行執行\n", "from selenium.webdriver.support import expected_conditions as EC\n", "\n", "# 期待元素出現要透過什麼方式指定，通常與 EC、WebDriverWait 一起使用\n", "from selenium.webdriver.common.by import By\n", "\n", "# 處理下拉式選單的工具\n", "from selenium.webdriver.support.ui import Select\n", "\n", "# 強制等待 (執行期間休息一下)\n", "from time import sleep\n", "\n", "# pretty-print\n", "from pprint import pprint\n", "\n", "# 隨機\n", "from random import randint\n", "\n", "# 計時\n", "import time\n", "\n", "# 整理 json 使用的工具\n", "import json\n", "\n", "# 執行 shell command 的時候用的\n", "import os\n", "\n", "# 取得錯誤訊息\n", "import sys, traceback\n", "\n", "# 子處理程序，用來取代 os.system 的功能\n", "import subprocess\n", "\n", "# 正規表達式\n", "import re\n", "\n", "# 編碼\n", "from urllib.parse import quote\n", "\n", "# SQLite 資料庫\n", "import sqlite3\n", "\n", "# 存取 Excel 的工具\n", "from openpyxl import load_workbook\n", "from openpyxl import Workbook\n", "\n", "# 取得系統時間的工具\n", "from datetime import datetime\n", "\n", "# 引入 hashlib 模組\n", "import hashlib\n", "\n", "# 啟動瀏覽器工具的選項\n", "my_options = webdriver.ChromeOptions()\n", "# my_options.add_argument(\"--headless\")             #不開啟實體瀏覽器背景執行\n", "my_options.add_argument(\"--start-maximized\")        #最大化視窗\n", "my_options.add_argument(\"--incognito\")              #開啟無痕模式\n", "my_options.add_argument(\"--disable-popup-blocking\") #禁用彈出攔截\n", "my_options.add_argument(\"--disable-notifications\")  #取消通知\n", "my_options.add_argument(\"--lang=zh-TW\")  #設定為正體中文\n", "\n", "# 指定 chromedriver 檔案的路徑\n", "driver_exec_path = './chromedriver.exe'\n", "\n", "# 給 web driver 用的變數\n", "driver = None\n", "\n", "# 來源首頁\n", "prefix_url = 'http://www.concentric-literature.url.tw'\n", "url = prefix_url + ''\n", "\n", "# 指定 sheet name\n", "folderName = sheetName = 'www_concentric_literature_url_tw'\n", "\n", "# 指定 json 檔名\n", "jsonFileName = f'{folderName}.json'\n", "\n", "# 建立儲存檔案用的資料夾\n", "folderPath = f'./{folderName}'\n", "if not os.path.exists(folderPath):\n", "    os.makedirs(folderPath)\n", "\n", "#預設下載路徑\n", "my_options.add_experimental_option(\"prefs\", {\n", "    \"download.default_directory\": folderPath\n", "})\n", "\n", "# 放置爬取的資料\n", "listData = []\n", "\n", "# iframe 網址\n", "listIframeLinks = []"]}, {"cell_type": "markdown", "id": "97dc925f", "metadata": {}, "source": ["# 自訂函式 (網路爬蟲執行流程)"]}, {"cell_type": "code", "execution_count": null, "id": "c2534200", "metadata": {}, "outputs": [], "source": ["'''\n", "函式\n", "'''\n", "# md5 (用來為每一筆資料建立唯一代號)\n", "def md5(string):\n", "    m = hashlib.md5()\n", "    m.update(string.encode(\"utf-8\"))\n", "    return m.hexdigest()\n", "\n", "# 初始化 Web Driver\n", "def init():\n", "    global driver\n", "    # 使用 Chrome 的 WebDriver\n", "    driver = webdriver.Chrome( \n", "        options = my_options, \n", "        executable_path = driver_exec_path\n", "    )\n", "    \n", "# 走訪頁面\n", "def visit():\n", "    global driver\n", "    \n", "    try:\n", "        # 走訪首頁\n", "        driver.get(url)\n", "\n", "        # 等待目標元素出現\n", "        WebDriverWait(driver, 10).until(\n", "            EC.presence_of_element_located(\n", "                (By.CSS_SELECTOR, 'div.th_year > div.year_block > div.md_year > div.year_rt')\n", "            )\n", "        )\n", "        \n", "    except TimeoutException as e:\n", "        print('等待逾時: visit')\n", "    \n", "# 剖析元素資料\n", "def parse():\n", "    global driver\n", "    \n", "    try:\n", "        # 過濾出 iframe 路徑\n", "        regexIframeLinks = r'\\/lig[nh]tbox\\d{4}-\\d{1}\\.html'\n", "        \n", "        # 整理出所有 iframe 路徑\n", "        div_year_elms = driver.find_elements(By.CSS_SELECTOR, 'div.th_year > div.year_block > div.md_year > div.year_rt')\n", "        for div in div_year_elms:\n", "            strInnerText = div.get_attribute('innerHTML')\n", "            listIframeLinks.extend(re.findall(regexIframeLinks, strInnerText))\n", "            \n", "        # 更新成正確的 iframe 網址\n", "        for index in range(len(listIframeLinks)):\n", "            listIframeLinks[index] = prefix_url + listIframeLinks[index]\n", "            \n", "        # 過濾刊號與出版日期\n", "        regexPublish = r'(.+)\\s\\|\\s(.+)'\n", "            \n", "        # 走訪所有 iframe 內頁\n", "        for link in listIframeLinks:\n", "            # 等待\n", "            sleep(randint(1,2))\n", "            \n", "            # 走訪內頁\n", "            driver.get(link)\n", "            \n", "            # 等待目標元素出現\n", "            WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located(\n", "                    (By.CSS_SELECTOR, 'div#wrap > main div.single-item div.slider-item')\n", "                )\n", "            )\n", "            \n", "            # 取得期刊標題\n", "            strJournalTitle = driver.find_elements(By.CSS_SELECTOR, 'div.single-item div.slider-item header h4')[0].get_attribute('innerText').strip()\n", "            \n", "            # 取得刊號與出版日期\n", "            strPublish = driver.find_elements(By.CSS_SELECTOR, 'div.single-item div.slider-item header span.booktime')[0].get_attribute('innerText').strip()\n", "            matchPublish = re.search(regexPublish, strPublish)\n", "            strPublishNum = matchPublish[1]\n", "            strPublishDate = matchPublish[2]\n", "            \n", "            # 取得主要元素\n", "            a_elms = driver.find_elements(By.CSS_SELECTOR, 'div.single-item div.slider-item:not([class~=slick-cloned]) div.th_lightbox a.md_book[href$=pdf]')\n", "            for a in a_elms:\n", "                # 篇名(書名)\n", "                strJournalName = a.find_element(By.CSS_SELECTOR, 'div > p').get_attribute('innerText').strip()\n", "                \n", "                # 作者\n", "                strAuthor = a.find_element(By.CSS_SELECTOR, 'header').get_attribute('innerText').strip()\n", "                \n", "                # pdf 連結\n", "                strPdfLink = a.get_attribute('href')\n", "                \n", "                # 整理資料\n", "                listData.append({\n", "                    'id': md5(strPdfLink),\n", "                    '刊名': strJournalTitle,\n", "                    '刊號': strPublishNum,\n", "                    '出版日期': strPublishDate,\n", "                    '篇名': strJournalName,\n", "                    '作者': s<PERSON><PERSON><PERSON><PERSON>,\n", "                    '網頁': link,\n", "                    'pdf連結': strPdfLink\n", "                })\n", "        \n", "    except TimeoutException as e:\n", "        print('等待逾時: parse')\n", "    \n", "        \n", "# 關閉瀏覽器\n", "def close():\n", "    global driver\n", "    driver.quit()\n", "        \n", "# 儲存成 json\n", "def save<PERSON><PERSON>():\n", "    global listData\n", "    with open(f\"{folderPath}/{jsonFileName}\", \"w\", encoding=\"utf-8\") as file:\n", "        file.write( json.dumps( listData, ensure_ascii=False, indent=4 ) )\n", "    \n", "# 儲存 .db\n", "def saveDB():\n", "    with open(f\"{folderPath}/{jsonFileName}\", \"r\", encoding=\"utf-8\") as file:      \n", "        # 取得 json 內容\n", "        strJson = file.read()\n", "        \n", "        # 將 json 轉成 list\n", "        listJson = json.loads(strJson)\n", "        \n", "    # 寫入對話記錄\n", "    conn = sqlite3.connect(f\"{folderPath}/{folderName}.db\")\n", "    \n", "    # 建立 cursor 物件\n", "    cursor = conn.cursor()\n", "\n", "    # 執行 SQL 語法\n", "    try:\n", "        # 查詢特定資料，看看是否已經存在於資料表當中\n", "        sql_query = f'''\n", "        SELECT 1\n", "        FROM journals\n", "        WHERE id = ?\n", "        '''\n", "        \n", "        # 寫入資料\n", "        sql_insert = f'''\n", "        INSERT INTO journals (\n", "            id, journal_title, publish_num, publish_date, journal_sub_title, \n", "            author, link, pdf_link, is_downloaded, created_at, \n", "            updated_at\n", "        ) VALUES ( \n", "            ?,?,?,?,?,\n", "            ?,?,?,?,?,\n", "            ?\n", "        )\n", "        '''\n", "        \n", "        # 放置準備寫入的資料\n", "        list_insert = []\n", "        \n", "        # 將 json 資料一筆一筆找出來\n", "        for myDict in list<PERSON><PERSON>:\n", "            # 如果資料庫沒有這筆資料(透過 id)，則將資料以 tuple 格式放到 list 當中，方便新增 bulk 資料\n", "            if cursor.execute(sql_query, (myDict[\"id\"],)).fetchone() == None:\n", "                # 整合所有需要寫入的資料\n", "                list_insert.append((\n", "                    myDict['id'],\n", "                    myDict['刊名'],\n", "                    myDict['刊號'],\n", "                    myDict['出版日期'],\n", "                    myDict['篇名'],\n", "                    myDict['作者'],\n", "                    myDict['網頁'],\n", "                    myDict['pdf連結'],\n", "                    0,\n", "                    datetime.today().strftime(\"%Y-%m-%d %H-%M-%S\"),\n", "                    datetime.today().strftime(\"%Y-%m-%d %H-%M-%S\")\n", "                ))\n", "        \n", "        # 新增資料到資料庫當中\n", "        cursor.executemany(sql_insert, list_insert)\n", "        \n", "        # 執行 SQL 語法\n", "        conn.commit()\n", "    except sqlite3.<PERSON>rror as err: \n", "        # 回滾\n", "        conn.rollback()\n", "\n", "        # SQLite3 例外處理\n", "        exc_type, exc_value, exc_tb = sys.exc_info()\n", "        strErrorMsg = f'''SQLite error: {' '.join(err.args)}\\n\\n\n", "        SQLite traceback: {traceback.format_exception(exc_type, exc_value, exc_tb)}\n", "        '''\n", "        print(strErrorMsg)\n", "    finally:\n", "        # 關閉 sqlite\n", "        conn.close()\n", "    \n", "# 下載\n", "def download():\n", "    # 寫入對話記錄\n", "    conn = sqlite3.connect(f\"{folderPath}/{folderName}.db\")\n", "    \n", "    # 將查詢出來的結果 (tuple)，變成 key-value 型式 (dict)\n", "    conn.row_factory = sqlite3.Row\n", "    \n", "    # 建立 cursor 物件\n", "    cursor = conn.cursor()\n", "\n", "    # 執行 SQL 語法\n", "    try:\n", "        # 查詢尚未下載的資料\n", "        sql_query = f'''\n", "        SELECT sn, id, pdf_link\n", "        FROM journals\n", "        WHERE `is_downloaded` = 0\n", "        '''\n", "        \n", "        # 更新資料的欄位(是否下載過)\n", "        sql_update = f'''\n", "        UPDATE `journals` \n", "        SET \n", "            `is_downloaded` = 1 ,\n", "            `updated_at` = ?\n", "        WHERE `id` = ?\n", "        '''\n", "            \n", "        # 取得所有未下載的資料\n", "        for myDict in cursor.execute(sql_query).fetchall():\n", "            # 等待\n", "            sleep(randint(1,2))\n", "\n", "            # 下載 pdf\n", "            cmd = [\n", "                'curl', \n", "                '-H', 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Safari/537.36',\n", "                '-L', myDict[\"pdf_link\"], \n", "                '-o', f'{folderPath}/{myDict[\"sn\"]}_{myDict[\"id\"]}.pdf'\n", "            ]\n", "            result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT)\n", "            #output = result.stdout\n", "            #pprint(output)\n", "            print(f'{folderPath}/{myDict[\"sn\"]}_{myDict[\"id\"]}.pdf')\n", "\n", "            # 將 is_downloaded 改成 1，代表已下載過\n", "            cursor.execute(sql_update, (datetime.today().strftime(\"%Y-%m-%d %H-%M-%S\"), myDict[\"id\"],))\n", "                \n", "        conn.commit()\n", "    except sqlite3.<PERSON>rror as err: \n", "        # 回滾\n", "        conn.rollback()\n", "\n", "        # SQLite3 例外處理\n", "        exc_type, exc_value, exc_tb = sys.exc_info()\n", "        strErrorMsg = f'''SQLite error: {' '.join(err.args)}\\n\\n\n", "        SQLite traceback: {traceback.format_exception(exc_type, exc_value, exc_tb)}\n", "        '''\n", "        print(strErrorMsg)\n", "    finally:\n", "        # 關閉 sqlite\n", "        conn.close()"]}, {"cell_type": "markdown", "id": "0a350409", "metadata": {}, "source": ["# 以下函式，請各別依情況分別、陸續執行"]}, {"cell_type": "code", "execution_count": null, "id": "441eb725", "metadata": {}, "outputs": [], "source": ["# 初始化 Web Driver\n", "init()"]}, {"cell_type": "code", "execution_count": null, "id": "e5bb3d40", "metadata": {}, "outputs": [], "source": ["# 走訪頁面\n", "visit()"]}, {"cell_type": "code", "execution_count": null, "id": "7e22f76a", "metadata": {}, "outputs": [], "source": ["# 剖析元素資料\n", "parse()"]}, {"cell_type": "code", "execution_count": null, "id": "725657b0", "metadata": {}, "outputs": [], "source": ["# 關閉瀏覽器\n", "close()"]}, {"cell_type": "code", "execution_count": null, "id": "ccb4a0cc", "metadata": {}, "outputs": [], "source": ["# 儲存成 json\n", "<PERSON><PERSON><PERSON>()"]}, {"cell_type": "code", "execution_count": null, "id": "408bc090", "metadata": {}, "outputs": [], "source": ["# 儲存 .db\n", "saveDB()"]}, {"cell_type": "code", "execution_count": null, "id": "491c5bbb", "metadata": {}, "outputs": [], "source": ["# 下載\n", "download()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}}, "nbformat": 4, "nbformat_minor": 5}