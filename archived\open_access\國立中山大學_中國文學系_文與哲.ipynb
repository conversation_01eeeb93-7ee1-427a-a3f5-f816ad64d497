{"cells": [{"cell_type": "code", "execution_count": null, "id": "8e17f17b", "metadata": {}, "outputs": [], "source": ["!pip install -U openpyxl selenium beautifulsoup4 lxml requests"]}, {"cell_type": "code", "execution_count": null, "id": "e751e389", "metadata": {}, "outputs": [], "source": ["'''\n", "注意事項:\n", "下載對應的 ChromeDriver (web driver) 到程式檔案同一個目錄下後解壓縮，下載前記得對應版本編號。\n", "連結: https://chromedriver.chromium.org/downloads\n", "\n", "參考網頁:\n", "[1] 國立中山大學 中國文學系\n", "http://www.chinese.nsysu.edu.tw/zh_tw/Academic_Achievements/Publication/BOOK\n", "'''\n", "\n", "\n", "'''\n", "匯入套件\n", "'''\n", "# HTML parser\n", "from bs4 import BeautifulSoup as bs\n", "\n", "# 網路請求工具\n", "import requests as req\n", "\n", "# 強制等待 (執行期間休息一下)\n", "from time import sleep\n", "\n", "# pretty-print\n", "from pprint import pprint\n", "\n", "# 隨機\n", "from random import randint\n", "\n", "# 計時\n", "import time\n", "\n", "# 整理 json 使用的工具\n", "import json\n", "\n", "# 執行 shell command 的時候用的\n", "import os\n", "\n", "# 子處理程序，用來取代 os.system 的功能\n", "import subprocess\n", "\n", "# 正規表達式\n", "import re\n", "\n", "# 編碼\n", "from urllib.parse import quote\n", "\n", "# 存取 Excel 的工具\n", "from openpyxl import load_workbook\n", "from openpyxl import Workbook\n", "\n", "# 來源首頁\n", "prefix_url = 'http://www.chinese.nsysu.edu.tw'\n", "url = prefix_url + '/zh_tw/Academic_Achievements/Publication/BOOK'\n", "\n", "# 指定 sheet name\n", "sheetName = 'chinese_nsysu'\n", "\n", "# 指定 excel 檔名\n", "excelFileName = f'{sheetName}.xlsx'\n", "\n", "# 指定 json 檔名\n", "jsonFileName = f'{sheetName}.json'\n", "\n", "# 建立儲存圖片、影片的資料夾\n", "folderPath = f'./{sheetName}'\n", "if not os.path.exists(folderPath):\n", "    os.makedirs(folderPath)\n", "\n", "# 判斷 excel 檔案是否存在，不存在就新增\n", "filePath = folderPath + '/' + excelFileName\n", "if not os.path.exists(filePath):\n", "    workbook = Workbook() # 動態新增檔案\n", "    worksheet = workbook.create_sheet(sheetName, 0) # 建立並取得 active sheet\n", "else:\n", "    workbook = load_workbook(filename = filePath)\n", "    worksheet = workbook[sheetName] # 取得 active sheet\n", "\n", "#預設下載路徑\n", "my_options.add_experimental_option(\"prefs\", {\n", "    \"download.default_directory\": folderPath\n", "})\n", "    \n", "# excel 標題\n", "worksheet['A1'] = \"流水號\"\n", "worksheet['B1'] = \"期刊標題\"\n", "worksheet['C1'] = \"期號\"\n", "worksheet['D1'] = \"出版日期\"\n", "worksheet['E1'] = \"主編\"\n", "worksheet['F1'] = \"章節列表_網頁連結\"\n", "worksheet['G1'] = \"篇名\"\n", "worksheet['H1'] = \"作者\"\n", "worksheet['I1'] = \"論文連結_原始\"\n", "worksheet['J1'] = \"論文連結_curl可用\"\n", "\n", "# 自訂標頭\n", "my_headers = {\n", "    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36'\n", "}\n", "\n", "# 放置首頁分頁的 set 變數\n", "setPagination = set()\n", "\n", "# 放置爬取的資料\n", "listData = []"]}, {"cell_type": "code", "execution_count": null, "id": "1ab7f260", "metadata": {}, "outputs": [], "source": ["'''\n", "函式\n", "'''\n", "# 取得分頁連結\n", "def getPaginationLink():\n", "    global setPagination\n", "    \n", "    # 將自訂標頭加入 GET 請求中\n", "    res = req.get(url, headers = my_headers)\n", "\n", "    # 建立 soup 物件\n", "    soup = bs(res.text, 'lxml')\n", "    \n", "    # 取得分頁連結\n", "    a_elms = soup.select('ul.pagination.pagination-sm > li > a');\n", "    \n", "    # 整理分頁\n", "    for a in a_elms:\n", "        setPagination.add(url + a['href'])\n", "\n", "# 取得分頁資料\n", "def getMainLinks():\n", "    global setPagination\n", "    \n", "    # 走訪分頁\n", "    for link in list(setPagination):\n", "        # 將自訂標頭加入 GET 請求中\n", "        res = req.get(link, headers = my_headers)\n", "\n", "        # 建立 soup 物件\n", "        soup = bs(res.text, 'lxml')\n", "        \n", "        # 取得表格資料\n", "        table_elm = soup.select_one('table.table.table-hover.table-striped.journals-index')\n", "        tr_elms = table_elm.select('tbody[data-list=\"journals\"] > tr')\n", "        for tr in tr_elms:\n", "            # 直接取得每一個 tr 底下的所有 td 集合\n", "            td_elms = tr.select('td')\n", "            \n", "            # 期刊標題\n", "            strJournalTitle = td_elms[1].get_text().strip()\n", "            \n", "            # 期號\n", "            matchNum = re.search(r\"\\d+\", strJournalTitle)\n", "            intJournalNum = int(matchNum[0])\n", "            \n", "            # 出版日期\n", "            strJounalPublishDate = td_elms[2].get_text().strip()\n", "            \n", "            # 主編\n", "            strEditor = td_elms[3].get_text().strip()\n", "            \n", "            # 章節列表_網頁連結\n", "            strLink = prefix_url + td_elms[4].select_one('a')['href']\n", "            \n", "            # 整理資料\n", "            listData.append({\n", "                \"期刊標題\": strJournalTitle,\n", "                \"期號\": intJournalNum,\n", "                \"出版日期\": strJounalPublishDate,\n", "                \"主編\": str<PERSON><PERSON><PERSON>,\n", "                \"章節列表_網頁連結\": strLink\n", "            })\n", "\n", "# 取得內頁資料\n", "def getDetailedData():\n", "    global listData\n", "    \n", "    # 逐個內頁連結走訪\n", "    for index, myDict in enumerate(listData):\n", "        # 建立子節點，以便放置內頁資料\n", "        if not 'sub' in listData[index]:\n", "            listData[index]['sub'] = []\n", "            \n", "        # 將自訂標頭加入 GET 請求中\n", "        res = req.get(myDict['章節列表_網頁連結'], headers = my_headers)\n", "        \n", "        # 等待\n", "        sleep(randint(1,3))\n", "        \n", "        # 建立 soup 物件\n", "        soup = bs(res.text, 'lxml')\n", "        \n", "        # 取得表格\n", "        table_elm = soup.select_one('table.table.table-hover.table-striped.chapters-index')\n", "        tr_elms = table_elm.select('tbody[data-list=\"chapters\"] > tr')\n", "        for tr in tr_elms:\n", "            # 整理資料的 dict\n", "            dict_tmp = {}\n", "            \n", "            # 直接取得每一個 tr 底下的所有 td 集合\n", "            td_elms = tr.select('td')\n", "            \n", "            # 篇名\n", "            strPdfName = td_elms[0].select_one('a').get_text().strip()\n", "            \n", "            # 作者\n", "            strAuthor = td_elms[1].get_text().strip()\n", "            \n", "            # 初步整理資料\n", "            dict_tmp['篇名'] = strPdfName\n", "            dict_tmp['作者'] = strAuthor\n", "            dict_tmp['論文連結_原始'] = dict_tmp['論文連結_curl可用'] = \"\"\n", "            \n", "            # 論文連結\n", "            strPdfLink = strPdfLinkForCurl = \"\"\n", "            \n", "            # 如果「動作」欄位裡面的連結有一個以上，代表可能有 pdf 連結\n", "            if len( td_elms[3].select('a') ) > 0:\n", "                for a in td_elms[3].select('a'):\n", "                    if '下載' in a.get_text() and a.has_attr('href') and a['href'] != '':\n", "                        # 取得論文連結_原始\n", "                        strPdfLink = prefix_url + a['href']\n", "                        \n", "                        # 取得論文連結_curl可用\n", "                        if not '%' in strPdfLink:\n", "                            strPdfLinkForCurl = quote(strPdfLink, safe=':/')\n", "                        else:\n", "                            strPdfLinkForCurl = strPdfLink\n", "                        \n", "                        # 整理連結資料\n", "                        dict_tmp['論文連結_原始'] = strPdfLink\n", "                        dict_tmp['論文連結_curl可用'] = strPdfLinkForCurl\n", "                        \n", "            # 建立子節點，以便放置內頁資料\n", "            listData[index]['sub'].append(dict_tmp)\n", "        \n", "# 儲存成 json\n", "def save<PERSON><PERSON>():\n", "    global listData\n", "    with open(f\"{folderPath}/{jsonFileName}\", \"w\", encoding=\"utf-8\") as file:\n", "        file.write( json.dumps( listData, ensure_ascii=False, indent=4 ) )\n", "\n", "# 儲存成 excel\n", "def saveExcel():\n", "    with open(f\"{folderPath}/{jsonFileName}\", \"r\", encoding=\"utf-8\") as file:\n", "        # 從 excel 列號 2 開始寫入資料\n", "        row_num = 2\n", "        \n", "        # 取得 json 內容\n", "        strJson = file.read()\n", "        \n", "        # 將 json 轉成 list\n", "        listJson = json.loads(strJson)\n", "        \n", "        # 流水號\n", "        sn = 1\n", "        \n", "        # 逐列寫入\n", "        for myDict in list<PERSON><PERSON>:\n", "            for d in myDict['sub']:\n", "                worksheet['A' + str(row_num)] = sn\n", "                worksheet['B' + str(row_num)] = myDict[\"期刊標題\"]\n", "                worksheet['C' + str(row_num)] = myDict[\"期號\"]\n", "                worksheet['D' + str(row_num)] = myDict[\"出版日期\"]\n", "                worksheet['E' + str(row_num)] = myDict[\"主編\"]\n", "                worksheet['F' + str(row_num)] = myDict[\"章節列表_網頁連結\"]\n", "                worksheet['G' + str(row_num)] = d[\"篇名\"]\n", "                worksheet['H' + str(row_num)] = d[\"作者\"]\n", "                worksheet['I' + str(row_num)] = d[\"論文連結_原始\"]\n", "                worksheet['J' + str(row_num)] = d[\"論文連結_curl可用\"]\n", "                row_num += 1\n", "                sn += 1\n", "    \n", "    # 儲存 workbook\n", "    workbook.save(filePath)\n", "\n", "    # 關閉 workbook\n", "    workbook.close()\n", "    \n", "# 下載\n", "def download():\n", "    with open(f\"{folderPath}/{jsonFileName}\", \"r\", encoding=\"utf-8\") as file:      \n", "        # 取得 json 內容\n", "        strJson = file.read()\n", "        \n", "        # 將 json 轉成 list\n", "        listJson = json.loads(strJson)\n", "        \n", "        # 流水號\n", "        sn = 1\n", "        \n", "        for myDict in list<PERSON><PERSON>:\n", "            for d in myDict['sub']:\n", "                # 若屬性的值不為空，代表有 pdf 連結，準備進行下載\n", "                if d[\"論文連結_curl可用\"] != '':\n", "                    # 等待\n", "                    sleep(randint(1,3))\n", "                \n", "                    # 下載 pdf\n", "                    cmd = ['curl', '-L', d[\"論文連結_curl可用\"], '-o', f'{folderPath}/sn_{sn}.pdf']\n", "                    result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT)\n", "                    print(f'{folderPath}/sn_{sn}.pdf')\n", "                sn += 1"]}, {"cell_type": "code", "execution_count": null, "id": "c895fc71", "metadata": {}, "outputs": [], "source": ["# 取得分頁連結\n", "getPaginationLink()"]}, {"cell_type": "code", "execution_count": null, "id": "80f4a823", "metadata": {}, "outputs": [], "source": ["# 取得分頁資料\n", "getMainLinks()"]}, {"cell_type": "code", "execution_count": null, "id": "e837a91c", "metadata": {}, "outputs": [], "source": ["# 取得內頁資料\n", "getDetailedData()"]}, {"cell_type": "code", "execution_count": null, "id": "e5452bcb", "metadata": {}, "outputs": [], "source": ["# 儲存成 json\n", "<PERSON><PERSON><PERSON>()"]}, {"cell_type": "code", "execution_count": null, "id": "18f74510", "metadata": {}, "outputs": [], "source": ["# 儲存成 excel\n", "saveExcel()"]}, {"cell_type": "code", "execution_count": null, "id": "dfa74dcb", "metadata": {}, "outputs": [], "source": ["time_begin = time.time()\n", "download()\n", "time_end = time.time()\n", "print(f\"總共執行了 { time_end - time_begin } 秒\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}}, "nbformat": 4, "nbformat_minor": 5}