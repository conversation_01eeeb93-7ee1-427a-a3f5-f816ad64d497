{"cells": [{"cell_type": "code", "execution_count": null, "id": "e1ed71e2", "metadata": {}, "outputs": [], "source": ["'''\n", "安裝 tika\n", "\n", "參考網頁\n", "https://pypi.org/project/tika/\n", "'''\n", "!pip install tika"]}, {"cell_type": "code", "execution_count": null, "id": "dde939c4", "metadata": {"scrolled": false}, "outputs": [], "source": ["'''\n", "東吳大學 政治學報\n", "https://web-ch.scu.edu.tw/politics/file/11106\n", "\n", "參考連結:\n", "[1] python selenium 對瀏覽器標籤頁進行關閉和切換\n", "https://www.itread01.com/content/1543567328.html\n", "'''\n", "\n", "\n", "'''\n", "匯入套件\n", "'''\n", "# 操作 browser 的 API\n", "from selenium import webdriver\n", "\n", "# 處理逾時例外的工具\n", "from selenium.common.exceptions import TimeoutException\n", "\n", "# 面對動態網頁，等待某個元素出現的工具，通常與 exptected_conditions 搭配\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "\n", "# 搭配 WebDriverWait 使用，對元素狀態的一種期待條件，若條件發生，則等待結束，往下一行執行\n", "from selenium.webdriver.support import expected_conditions as EC\n", "\n", "# 期待元素出現要透過什麼方式指定，通常與 EC、WebDriverWait 一起使用\n", "from selenium.webdriver.common.by import By\n", "\n", "# 強制等待 (執行期間休息一下)\n", "from time import sleep\n", "\n", "# 整理 json 使用的工具\n", "import json\n", "\n", "# 執行 command 的時候用的\n", "import os\n", "\n", "# 正規表達式\n", "import re\n", "\n", "# 使用 tika 的 parser\n", "from tika import parser\n", "\n", "'''\n", "Selenium with Python 中文翻譯文檔\n", "參考網頁：https://selenium-python-zh.readthedocs.io/en/latest/index.html\n", "selenium 啓動 Chrome 的進階配置參數\n", "參考網址：https://stackoverflow.max-everyday.com/2019/12/selenium-chrome-options/\n", "Mouse Hover Action in Selenium\n", "參考網址：https://www.toolsqa.com/selenium-webdriver/mouse-hover-action/\n", "yt-dlp 下載影音的好工具\n", "參考網址：https://github.com/yt-dlp/yt-dlp\n", "'''\n", "\n", "# 啟動瀏覽器工具的選項\n", "my_options = webdriver.ChromeOptions()\n", "# my_options.add_argument(\"--headless\")                #不開啟實體瀏覽器背景執行\n", "my_options.add_argument(\"--start-maximized\")         #最大化視窗\n", "my_options.add_argument(\"--incognito\")               #開啟無痕模式\n", "my_options.add_argument(\"--disable-popup-blocking\") #禁用彈出攔截\n", "my_options.add_argument(\"--disable-notifications\")  #取消通知\n", "my_options.add_argument(\"--lang=zh-TW\")  #設定為正體中文\n", "\n", "# 指定 chromedriver 檔案的路徑\n", "driver_exec_path = './chromedriver.exe'\n", "\n", "# 使用 Chrome 的 WebDriver\n", "driver = webdriver.Chrome( \n", "    options = my_options, \n", "    executable_path = driver_exec_path\n", ")\n", "\n", "# 放置爬取的資料\n", "listData = []\n", "\n", "# 建立資料夾\n", "folderPath = 'parsed_files'\n", "if not os.path.exists(folderPath):\n", "    os.makedirs(folderPath)\n", "\n", "# 網址\n", "url = 'https://web-ch.scu.edu.tw/politics/file/11106'\n", "\n", "'''\n", "函式\n", "'''\n", "# 走訪頁面\n", "def visit():\n", "    driver.get(url);\n", "    \n", "# 取得主要連結\n", "def getMainData():\n", "    try:\n", "        # 等待主要連結出現\n", "        WebDriverWait(driver, 10).until(\n", "            EC.presence_of_element_located(\n", "                (\n", "                    By.CSS_SELECTOR, \n", "                    \"div#rndbox_body table.table.table-striped.table-bordered tbody td a\"\n", "                )\n", "            )\n", "        )\n", "        \n", "        # 得到所有連結的數量\n", "        count = len(driver.find_elements(By.CSS_SELECTOR, \"div#rndbox_body table.table.table-striped.table-bordered tbody td a\"))\n", "        \n", "        # 切換到新分頁(初始分頁代號為 0，新開的為 1，所以切換到 1，代表移到分頁去操作)\n", "        for i in range(count):\n", "            # 開啟新分頁\n", "            driver.execute_script(f'window.open(\"{url}\", \"_blank\");')\n", "            \n", "            # 等待一下\n", "            sleep(7)\n", "            \n", "            # 切換到分頁\n", "            driver.switch_to.window(driver.window_handles[1])\n", "            \n", "            # 等元素出現\n", "            WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located(\n", "                    (\n", "                        By.CSS_SELECTOR, \n", "                        \"div#rndbox_body table.table.table-striped.table-bordered tbody td a\")\n", "                )\n", "            )\n", "            \n", "            # 取得 a 元素集合\n", "            a_elms = driver.find_elements(By.CSS_SELECTOR, \"div#rndbox_body table.table.table-striped.table-bordered tbody td a\")\n", "            \n", "            # 按下超連結\n", "            a_elms[i].click()\n", "        \n", "            # 等 pdf 資訊出現\n", "            WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located(\n", "                    (\n", "                        By.CSS_SELECTOR,\n", "                        \"div#article ol li h2 a\"\n", "                    )\n", "                )\n", "            )\n", "            \n", "            # 輸出所有 pdf 資訊\n", "            a_article_elms = driver.find_elements(By.CSS_SELECTOR, \"div#article ol li h2 a\")\n", "            for a_article in a_article_elms:\n", "                print(f\"文章名稱: {a_article.get_attribute('innerText')}\")\n", "                print(f\"PDF連結: {a_article.get_attribute('href')}\")\n", "                \n", "                # 簡單資料清理\n", "                title = re.sub(r\"\\?|’|:| |\\s\", \"_\", a_article.get_attribute('innerText'))\n", "                \n", "                # 讀取兩篇 pdf 的內文，並寫入檔案\n", "                with open(f\"{folderPath}/{title}.txt\", \"w\", encoding=\"utf-8\") as file:\n", "                    parsed_pdf = parser.from_file( a_article.get_attribute('href') )\n", "                    file.write( parsed_pdf['content'] )\n", "            \n", "            # 關閉當前分頁\n", "            driver.close()\n", "            \n", "            # 切換到初始分頁\n", "            driver.switch_to.window(driver.window_handles[0])\n", "            \n", "    except TimeoutException:\n", "        print(\"等不到指定元素出現…\")\n", "\n", "        \n", "# 關閉瀏覽器\n", "def close():\n", "    driver.quit()\n", "        \n", "'''\n", "主程式\n", "'''\n", "if __name__ == '__main__':\n", "    visit()\n", "    getMainData()\n", "    close()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}}, "nbformat": 4, "nbformat_minor": 5}