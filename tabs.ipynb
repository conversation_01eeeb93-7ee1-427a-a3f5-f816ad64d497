{"cells": [{"cell_type": "code", "execution_count": null, "id": "f4a081b8", "metadata": {}, "outputs": [], "source": ["'''\n", "匯入套件\n", "'''\n", "# 操作 browser 的 API\n", "from selenium import webdriver\n", "from selenium.webdriver.chrome.service import Service\n", "\n", "# ChromeDriver 的下載管理工具\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "# 處理逾時例外的工具\n", "from selenium.common.exceptions import TimeoutException\n", "\n", "# 面對動態網頁，等待某個元素出現的工具，通常與 exptected_conditions 搭配\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "\n", "# 搭配 WebDriverWait 使用，對元素狀態的一種期待條件，若條件發生，則等待結束，往下一行執行\n", "from selenium.webdriver.support import expected_conditions as EC\n", "\n", "# 期待元素出現要透過什麼方式指定，通常與 EC、WebDriverWait 一起使用\n", "from selenium.webdriver.common.by import By\n", "\n", "\n", "# 啟動瀏覽器工具的選項\n", "my_options = webdriver.ChromeOptions()\n", "# my_options.add_argument(\"--headless\")                #不開啟實體瀏覽器背景執行\n", "my_options.add_argument(\"--start-maximized\")         #最大化視窗\n", "my_options.add_argument(\"--incognito\")               #開啟無痕模式\n", "my_options.add_argument(\"--disable-popup-blocking\") #禁用彈出攔截\n", "my_options.add_argument(\"--disable-notifications\")  #取消 chrome 推播通知\n", "my_options.add_argument(\"--lang=zh-TW\")  #設定為正體中文\n", "\n", "# 使用 Chrome 的 WebDriver\n", "driver = webdriver.Chrome(\n", "    options = my_options\n", ")\n", "\n", "# 儲存資料的變數\n", "list_data = []"]}, {"cell_type": "markdown", "id": "a74f4a97", "metadata": {}, "source": ["---\n", "# 建立瀏覽器分頁: window.open\n", "- [window.open()](https://www.w3schools.com/jsref/met_win_open.asp)\n", "- [東吳大學中文學報 - 各期全文下載總覽列表](https://web-ch.scu.edu.tw/chinese/file/3423)"]}, {"cell_type": "code", "execution_count": null, "id": "b110643d", "metadata": {}, "outputs": [], "source": ["# 連續開 2 個分頁\n", "for i in range(2):\n", "    driver.execute_script(f'window.open(\"\");')"]}, {"cell_type": "code", "execution_count": null, "id": "bf9a5c51", "metadata": {}, "outputs": [], "source": ["# 切換到初始分頁 (索引為 0)\n", "driver.switch_to.window( driver.window_handles[0] )"]}, {"cell_type": "code", "execution_count": null, "id": "4f44a1cc", "metadata": {}, "outputs": [], "source": ["# 將所有 tabs 轉址，以便取得對應列表\n", "for index in range(1, len(driver.window_handles)):\n", "    # 切換分頁\n", "    driver.switch_to.window(\n", "        driver.window_handles[index]\n", "    )\n", "    \n", "    # 使分頁自動連結到指定網址 (此時的 drive 變數指向切後的分頁)\n", "    driver.get(f\"https://web-ch.scu.edu.tw/chinese/file/3423?page={index}\")\n", "\n", "    # 取得列表連結與內文\n", "    for a in driver.find_elements(By.CSS_SELECTOR, 'table.table.table-striped.table-border tbody tr a'):\n", "        list_data.append({\n", "            'title': a.get_attribute('innerText'),\n", "            'link': a.get_attribute('href')\n", "        })"]}, {"cell_type": "code", "execution_count": null, "id": "2415b152", "metadata": {}, "outputs": [], "source": ["# 讀最後一頁開始，把所有分頁關掉 (初始頁要保留)\n", "while len(driver.window_handles) > 1:\n", "    # 切換分頁\n", "    driver.switch_to.window( \n", "        driver.window_handles[ len(driver.window_handles) - 1 ] \n", "    )\n", "    \n", "    # 關閉分頁 (與 driver.quit() 不同)\n", "    driver.close()"]}, {"cell_type": "code", "execution_count": null, "id": "ddb395da", "metadata": {}, "outputs": [], "source": ["# 關閉瀏覽器\n", "driver.quit()"]}, {"cell_type": "code", "execution_count": null, "id": "51efacaf", "metadata": {}, "outputs": [], "source": ["# 預覽結果\n", "list_data"]}], "metadata": {"kernelspec": {"display_name": "Python3@da", "language": "python", "name": "da"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 5}