{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["'''\n", "司法院大法官 -> 解釋及不受理決議 -> 不受理決議\n", "\n", "範例連結: https://cons.judicial.gov.tw/jcc/zh-tw/rrp04?page=1\n", "'''\n", "import requests, json\n", "from bs4 import BeautifulSoup\n", "\n", "# 整理所有資料的變數\n", "listData = []\n", "\n", "# 走訪總頁數\n", "pages = 1\n", "\n", "# 取得 table 中的列表連結\n", "def getListItems():\n", "    for page in range(1, pages + 1):\n", "        response = requests.get(f\"https://cons.judicial.gov.tw/jcc/zh-tw/rrp04?page={page}\")\n", "        soup = BeautifulSoup(response.text, 'lxml')\n", "        a_elms = soup.select(\"table.blocky_body.form_table.form_table_second.sm-responsive tbody tr td[data-head='案號'] a\")\n", "        for a in a_elms:\n", "            listData.append({\n", "                \"title\": a.get_text(),\n", "                \"link\": \"https://cons.judicial.gov.tw\" + a[\"href\"]\n", "            })\n", "\n", "# 根據先前儲存的列表連結，爬出需要的資訊\n", "def getItemDetail():\n", "    for index, _dict in enumerate(listData):\n", "        response = requests.get(_dict['link'])\n", "        soup = BeautifulSoup(response.text, 'lxml')\n", "        pre_elms = soup.select('div.item.title-w-8 pre.content.pure_text')\n", "        listData[index]['公布院令'] = pre_elms[0].get_text()\n", "        listData[index]['會次'] = pre_elms[1].get_text()\n", "        listData[index]['日期'] = pre_elms[2].get_text()\n", "        listData[index]['案號'] = pre_elms[3].get_text()\n", "        listData[index]['聲請人'] = pre_elms[4].get_text()\n", "        listData[index]['案由'] = pre_elms[5].get_text()\n", "        listData[index]['決議'] = pre_elms[6].get_text()\n", "\n", "# 將所有資訊轉成 JSON 檔\n", "def save<PERSON><PERSON>():\n", "    with open(\"JCC_reject.json\", \"w\", encoding=\"utf-8\") as file:\n", "        file.write( json.dumps(listData, ensure_ascii=False, indent=4) )\n", "\n", "# 主程式區段\n", "if __name__ == \"__main__\":\n", "    getListItems()\n", "    getItemDetail()\n", "    <PERSON><PERSON><PERSON>()\n", "        "]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}}, "nbformat": 4, "nbformat_minor": 4}