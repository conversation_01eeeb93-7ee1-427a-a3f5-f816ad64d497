{"cells": [{"cell_type": "code", "execution_count": null, "id": "10fcc5fe", "metadata": {}, "outputs": [], "source": ["!pip install -U openpyxl selenium beautifulsoup4 lxml requests tika"]}, {"cell_type": "code", "execution_count": null, "id": "c0f87987", "metadata": {}, "outputs": [], "source": ["'''\n", "注意事項:\n", "下載對應的 ChromeDriver (web driver) 到程式檔案同一個目錄下後解壓縮，下載前記得對應版本編號。\n", "連結: https://chromedriver.chromium.org/downloads\n", "\n", "參考網頁:\n", "[1] 國立臺灣大學美術史研究集刊\n", "http://ejournal.press.ntu.edu.tw/query.php?Action_From=level&lvbw=F111\n", "[2] sqlite3 --- SQLite 数据库 DB-API 2.0 接口模块\n", "https://docs.python.org/zh-tw/3/library/sqlite3.html\n", "[3] Selenium give file name when downloading\n", "https://stackoverflow.com/questions/34548041/selenium-give-file-name-when-downloading\n", "'''\n", "\n", "\n", "'''\n", "匯入套件\n", "'''\n", "# HTML parser\n", "from bs4 import BeautifulSoup as bs\n", "\n", "# 操作 browser 的 API\n", "from selenium import webdriver\n", "\n", "# 處理逾時例外的工具\n", "from selenium.common.exceptions import TimeoutException\n", "\n", "# 面對動態網頁，等待某個元素出現的工具，通常與 exptected_conditions 搭配\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "\n", "# 搭配 WebDriverWait 使用，對元素狀態的一種期待條件，若條件發生，則等待結束，往下一行執行\n", "from selenium.webdriver.support import expected_conditions as EC\n", "\n", "# 期待元素出現要透過什麼方式指定，通常與 EC、WebDriverWait 一起使用\n", "from selenium.webdriver.common.by import By\n", "\n", "# 處理下拉式選單的工具\n", "from selenium.webdriver.support.ui import Select\n", "\n", "# 強制等待 (執行期間休息一下)\n", "from time import sleep\n", "\n", "# pretty-print\n", "from pprint import pprint\n", "\n", "# 隨機\n", "from random import randint\n", "\n", "# 計時\n", "import time\n", "\n", "# 整理 json 使用的工具\n", "import json\n", "\n", "# 執行 shell command 的時候用的\n", "import os\n", "\n", "# 取得錯誤訊息\n", "import sys, traceback\n", "\n", "# 子處理程序，用來取代 os.system 的功能\n", "import subprocess\n", "\n", "# 正規表達式\n", "import re\n", "\n", "# 編碼\n", "from urllib.parse import quote\n", "\n", "# SQLite 資料庫\n", "import sqlite3\n", "\n", "# 存取 Excel 的工具\n", "from openpyxl import load_workbook\n", "from openpyxl import Workbook\n", "\n", "# 取得系統時間的工具\n", "from datetime import datetime\n", "\n", "# 引入 hashlib 模組\n", "import hashlib\n", "\n", "# 高階文件操作工具\n", "import shutil\n", "\n", "# 檔案剖析工具\n", "from tika import parser"]}, {"cell_type": "code", "execution_count": null, "id": "01cc6413", "metadata": {}, "outputs": [], "source": ["# 啟動瀏覽器工具的選項\n", "my_options = webdriver.ChromeOptions()\n", "# my_options.add_argument(\"--headless\")             #不開啟實體瀏覽器背景執行\n", "my_options.add_argument(\"--start-maximized\")        #最大化視窗\n", "my_options.add_argument(\"--incognito\")              #開啟無痕模式\n", "my_options.add_argument(\"--disable-popup-blocking\") #禁用彈出攔截\n", "my_options.add_argument(\"--disable-notifications\")  #取消通知\n", "my_options.add_argument(\"--lang=zh-TW\")  #設定為正體中文\n", "my_options.add_argument('--disable-gpu')\n", "my_options.add_argument('--disable-software-rasterizer')\n", "my_options.add_argument('--user-agent=\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Safari/537.36\"')\n", "\n", "# 指定 chromedriver 檔案的路徑\n", "driver_exec_path = './chromedriver.exe'\n", "\n", "# 給 web driver 用的變數\n", "driver = None\n", "\n", "# 來源首頁\n", "prefix_url = 'http://ejournal.press.ntu.edu.tw'\n", "url = prefix_url + '/query.php?Action_From=level&lvbw=F111'\n", "\n", "# 指定 sheet name\n", "folderName = sheetName = 'ejournal_press_ntu_edu_tw_F111'\n", "\n", "# 指定 json 檔名\n", "jsonFileName = f'{folderName}.json'\n", "\n", "# 建立儲存檔案用的資料夾\n", "folderPath = f'./{folderName}'\n", "if not os.path.exists(folderPath):\n", "    os.makedirs(folderPath)\n", "    \n", "# 設定 Chrome 下載路徑 (需要絕對路徑)\n", "fullDownloadPath = os.getcwd() + '\\\\' + folderName\n", "\n", "#預設下載路徑\n", "my_options.add_experimental_option(\"prefs\", {\n", "    \"download.default_directory\": fullDownloadPath,\n", "    \"download.prompt_for_download\": <PERSON>als<PERSON>,\n", "    \"download.directory_upgrade\": True,\n", "    \"safebrowsing_for_trusted_sources_enabled\": False,\n", "    \"safebrowsing.enabled\": <PERSON><PERSON><PERSON>,\n", "    \"plugins.always_open_pdf_externally\": True\n", "})\n", "\n", "# 放置爬取的資料\n", "listData = []"]}, {"cell_type": "code", "execution_count": null, "id": "92d74bd6", "metadata": {}, "outputs": [], "source": ["'''\n", "函式\n", "'''\n", "# md5 (用來為每一筆資料建立唯一代號)\n", "def md5(string):\n", "    m = hashlib.md5()\n", "    m.update(string.encode(\"utf-8\"))\n", "    return m.hexdigest()\n", "\n", "# 初始化 Web Driver\n", "def init():\n", "    global driver\n", "    # 使用 Chrome 的 WebDriver\n", "    driver = webdriver.Chrome( \n", "        options = my_options, \n", "        executable_path = driver_exec_path\n", "    )\n", "    \n", "# 走訪頁面\n", "def visit():\n", "    global driver\n", "    \n", "    try:\n", "        # 走訪首頁\n", "        driver.get(url)\n", "\n", "        # 等待目標元素出現\n", "        WebDriverWait(driver, 10).until(\n", "            EC.presence_of_element_located(\n", "                (By.CSS_SELECTOR, 'input[type=submit][value=中文]')\n", "            )\n", "        )\n", "        \n", "        # 點按首頁\n", "        driver.find_element(By.CSS_SELECTOR, 'input[type=submit][value=中文]').click()\n", "        \n", "        # 等待目標元素出現\n", "        WebDriverWait(driver, 10).until(\n", "            EC.presence_of_element_located(\n", "                (By.CSS_SELECTOR, 'div.Login_Data a[href*=logout]')\n", "            )\n", "        )\n", "        \n", "        # 轉向至實際走訪頁面\n", "        driver.get(url)\n", "        \n", "        # 等待\n", "        sleep(randint(1,2))\n", "        \n", "        # 按下設定分頁數的連結\n", "        driver.find_element(By.CSS_SELECTOR, 'span.control_select_off > a[onclick*=\"page\"]').click()\n", "    except TimeoutException as e:\n", "        print('等待逾時: visit')\n", "    \n", "# 剖析元素資料\n", "def parse():\n", "    global driver\n", "    \n", "    try:\n", "        # 刷新頁面\n", "        driver.refresh()\n", "        \n", "        # 等待目標元素出現\n", "        WebDriverWait(driver, 10).until(\n", "            EC.presence_of_element_located(\n", "                (By.CSS_SELECTOR, 'select[onchange*=pageJump]')\n", "            )\n", "        )\n", "        \n", "        # 計算 select 底下的 option 數量\n", "        numPages = len(driver.find_elements(By.CSS_SELECTOR, 'select[onchange*=pageJump]')[0].find_elements(By.CSS_SELECTOR, 'option'))\n", "        \n", "        # 切換分頁用的變數 (zero-based)\n", "        idx = 0\n", "        \n", "        # 走訪分頁\n", "        while idx < numPages:\n", "            # 取得 select\n", "            sel_elm = Select(driver.find_elements(By.CSS_SELECTOR, 'select[onchange*=pageJump]')[0])\n", "            \n", "            # 切換分頁\n", "            sel_elm.select_by_index(idx)\n", "            \n", "            # 等待\n", "            sleep(randint(1,2))\n", "            \n", "            # 等待目標元素出現\n", "            WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located(\n", "                    (By.CSS_SELECTOR, 'div.DataList')\n", "                )\n", "            )\n", "            \n", "            # 過濾刊名與出版日期\n", "            regexOrigin = r'(.+)（(\\d{4}年\\d{1,2}月)）'\n", "            \n", "            # 取得每一個\n", "            for div in driver.find_elements(By.CSS_SELECTOR, 'div.DataList'):\n", "                # 取得底下所有 div\n", "                div_elms = div.find_elements(By.CSS_SELECTOR, 'div')\n", "                \n", "                # 篇名(中文)\n", "                a_cht_title_elm = div_elms[0].find_element(By.CSS_SELECTOR, 'div.title_area span.data_ctitle a[href*=record]')\n", "                strJournalChtName = a_cht_title_elm.get_attribute('innerText').strip()\n", "                \n", "                # 篇名(英文)\n", "                a_eng_title_elm = div_elms[0].find_element(By.CSS_SELECTOR, 'div.title_area span.data_etitle a[href*=record]')\n", "                strJournalEngName = a_eng_title_elm.get_attribute('innerText').strip()\n", "                \n", "                # 取得 出處(刊名)、作者 等元素\n", "                div_field_value_elms = div_elms[0].find_elements(By.CSS_SELECTOR, 'div.class_area div.field_value')\n", "                \n", "                # 刊名、出版日期\n", "                strOrigin = div_field_value_elms[0].get_attribute('innerText').strip()\n", "                strOrigin = re.sub(r'\\/|\\n', ' ', strOrigin)\n", "                matchOrigin = re.search(regex<PERSON><PERSON><PERSON>, strO<PERSON>in)\n", "                strJournalTitle = matchOrigin[1]\n", "                strPublishDate = matchOrigin[2]\n", "             \n", "                # 作者\n", "                strAuthor = div_field_value_elms[1].get_attribute('innerText').strip()\n", "                \n", "                # 連結\n", "                strLink = a_cht_title_elm.get_attribute('href')\n", "                \n", "                # 整理資料\n", "                listData.append({\n", "                    'id': md5(strJournalTitle + strPublishDate + strJournalChtName + strJournalEngName + strAuthor),\n", "                    'journal_title': strJournalTitle,\n", "                    'publish_date': strPublishDate,\n", "                    'journal_name_cht': strJournalChtName,\n", "                    'journal_name_eng': strJournalEngName,\n", "                    'author': s<PERSON><PERSON><PERSON><PERSON>,\n", "                    'link': strLink\n", "                })\n", "                \n", "            \n", "            # 等待\n", "            sleep(1)\n", "            \n", "            # 累計下個分頁數\n", "            idx += 1\n", "        \n", "    except TimeoutException as e:\n", "        print('等待逾時: parse')\n", "    \n", "# 取得 pdf\n", "def getPdf():\n", "    global driver, listData\n", "    \n", "    try:        \n", "        # 回到首頁\n", "        driver.get(url)\n", "        \n", "        # 等待\n", "        sleep(1)\n", "        \n", "        # 走訪每一個內頁\n", "        for index, myDict in enumerate(listData):\n", "            # 進入內頁\n", "            driver.get(myDict['link'])\n", "            \n", "            # 有 pdf viewer 的話，則進行 pdf 下載流程\n", "            if len(driver.find_elements(By.CSS_SELECTOR, 'div#PDF_View object')) > 0:\n", "                # 取得 pdf viewer 的連結\n", "                object_elm = driver.find_element(By.CSS_SELECTOR, 'div#PDF_View object')\n", "                strPdfLink = object_elm.get_attribute('data')\n", "\n", "                # 開 tab 下載 pdf\n", "                driver.execute_script(f'window.open(\"{strPdfLink}\", \"_blank\");')\n", "\n", "                # 回到原來的 tab\n", "                driver.switch_to.window(driver.window_handles[0])\n", "\n", "                # 等檔案下載一下，讓下載路徑至少有 .crdownload\n", "                sleep(5)\n", "\n", "                # 檔案下載完後，再改檔名\n", "                while True:\n", "                    # 取得最後下載的檔案名稱(含絕對路徑)\n", "                    filename = max([os.path.join(fullDownloadPath, f) for f in os.listdir(fullDownloadPath)], key=lambda f : os.path.getmtime(os.path.join(fullDownloadPath, f)))\n", "                    \n", "                    # 若檔案還是 .part，則繼續等待\n", "                    if 'crdownload' in filename:\n", "                        sleep(5)\n", "                    else:\n", "                        # 修改檔名\n", "                        shutil.move(filename, os.path.join(fullDownloadPath, f'{myDict[\"id\"]}.pdf'))\n", "                        sleep(5)\n", "                        break\n", "                        \n", "    except TimeoutException as e:\n", "        print('等待逾時: getPdf')\n", "        \n", "# 關閉瀏覽器\n", "def close():\n", "    global driver\n", "    driver.quit()\n", "        \n", "# 儲存成 json\n", "def save<PERSON><PERSON>():\n", "    global listData\n", "    with open(f\"{folderPath}/{jsonFileName}\", \"w\", encoding=\"utf-8\") as file:\n", "        file.write( json.dumps( listData, ensure_ascii=False, indent=4 ) )\n", "    \n", "# 儲存 .db\n", "def saveDB():\n", "    with open(f\"{folderPath}/{jsonFileName}\", \"r\", encoding=\"utf-8\") as file:      \n", "        # 取得 json 內容\n", "        strJson = file.read()\n", "        \n", "        # 將 json 轉成 list\n", "        listJson = json.loads(strJson)\n", "        \n", "    # 寫入對話記錄\n", "    conn = sqlite3.connect(f\"{folderPath}/{folderName}.db\")\n", "    \n", "    # 建立 cursor 物件\n", "    cursor = conn.cursor()\n", "\n", "    # 執行 SQL 語法\n", "    try:\n", "        # 查詢特定資料，看看是否已經存在於資料表當中\n", "        sql_query = f'''\n", "        SELECT 1\n", "        FROM journals\n", "        WHERE id = ?\n", "        '''\n", "        \n", "        # 寫入資料\n", "        sql_insert = f'''\n", "        INSERT INTO journals (\n", "            id, journal_title, publish_date, journal_name_cht, journal_name_eng, \n", "            author, link, pdf_link, is_downloaded, created_at, \n", "            updated_at\n", "        ) VALUES ( \n", "            ?,?,?,?,?,\n", "            ?,?,?,?,?,\n", "            ?\n", "        )\n", "        '''\n", "        \n", "        # 放置準備寫入的資料\n", "        list_insert = []\n", "        \n", "        # 將 json 資料一筆一筆找出來\n", "        for myDict in list<PERSON><PERSON>:\n", "            # 如果資料庫沒有這筆資料(透過 id)，則將資料以 tuple 格式放到 list 當中，方便新增 bulk 資料\n", "            if cursor.execute(sql_query, (myDict[\"id\"],)).fetchone() == None:\n", "                # 整合所有需要寫入的資料\n", "                list_insert.append((\n", "                    myDict['id'],\n", "                    myDict['journal_title'],\n", "                    myDict['publish_date'],\n", "                    myDict['journal_name_cht'],\n", "                    myDict['journal_name_eng'],\n", "                    myDict['author'],\n", "                    myDict['link'],\n", "                    myDict['link'],\n", "                    0,\n", "                    datetime.today().strftime(\"%Y-%m-%d %H-%M-%S\"),\n", "                    datetime.today().strftime(\"%Y-%m-%d %H-%M-%S\")\n", "                ))\n", "        \n", "        # 新增資料到資料庫當中\n", "        cursor.executemany(sql_insert, list_insert)\n", "        \n", "        # 執行 SQL 語法\n", "        conn.commit()\n", "    except sqlite3.<PERSON>rror as err: \n", "        # 回滾\n", "        conn.rollback()\n", "\n", "        # SQLite3 例外處理\n", "        exc_type, exc_value, exc_tb = sys.exc_info()\n", "        strErrorMsg = f'''SQLite error: {' '.join(err.args)}\\n\\n\n", "        SQLite traceback: {traceback.format_exception(exc_type, exc_value, exc_tb)}\n", "        '''\n", "        print(strErrorMsg)\n", "    finally:\n", "        # 關閉 sqlite\n", "        conn.close()\n", "    \n", "# 確認 pdf 檔案狀況\n", "def checkPdf():\n", "    # 寫入對話記錄\n", "    conn = sqlite3.connect(f\"{folderPath}/{folderName}.db\")\n", "    \n", "    # 將查詢出來的結果 (tuple)，變成 key-value 型式 (dict)\n", "    conn.row_factory = sqlite3.Row\n", "    \n", "    # 建立 cursor 物件\n", "    cursor = conn.cursor()\n", "\n", "    # 執行 SQL 語法\n", "    try:\n", "        # 查詢尚未確認的資料\n", "        sql_query = f'''\n", "        SELECT sn, id, pdf_link\n", "        FROM journals\n", "        WHERE `is_downloaded` = 0\n", "        '''\n", "        \n", "        # 更新資料的欄位(是否下載過)\n", "        sql_update = f'''\n", "        UPDATE `journals` \n", "        SET \n", "            `is_downloaded` = 1 ,\n", "            `updated_at` = ?\n", "        WHERE `id` = ?\n", "        '''\n", "            \n", "        # 取得所有 is_downloaded = 0 的資料\n", "        for myDict in cursor.execute(sql_query).fetchall():\n", "            # 開啟 pdf 檔案\n", "            parsed_pdf = parser.from_file(f'{folderPath}/{myDict[\"id\"]}.pdf')\n", "\n", "            # 若 pdf 可以開啟，則代表檔案可用\n", "            if parsed_pdf['content'] != None:\n", "                # 將 is_downloaded 改成 1，代表下載檔案可用\n", "                cursor.execute(sql_update, (datetime.today().strftime(\"%Y-%m-%d %H-%M-%S\"), myDict[\"id\"],))\n", "                \n", "        conn.commit()\n", "    except sqlite3.<PERSON>rror as err: \n", "        # 回滾\n", "        conn.rollback()\n", "\n", "        # SQLite3 例外處理\n", "        exc_type, exc_value, exc_tb = sys.exc_info()\n", "        strErrorMsg = f'''SQLite error: {' '.join(err.args)}\\n\\n\n", "        SQLite traceback: {traceback.format_exception(exc_type, exc_value, exc_tb)}\n", "        '''\n", "        print(strErrorMsg)\n", "    finally:\n", "        # 關閉 sqlite\n", "        conn.close()"]}, {"cell_type": "code", "execution_count": null, "id": "aba7def4", "metadata": {}, "outputs": [], "source": ["# 初始化 Web Driver\n", "init()"]}, {"cell_type": "code", "execution_count": null, "id": "123e8560", "metadata": {}, "outputs": [], "source": ["# 走訪頁面\n", "visit()"]}, {"cell_type": "code", "execution_count": null, "id": "52577f04", "metadata": {}, "outputs": [], "source": ["# 剖析元素資料\n", "parse()"]}, {"cell_type": "code", "execution_count": null, "id": "14a46b6a", "metadata": {}, "outputs": [], "source": ["# 取得 pdf (總共執行了 2515.2226753234863 秒)\n", "time_begin = time.time()\n", "getPdf()\n", "time_end = time.time()\n", "print(f\"總共執行了 { time_end - time_begin } 秒\")"]}, {"cell_type": "code", "execution_count": null, "id": "ebc9ef26", "metadata": {}, "outputs": [], "source": ["# 關閉瀏覽器\n", "close()"]}, {"cell_type": "code", "execution_count": null, "id": "9adfb482", "metadata": {}, "outputs": [], "source": ["# 儲存成 json\n", "<PERSON><PERSON><PERSON>()"]}, {"cell_type": "code", "execution_count": null, "id": "ffede2dd", "metadata": {}, "outputs": [], "source": ["# 儲存 .db\n", "saveDB()"]}, {"cell_type": "code", "execution_count": null, "id": "199bd4a7", "metadata": {}, "outputs": [], "source": ["# 確認 pdf 檔案狀況 (總共執行了 91.64600825309753 秒)\n", "time_begin = time.time()\n", "checkPdf()\n", "time_end = time.time()\n", "print(f\"總共執行了 { time_end - time_begin } 秒\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}}, "nbformat": 4, "nbformat_minor": 5}