{"cells": [{"cell_type": "markdown", "id": "1bcf22b4", "metadata": {}, "source": ["![urllib.parse.urlsplit用法](https://i.imgur.com/pezosFp.png \"urllib.parse.urlsplit(urlstring, scheme='', allow_fragments=True)\")"]}, {"cell_type": "code", "execution_count": 6, "id": "749d45f9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SplitResult(scheme='https', netloc='video.ftpe7-2.fna.fbcdn.net', path='/v/t39.25447-2/10000000_577277956950365_2254753586110660345_n.webm', query='_nc_cat=104&ccb=1-5&_nc_sid=5aebc0&efg=eyJ2ZW5jb2RlX3RhZyI6ImRhc2hfdnA5XzVzZWNnb3BfbWlucmVzX2hhbG9fNzUwa19mcmFnXzJfdmlkZW8ifQ%3D%3D&_nc_ohc=vEQcdJfBmacAX_sV09z&_nc_ht=video.ftpe7-2.fna&oh=c6a17421e11fb82dd4b3467a6cb3a199&oe=6165D594&bytestart=0&byteend=34000000', fragment='')\n", "==================================================\n", "'_nc_cat=104&ccb=1-5&_nc_sid=5aebc0&efg=eyJ2ZW5jb2RlX3RhZyI6ImRhc2hfdnA5XzVzZWNnb3BfbWlucmVzX2hhbG9fNzUwa19mcmFnXzJfdmlkZW8ifQ%3D%3D&_nc_ohc=vEQcdJfBmacAX_sV09z&_nc_ht=video.ftpe7-2.fna&oh=c6a17421e11fb82dd4b3467a6cb3a199&oe=6165D594&bytestart=0&byteend=34000000'\n", "==================================================\n", "[('_nc_cat', '104'),\n", " ('ccb', '1-5'),\n", " ('_nc_sid', '5aebc0'),\n", " ('efg',\n", "  'eyJ2ZW5jb2RlX3RhZyI6ImRhc2hfdnA5XzVzZWNnb3BfbWlucmVzX2hhbG9fNzUwa19mcmFnXzJfdmlkZW8ifQ=='),\n", " ('_nc_ohc', 'vEQcdJfBmacAX_sV09z'),\n", " ('_nc_ht', 'video.ftpe7-2.fna'),\n", " ('oh', 'c6a17421e11fb82dd4b3467a6cb3a199'),\n", " ('oe', '6165D594'),\n", " ('bytestart', '0'),\n", " ('byteend', '34000000')]\n", "==================================================\n", "{'_nc_cat': '104',\n", " '_nc_ht': 'video.ftpe7-2.fna',\n", " '_nc_ohc': 'vEQcdJfBmacAX_sV09z',\n", " '_nc_sid': '5aebc0',\n", " 'byteend': '34000000',\n", " 'bytestart': '0',\n", " 'ccb': '1-5',\n", " 'efg': 'eyJ2ZW5jb2RlX3RhZyI6ImRhc2hfdnA5XzVzZWNnb3BfbWlucmVzX2hhbG9fNzUwa19mcmFnXzJfdmlkZW8ifQ==',\n", " 'oe': '6165D594',\n", " 'oh': 'c6a17421e11fb82dd4b3467a6cb3a199'}\n"]}], "source": ["'''\n", "練習資料來源: 臉書粉絲團的 Video\n", "https://www.facebook.com/JesseTang11/videos\n", "'''\n", "\n", "from pprint import pprint\n", "from urllib import parse\n", "\n", "'''\n", "1. 透過 parse.urlsplit 取得 SplitResult 物件\n", "2. 透過 parse.urlsplit(url).query 取得 Query String\n", "3. parse.parse_qsl(parse.urlsplit(url).query) 轉成 tuple\n", "4. dict(parse.parse_qsl(parse.urlsplit(url).query)) 將 Query String 轉成 dict 型態\n", "'''\n", "\n", "# 將 query string 變成 dict 格式\n", "url = 'https://video.ftpe7-2.fna.fbcdn.net/v/t39.25447-2/10000000_577277956950365_2254753586110660345_n.webm?_nc_cat=104&ccb=1-5&_nc_sid=5aebc0&efg=eyJ2ZW5jb2RlX3RhZyI6ImRhc2hfdnA5XzVzZWNnb3BfbWlucmVzX2hhbG9fNzUwa19mcmFnXzJfdmlkZW8ifQ%3D%3D&_nc_ohc=vEQcdJfBmacAX_sV09z&_nc_ht=video.ftpe7-2.fna&oh=c6a17421e11fb82dd4b3467a6cb3a199&oe=6165D594&bytestart=0&byteend=34000000'\n", "\n", "# 變成 SplitResult 物件\n", "sr = parse.urlsplit(url)\n", "pprint( sr )\n", "\n", "print(\"=\" * 50)\n", "\n", "# 取得 SplitResult 物件其中 query 屬性的值\n", "pprint( sr.query )\n", "\n", "print(\"=\" * 50)\n", "\n", "# 將 query 屬性的值，其中的 key-value 字串格式轉為 tuple \n", "pprint( parse.parse_qsl(sr.query) )\n", "\n", "print(\"=\" * 50)\n", "\n", "# 將所有 tuple 一起轉換成 dict 格式\n", "pprint( dict(parse.parse_qsl(sr.query)) )"]}, {"cell_type": "code", "execution_count": 8, "id": "06a97aa3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["https://video.ftpe7-2.fna.fbcdn.net/v/t39.25447-2/10000000_577277956950365_2254753586110660345_n.webm?_nc_cat=104&_nc_ht=video.ftpe7-2.fna&_nc_ohc=vEQcdJfBmacAX_sV09z&_nc_sid=5aebc0&byteend=34000000&bytestart=0&ccb=1-5&efg=eyJ2ZW5jb2RlX3RhZyI6ImRhc2hfdnA5XzVzZWNnb3BfbWlucmVzX2hhbG9fNzUwa19mcmFnXzJfdmlkZW8ifQ%3D%3D&oe=6165D594&oh=c6a17421e11fb82dd4b3467a6cb3a199\n"]}], "source": ["'''將 query string (dict 格式)，整合在自訂的網址後面'''\n", "\n", "dictQuery = {'_nc_cat': '104',\n", " '_nc_ht': 'video.ftpe7-2.fna',\n", " '_nc_ohc': 'vEQcdJfBmacAX_sV09z',\n", " '_nc_sid': '5aebc0',\n", " 'byteend': '34000000',\n", " 'bytestart': '0',\n", " 'ccb': '1-5',\n", " 'efg': 'eyJ2ZW5jb2RlX3RhZyI6ImRhc2hfdnA5XzVzZWNnb3BfbWlucmVzX2hhbG9fNzUwa19mcmFnXzJfdmlkZW8ifQ==',\n", " 'oe': '6165D594',\n", " 'oh': 'c6a17421e11fb82dd4b3467a6cb3a199'}\n", "\n", "url = f\"{sr.scheme}://{sr.netloc}{sr.path}\"\n", "\n", "full_url = url + '?' + parse.urlencode(dictQuery)\n", "\n", "print(full_url)"]}, {"cell_type": "code", "execution_count": 1, "id": "daf33dcc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: browsermob-proxy in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (0.8.0)\n", "Requirement already satisfied: requests>=2.9.1 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from browsermob-proxy) (2.25.1)\n", "Requirement already satisfied: certifi>=2017.4.17 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from requests>=2.9.1->browsermob-proxy) (2020.12.5)\n", "Requirement already satisfied: idna<3,>=2.5 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from requests>=2.9.1->browsermob-proxy) (2.10)\n", "Requirement already satisfied: urllib3<1.27,>=1.21.1 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from requests>=2.9.1->browsermob-proxy) (1.26.4)\n", "Requirement already satisfied: chardet<5,>=3.0.2 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from requests>=2.9.1->browsermob-proxy) (4.0.0)\n"]}], "source": ["'''\n", "1. 下載 browsermob-proxy (需要有 Java 執行環境)，放在專案目錄下\n", "網址: https://github.com/lightbody/browsermob-proxy/releases/tag/browsermob-proxy-2.1.4\n", "連結: https://github.com/lightbody/browsermob-proxy/releases/download/browsermob-proxy-2.1.4/browsermob-proxy-2.1.4-bin.zip\n", "\n", "2. 下載 ffmpeg，放在專案目錄下\n", "網址: https://ffmpeg.org/download.html\n", "連結 1: https://www.gyan.dev/ffmpeg/builds/ffmpeg-release-full.7z\n", "連結 2: https://www.gyan.dev/ffmpeg/builds/ffmpeg-release-essentials.zip\n", "說明: 解壓縮後，把資料夾名稱改為「ffmpeg」\n", "\n", "3. 執行下方的套件安裝指令:\n", "'''\n", "!pip install browsermob-proxy"]}, {"cell_type": "code", "execution_count": 2, "id": "********", "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "# 先建立 dict 格式的 account.json 檔，並取得其中的帳密資料\n", "file = open(\"./account.json\", \"r\", encoding=\"utf-8\")\n", "strJson = file.read()\n", "dictJson = json.loads(strJson)"]}, {"cell_type": "code", "execution_count": 7, "id": "e554ae8f", "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["網址: https://video.ftpe7-2.fna.fbcdn.net/v/t39.25447-2/10000000_577277956950365_2254753586110660345_n.webm?_nc_cat=104&ccb=1-5&_nc_sid=5aebc0&efg=eyJ2ZW5jb2RlX3RhZyI6ImRhc2hfdnA5XzVzZWNnb3BfbWlucmVzX2hhbG9fNzUwa19mcmFnXzJfdmlkZW8ifQ%3D%3D&_nc_ohc=9FYK90hG2n0AX8l9LNt&_nc_ht=video.ftpe7-2.fna&oh=4d830406ac7665653d590a8aa8252225&oe=615FE6D4&bytestart=0&byteend=9999999999999999999999\n", "秒數: 362.200000\n", "寬高: 1280,720\n", "========================= 分隔線 =========================\n", "網址: https://video.ftpe7-2.fna.fbcdn.net/v/t42.1790-2/243828531_1279392475850998_5759680787346630683_n.mp4?_nc_cat=109&ccb=1-5&_nc_sid=5aebc0&efg=eyJ2ZW5jb2RlX3RhZyI6ImRhc2hfYXVkaW9fYWFjcF82NF9mcmFnXzJfYXVkaW8ifQ%3D%3D&_nc_ohc=0PTW1HOITqkAX8u4zDf&_nc_ht=video.ftpe7-2.fna&oh=b81c2328929941de2a7be1cf1487e975&oe=615B3163&bytestart=0&byteend=9999999999999999999999\n", "秒數: 362.219000\n", "寬高: \n", "========================= 分隔線 =========================\n", "{362: ['https://video.ftpe7-2.fna.fbcdn.net/v/t39.25447-2/10000000_577277956950365_2254753586110660345_n.webm?_nc_cat=104&ccb=1-5&_nc_sid=5aebc0&efg=eyJ2ZW5jb2RlX3RhZyI6ImRhc2hfdnA5XzVzZWNnb3BfbWlucmVzX2hhbG9fNzUwa19mcmFnXzJfdmlkZW8ifQ%3D%3D&_nc_ohc=9FYK90hG2n0AX8l9LNt&_nc_ht=video.ftpe7-2.fna&oh=4d830406ac7665653d590a8aa8252225&oe=615FE6D4&bytestart=0&byteend=9999999999999999999999',\n", "       'https://video.ftpe7-2.fna.fbcdn.net/v/t42.1790-2/243828531_1279392475850998_5759680787346630683_n.mp4?_nc_cat=109&ccb=1-5&_nc_sid=5aebc0&efg=eyJ2ZW5jb2RlX3RhZyI6ImRhc2hfYXVkaW9fYWFjcF82NF9mcmFnXzJfYXVkaW8ifQ%3D%3D&_nc_ohc=0PTW1HOITqkAX8u4zDf&_nc_ht=video.ftpe7-2.fna&oh=b81c2328929941de2a7be1cf1487e975&oe=615B3163&bytestart=0&byteend=9999999999999999999999']}\n"]}], "source": ["'''\n", "[1] Selenium with Python 中文翻譯文檔\n", "參考網頁：https://selenium-python-zh.readthedocs.io/en/latest/index.html\n", "[2] selenium 啓動 Chrome 的進階配置參數\n", "參考網址：https://stackoverflow.max-everyday.com/2019/12/selenium-chrome-options/\n", "[3] Mouse Hover Action in Selenium\n", "參考網址：https://www.toolsqa.com/selenium-webdriver/mouse-hover-action/\n", "[4] browsermob-proxy-2.1.5\n", "參考網址：https://github.com/lightbody/browsermob-proxy/releases\n", "[5] How Do I Reset The Har File Used By Browser-Mob-Proxy Module For Python?\n", "參考網頁：https://stackoverflow.com/questions/56742167/how-do-i-reset-the-har-file-used-by-browser-mob-proxy-module-for-python\n", "[6] [Tool] FFprobe 教學\n", "參考網頁：https://zwindr.blogspot.com/2016/08/tool-ffprobe.html\n", "[7] 每天學習一個命令：ffprobe 查看多媒體信息\n", "參考網頁：https://einverne.github.io/post/2015/02/ffprobe-show-media-info.html\n", "[8] How do I use ffmpeg to get the video resolution?\n", "參考網頁：https://superuser.com/questions/841235/how-do-i-use-ffmpeg-to-get-the-video-resolution\n", "[9] subprocess---子進程管理\n", "參考網頁：https://docs.python.org/zh-cn/3.8/library/subprocess.html\n", "[10] YouTube影片下載（四）：透過subprocess.run()執行外部命令\n", "參考網頁：https://swf.com.tw/?p=1369\n", "[11] JSON Formatter & Validator\n", "參考網頁：https://jsonformatter.curiousconcept.com/\n", "'''\n", "\n", "'''\n", "匯入套件\n", "'''\n", "# 操作 browser 的 API\n", "from selenium import webdriver\n", "\n", "# 處理逾時例外的工具\n", "from selenium.common.exceptions import TimeoutException\n", "\n", "# 面對動態網頁，等待某個元素出現的工具，通常與 exptected_conditions 搭配\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "\n", "# 搭配 WebDriverWait 使用，對元素狀態的一種期待條件，若條件發生，則等待結束，往下一行執行\n", "from selenium.webdriver.support import expected_conditions as EC\n", "\n", "# 期待元素出現要透過什麼方式指定，通常與 EC、WebDriverWait 一起使用\n", "from selenium.webdriver.common.by import By\n", "\n", "# 強制等待 (執行期間休息一下)\n", "from time import sleep\n", "\n", "# 整理 json 使用的工具\n", "import json\n", "\n", "# 執行 command 的時候用的\n", "import os\n", "\n", "# 引入 regular expression 工具\n", "import re\n", "\n", "# 輸出排版美化的工具\n", "from pprint import pprint\n", "\n", "# 瀏覽器代理工具\n", "from browsermobproxy import Server\n", "\n", "# 剖析網址資訊\n", "from urllib import parse\n", "\n", "# 正規表達式\n", "import re\n", "\n", "# 子處理程序，用來取代 os.system 的功能\n", "import subprocess\n", "'''\n", "啟動瀏覽器工具的選項\n", "'''\n", "# 選項初始設定\n", "options = webdriver.ChromeOptions()\n", "# options.add_argument(\"--headless\")                #不開啟實體瀏覽器背景執行\n", "options.add_argument(\"--start-maximized\")         #最大化視窗\n", "options.add_argument(\"--incognito\")               #開啟無痕模式\n", "options.add_argument(\"--disable-popup-blocking \") #禁用彈出攔截\n", "\n", "# 啟動 proxy server 與 proxy client\n", "dictSetting = {'port': 8090}\n", "server = Server(\n", "    path = '.\\\\browsermob-proxy-2.1.4\\\\bin\\\\browsermob-proxy.bat',\n", "    options = dictSetting\n", ")\n", "server.start()\n", "proxy = server.create_proxy()\n", "\n", "# 忽略認證錯誤訊息，以及加入自訂的 proxy\n", "options.add_argument(\"--ignore-certificate-errors\")\n", "options.add_argument(f\"--proxy-server={proxy.proxy}\")\n", "\n", "# chrome 執行檔路徑 (在 unix-like 環境要用 / 這個斜線)\n", "executable_path = os.getcwd() + \"\\\\\" + \"chromedriver.exe\" \n", "\n", "# 使用 Chrome 的 WebDriver (含 options, executable_path)\n", "driver = webdriver.Chrome(\n", "    options = options, \n", "    executable_path = executable_path\n", ")\n", "\n", "'''\n", "自訂變數\n", "'''\n", "# 粉絲團特定影音的網址\n", "url = 'https://www.facebook.com/JesseTang11/videos/1462410270797449'\n", "\n", "# 放置處理過程中的資料\n", "listData = []\n", "\n", "# 放置最終處理完的資料\n", "listResult = []\n", "\n", "# 去除重複用\n", "setTmp = set()\n", "\n", "# 將相關的影音連結整合在一起的 dict\n", "dictGroupLink = {}\n", "\n", "'''\n", "整個程式執行完的結果：用秒數作為 key，裡面都是相同秒數的「影」或「音」\n", "{\n", "   \"181\":[\n", "      \"https://video.ftpe7-1.fna.fbcdn.net/v/t39.25447-2/10000000_123879266400340_8051677204819909739_n.webm?_nc_cat=106&ccb=1-3&_nc_sid=5aebc0&efg=eyJ2ZW5jb2RlX3RhZyI6ImRhc2hfdnA5XzVzZWNnb3BfbWlucmVzX2hhbG9fMTUwMGtfZnJhZ18yX3ZpZGVvIn0%3D&_nc_ohc=NUEnVTkF_FIAX8veJWz&_nc_ht=video.ftpe7-1.fna&oh=ae1151a66ac2409c9722bfa081fe07fa&oe=60DF3727&bytestart=0&byteend=9999999999999999999999\",\n", "      \"https://video.ftpe7-2.fna.fbcdn.net/v/t39.25447-2/10000000_770205917032788_7081845633667863868_n.webm?_nc_cat=111&ccb=1-3&_nc_sid=5aebc0&efg=eyJ2ZW5jb2RlX3RhZyI6ImRhc2hfdnA5XzVzZWNnb3BfbWlucmVzX2hhbG9fNzUwa19mcmFnXzJfdmlkZW8ifQ%3D%3D&_nc_ohc=erO227PpPwUAX8DwM_O&_nc_ht=video.ftpe7-2.fna&oh=43db04165e915ea3eedaa71ba1b74901&oe=60E04799&bytestart=0&byteend=9999999999999999999999\",\n", "      \"https://video.ftpe7-2.fna.fbcdn.net/v/t39.25447-2/176180580_511295376893144_6196517561985382824_n.webm?_nc_cat=111&ccb=1-3&_nc_sid=5aebc0&efg=eyJ2ZW5jb2RlX3RhZyI6ImRhc2hfdnA5XzVzZWNnb3BfbWlucmVzX2hhbG9fMjEwa19mcmFnXzJfdmlkZW8ifQ%3D%3D&_nc_ohc=GfPqAvOuYagAX8JK8kF&_nc_ht=video.ftpe7-2.fna&oh=1d6267b7a8eb663abc0d08b62b91dac8&oe=60DFDBF2&bytestart=0&byteend=9999999999999999999999\",\n", "      \"https://video.ftpe7-2.fna.fbcdn.net/v/t42.1790-2/140608276_485527249512640_7357614482142518882_n.mp4?_nc_cat=109&ccb=1-3&_nc_sid=5aebc0&efg=eyJ2ZW5jb2RlX3RhZyI6ImRhc2hfYXVkaW9fYWFjcF82NF9mcmFnXzJfYXVkaW8ifQ%3D%3D&_nc_ohc=axugawuIRVgAX-l922B&_nc_ht=video.ftpe7-2.fna&oh=4469f6f60c872db15e5c1aa42d3fd219&oe=60DB513B&bytestart=0&byteend=9999999999999999999999\",\n", "      \"https://video.ftpe7-1.fna.fbcdn.net/v/t39.25447-2/177744476_1226077594473468_7179738070189068103_n.webm?_nc_cat=110&ccb=1-3&_nc_sid=5aebc0&efg=eyJ2ZW5jb2RlX3RhZyI6ImRhc2hfdnA5XzVzZWNnb3BfbWlucmVzX2hhbG9fOTVrX2ZyYWdfMl92aWRlbyJ9&_nc_ohc=2T53q2MdCuQAX8_Zedd&_nc_ht=video.ftpe7-1.fna&oh=03108768bfa5b90b8f16785b1359610b&oe=60E07551&bytestart=0&byteend=9999999999999999999999\",\n", "      \"https://video.ftpe7-3.fna.fbcdn.net/v/t39.25447-2/175666063_3384152905018455_7702029337104211420_n.webm?_nc_cat=108&ccb=1-3&_nc_sid=5aebc0&efg=eyJ2ZW5jb2RlX3RhZyI6ImRhc2hfdnA5XzVzZWNnb3BfbWlucmVzX2hhbG9fNDAwa19mcmFnXzJfdmlkZW8ifQ%3D%3D&_nc_ohc=3prrnYDlMXsAX-GW3Qb&_nc_ht=video.ftpe7-3.fna&oh=7286cecf37a18ab81c0c141cdfcf447b&oe=60E0E4DA&bytestart=0&byteend=9999999999999999999999\"\n", "   ],\n", "   \"278\":[\n", "      \"https://video.ftpe7-2.fna.fbcdn.net/v/t39.25447-2/10000000_2880432285542824_6660762804273239231_n.webm?_nc_cat=104&ccb=1-3&_nc_sid=5aebc0&efg=eyJ2ZW5jb2RlX3RhZyI6ImRhc2hfdnA5XzVzZWNnb3BfbWlucmVzX2hhbG9fNDAwa19mcmFnXzJfdmlkZW8ifQ%3D%3D&_nc_ohc=_huIxLPplXkAX_zrKWd&_nc_ht=video.ftpe7-2.fna&oh=e637120b12ff9bcb3351fec43bdf6219&oe=60E08346&bytestart=0&byteend=9999999999999999999999\",\n", "      \"https://video.ftpe7-4.fna.fbcdn.net/v/t39.25447-2/209317404_101266505532763_8948422060299658210_n.webm?_nc_cat=101&ccb=1-3&_nc_sid=5aebc0&efg=eyJ2ZW5jb2RlX3RhZyI6ImRhc2hfdnA5XzVzZWNnb3BfbWlucmVzX2hhbG9fOTVrX2ZyYWdfMl92aWRlbyJ9&_nc_ohc=jcdnh5n023cAX87WLiJ&_nc_ht=video.ftpe7-4.fna&oh=236a16401c296104fca639ed42cfcc15&oe=60E10ECA&bytestart=0&byteend=9999999999999999999999\",\n", "      \"https://video.ftpe7-1.fna.fbcdn.net/v/t39.25447-2/207095862_867744707484776_8248974963914107905_n.webm?_nc_cat=100&ccb=1-3&_nc_sid=5aebc0&efg=eyJ2ZW5jb2RlX3RhZyI6ImRhc2hfdnA5XzVzZWNnb3BfbWlucmVzX2hhbG9fMjEwa19mcmFnXzJfdmlkZW8ifQ%3D%3D&_nc_ohc=dm4eA_mdKRAAX8nKN-_&_nc_ht=video.ftpe7-1.fna&oh=eeb80766c52b28b284fed0baa5699a1c&oe=60E09F32&bytestart=0&byteend=9999999999999999999999\",\n", "      \"https://video.ftpe7-2.fna.fbcdn.net/v/t42.1790-2/207382031_3991789930875966_6072804754383815783_n.mp4?_nc_cat=104&ccb=1-3&_nc_sid=5aebc0&efg=eyJ2ZW5jb2RlX3RhZyI6ImRhc2hfYXVkaW9fYWFjcF82NF9mcmFnXzJfYXVkaW8ifQ%3D%3D&_nc_ohc=MSbvEUklBy4AX9XE99I&_nc_ht=video.ftpe7-2.fna&oh=040aedfb676ef6cd3ee11723a0a3898d&oe=60DB580B&bytestart=0&byteend=9999999999999999999999\"\n", "   ]\n", "}\n", "'''\n", "\n", "\n", "# 登入帳號密碼\n", "def login():\n", "    # 前往指定連結\n", "    driver.get('https://www.facebook.com/');\n", "\n", "    # 輸入帳號\n", "    driver.find_element_by_id('email').send_keys(dict<PERSON>son['username'])\n", "\n", "    # 輸入密碼\n", "    driver.find_element_by_id('pass').send_keys(dictJson['password'])\n", "\n", "    # 按下登入/送出鈕\n", "    driver.find_element_by_css_selector('button[type=\"submit\"][name=\"login\"]').click()\n", "       \n", "# 走訪頁面\n", "def visit():\n", "    try:\n", "        # 等待 fb 右上角個人超連結，確認成功登入與否\n", "        WebDriverWait(driver, 15).until(\n", "            EC.presence_of_element_located( \n", "                (By.CSS_SELECTOR, 'a[role=\"link\"]') \n", "            )\n", "        )\n", "        \n", "        # 前往指定連結\n", "        driver.get(url);\n", "        \n", "        #等待影片播放按鈕是否出現在 DOM 元素當中\n", "        WebDriverWait(driver, 30).until(\n", "            EC.presence_of_element_located( \n", "                (By.CSS_SELECTOR, 'div.i09qtzwb.rq0escxv.n7fi1qx3.pmk7jnqg.j9ispegn.kr520xx4.nhd2j8a9[role=\"presentation\"]') \n", "            )\n", "        )\n", "        \n", "        # 代理機制設定\n", "        proxy.new_har('fb_video', options = {\n", "            'captureHeaders': <PERSON><PERSON><PERSON>,\n", "            'captureContent': True\n", "        })\n", "\n", "        # 如果有播放鈕，則點按\n", "#         if len( driver.find_elements(By.CSS_SELECTOR, 'div.i09qtzwb.rq0escxv.n7fi1qx3.pmk7jnqg.j9ispegn.kr520xx4.nhd2j8a9[role=\"presentation\"]') ) > 0:\n", "#             driver.find_elements(By.CSS_SELECTOR, 'div.i09qtzwb.rq0escxv.n7fi1qx3.pmk7jnqg.j9ispegn.kr520xx4.nhd2j8a9[role=\"presentation\"]')[0].click()\n", "        \n", "    except TimeoutException:\n", "        print(\"等待逾時，即將關閉瀏覽器…\")\n", "        sleep(3)\n", "        driver.quit()\n", "    \n", "# 測試 blob\n", "def testBlob():\n", "    dictResult = {}\n", "    \n", "    # 這裡的強制等待比較特別，等越久，取得的 Network 面板資訊愈多 (FB 會一直發出 XmlHttpRequest 請求)\n", "    sleep(10)\n", "    \n", "    # 取得所有請求與回應的資訊\n", "    result = proxy.har\n", "    \n", "    # 走訪每一個 entries\n", "    for entry in result['log']['entries']:\n", "        # 取得每一個 entries 底下的 request\n", "        request = entry['request']\n", "        \n", "        # 將 Query String 轉成 dict\n", "        dictUrl = dict(parse.parse_qsl(parse.urlsplit(request['url']).query))\n", "        \n", "        # 判斷特定 key 是否存在於 dict 當中，若有，則可能包含影音、圖片相關的資訊\n", "        if '_nc_ht' in dictUrl:\n", "            # 取得特定 key 底下的值，並將值進行陣列切割，取得關鍵字詞 (video)\n", "            _nc_ht = dictUrl['_nc_ht']\n", "            \n", "            # 例如取得 video.ftpe7-1.fna，以「,」進行分割後，取其索引為 0 的值\n", "            _nc_ht = _nc_ht.split(\".\")[0]\n", "            \n", "            # 如果關鍵字詞為 video，則\n", "            if _nc_ht == 'video':\n", "                # 設定串流區間 (range)\n", "                dictUrl['bytestart'] = 0\n", "                dictUrl['byteend'] = 9999999999999999999999\n", "                \n", "                # 取得檔案名稱(包括副檔名)\n", "                regex = r\"https?:\\/\\/.+\\/[a-zA-Z0-9_]+\\.(webm|mp4)\"\n", "                match = re.match(regex, request['url'])\n", "                if match != None:\n", "                    # 整合所有影音網址在 list 當中\n", "                    listData.append(f\"{match[0]}?{parse.urlencode(dictUrl)}\")\n", "    \n", "    # 去除重複的 url\n", "    for url in listData:\n", "        setTmp.add(url)\n", "        \n", "    # 把過濾完的 url 各別整理\n", "    for url in setTmp:\n", "        print(f\"網址: {url}\")\n", "        \n", "        # 取得影音時間的工具與指令\n", "        cmd_seconds = [\n", "            r'./ffmpeg/bin/ffprobe.exe', \n", "            '-i', url, \n", "            '-show_entries', 'format=duration', \n", "            '-v', 'quiet', \n", "            '-of', 'csv=p=0'\n", "        ]\n", "        \n", "        # 執行指令\n", "        result_seconds = subprocess.run(cmd_seconds, stdout=subprocess.PIPE, stderr=subprocess.STDOUT)\n", "        \n", "        # 取得影音的秒數\n", "        seconds = result_seconds.stdout.decode('utf-8')\n", "        \n", "        # 去除斷行符號\n", "        seconds = re.sub(\"\\r|\\n\", \"\", seconds)\n", "        \n", "        # 取得影音寬高的工具與指令\n", "        cmd_wh = [\n", "            r'./ffmpeg/bin/ffprobe.exe', \n", "            '-i', url, \n", "            '-show_entries', 'stream=width,height', \n", "            '-v', 'quiet', \n", "            '-of', 'csv=p=0'\n", "        ]\n", "        \n", "        #執行指令\n", "        result_wh = subprocess.run(cmd_wh, stdout=subprocess.PIPE, stderr=subprocess.STDOUT)\n", "        \n", "        # 取得影音寬高\n", "        wh = result_wh.stdout.decode('utf-8')\n", "        \n", "        # 去除斷行符號\n", "        wh = re.sub(\"\\r|\\n\", \"\", wh)\n", "        \n", "        # 顯示秒數與寬高\n", "        print(f\"秒數: {seconds}\")\n", "        print(f\"寬高: {wh}\")\n", "        print(\"=\" * 25, \"分隔線\", \"=\" * 25)\n", "        \n", "        # 將秒數變成 key (去掉小數點)\n", "        key = int(float(seconds))\n", "        \n", "        # 如果以秒數作為 key，並不存在於 dict 當中，則初始化成 list\n", "        if key not in dictGroupLink:\n", "            dictGroupLink[key] = []\n", "            \n", "        # 同一個秒數的影音連結，則歸類在同一個 list 底下\n", "        dictGroupLink[key].append(url)\n", "    \n", "    # 輸出結果檢視\n", "    pprint(dictGroupLink)\n", "    \n", "# 關閉瀏覽器、代理機制\n", "def close():\n", "    driver.quit()\n", "    proxy.close()\n", "    server.stop()\n", "            \n", "'''主程式'''\n", "if __name__ == '__main__':\n", "    login()\n", "    visit()\n", "    testBlob()\n", "    close()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}}, "nbformat": 4, "nbformat_minor": 5}