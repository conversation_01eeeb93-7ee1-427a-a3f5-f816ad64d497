{"cells": [{"cell_type": "code", "execution_count": null, "id": "a958ea25", "metadata": {}, "outputs": [], "source": ["'''\n", "參考網址\n", "1. http://free-proxy.cz/zh/proxylist/country/US/https/uptime/all\n", "2. https://ip.seeip.org/jsonip\n", "3. https://pypi.org/project/fake-useragent/\n", "'''\n", "\n", "\n", "import requests as req\n", "from bs4 import BeautifulSoup as bs\n", "import re, base64\n", "from random import randint\n", "from pprint import pprint\n", "\n", "# 隨機取得 User-Agent\n", "'''\n", "# 從外部資料來取得清單，清單預設儲存路徑: /tmp\n", "ua = UserAgent(use_external_data=True)\n", "# 從外部資料來取得清單，儲存在指定路徑\n", "ua = UserAgent(use_external_data=True, cache_path=/home/<USER>\n", "\n", "更詳細的說明，請見以下網頁:\n", "https://pypi.org/project/fake-useragent/\n", "'''\n", "from fake_useragent import UserAgent\n", "ua = UserAgent(use_external_data=True)\n", "\n", "# 操作 browser 的 API\n", "from selenium import webdriver\n", "from selenium.webdriver.chrome.service import Service\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "# 處理逾時例外的工具\n", "from selenium.common.exceptions import TimeoutException\n", "\n", "# 面對動態網頁，等待某個元素出現的工具，通常與 exptected_conditions 搭配\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "\n", "# 搭配 WebDriverWait 使用，對元素狀態的一種期待條件，若條件發生，則等待結束，往下一行執行\n", "from selenium.webdriver.support import expected_conditions as EC\n", "\n", "# 期待元素出現要透過什麼方式指定，通常與 EC、WebDriverWait 一起使用\n", "from selenium.webdriver.common.by import By\n", "\n", "# 強制等待 (執行期間休息一下)\n", "from time import sleep, time"]}, {"cell_type": "code", "execution_count": null, "id": "f6a36048", "metadata": {}, "outputs": [], "source": ["# 欲抓取資料的網址\n", "domainName = 'https://www.wine-searcher.com'\n", "url = domainName + '/find/mouton+rothschild+pauillac+medoc+bordeaux+france'\n", "\n", "# proxy 設定\n", "my_proxies = {\n", "    \"http\": \"\",\n", "    \"https\": \"\",\n", "}\n", "\n", "# 放置 proxy 列表的變數，每次用完或確認無法使用，就會拋棄，用完了，會再重新請求\n", "listProxyPool = []\n", "\n", "# 啟動瀏覽器工具的選項\n", "my_options = webdriver.ChromeOptions()\n", "# my_options.add_argument(\"--headless\")                #不開啟實體瀏覽器背景執行\n", "my_options.add_argument(\"--start-maximized\")         #最大化視窗\n", "my_options.add_argument(\"--incognito\")               #開啟無痕模式\n", "my_options.add_argument(\"--disable-popup-blocking\") #禁用彈出攔截\n", "my_options.add_argument(\"--disable-notifications\")  #取消通知\n", "my_options.add_argument(\"--lang=zh-TW\")  #設定為正體中文\n", "# my_options.add_argument(f'--proxy-server={ip_port}') # web driver 也可以設定 proxy\n", "\n", "# 使用 Chrome 的 WebDriver\n", "driver = webdriver.Chrome(\n", "    options = my_options,\n", "    service = Service(ChromeDriverManager().install())\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "99353050", "metadata": {}, "outputs": [], "source": ["'''\n", "函式\n", "'''\n", "# 取得 free proxy 列表 (http://free-proxy.cz/en/)\n", "def getProxyPool():\n", "    global listProxyPool\n", "    global my_proxies\n", "    global driver\n", "    \n", "    # 若先前有 proxy pool 資料，則先清空\n", "    listProxyPool = []\n", "    \n", "    # 暫存 ip 列表\n", "    listTmp = []\n", "    \n", "    try:\n", "        # 開始時間\n", "        time_begin = time()\n", "        \n", "        # 走訪分頁\n", "        for page in range(1, 4):\n", "            # 隨機等待\n", "            sleep(randint(2, 5))\n", "            \n", "            # 取得 html\n", "            driver.get(f'http://free-proxy.cz/zh/proxylist/country/all/https/ping/all/{page}')\n", "\n", "            # 等待元素出現\n", "            WebDriverWait(driver, 30).until(\n", "                EC.presence_of_element_located(\n", "                    (By.CSS_SELECTOR, \"table#proxy_list > tbody > tr\")\n", "                )\n", "            )\n", "\n", "            # 建立 soup\n", "            soup = bs(driver.page_source, \"lxml\")\n", "\n", "            # 選擇 table 裡面的所有 tr\n", "            tr_elms = soup.select('table#proxy_list > tbody > tr')\n", "\n", "            # 定義 regex，準備對 ip 進行解碼\n", "            regexIP = r\"Base64\\.decode\\(\\\"(.+?)\\\"\\)\"\n", "\n", "            # 走訪所有 tr\n", "            for tr in tr_elms:\n", "                # 找出 tr 當中的所有 td\n", "                td_elms = tr.select('td')\n", "\n", "                # 對 ip 解碼\n", "                ip = str(td_elms[0])\n", "                match = re.search(regexIP, ip)\n", "                if match == None:\n", "                    continue\n", "                ip = match[1]\n", "                ip = base64.b64decode(ip)\n", "                ip = ip.decode(\"utf-8\")\n", "\n", "                # 找出 port\n", "                port = td_elms[1].get_text().strip()\n", "\n", "                # 整理可用資料\n", "                listTmp.append(f\"{ip}:{port}\")\n", "            \n", "        # 目前有多少 ip 可進行測試\n", "        print(f\"測試 IP 數: {len(listTmp)}\")\n", "\n", "        # 過濾掉當前不可用的 ip:port\n", "        for ip in listTmp:\n", "            try:\n", "                # 設定 proxy\n", "                my_proxies['http'] = ip\n", "                my_proxies['https'] = ip\n", "                \n", "                # 發出請求，取得回應\n", "                res = req.get('https://ip.seeip.org/jsonip', proxies = my_proxies, timeout = 2)\n", "                \n", "                # 回傳 200 ok，代表 ip 暫時可用\n", "                if res.ok:\n", "                    print(f\"可用 IP: {res.json()}\")\n", "\n", "                    # 將可用 ip 加入 proxy pool\n", "                    listProxyPool.append(ip)\n", "                else:\n", "                    print(f\"{ip} 無法使用...\")\n", "            except:\n", "                print(f\"{ip} 無法使用...\")\n", "\n", "        print(f\"可用 IP 列表 ({len(listProxyPool)}):\")\n", "        pprint(listProxyPool)\n", "\n", "        # 計算結束時間\n", "        print(f\"取得可用 IP 列表花費 {time() - time_begin} 秒\")\n", "    except TimeoutException:\n", "        print(\"等待逾時...\")\n", "    \n", "# 取得首頁列表資訊\n", "def checkProxy():\n", "    global listProxyPool\n", "    global my_proxies\n", "    \n", "    print(\"=\" * 15 + \"開始測試請求\" + \"=\" * 15)\n", "    \n", "    # 逐個 ip 測試\n", "    for ip in listProxyPool:\n", "        try:\n", "            print(f\"測試 IP: {ip}\")\n", "            \n", "            # 一個 IP 測 5 次，看看是不是真的可以使用\n", "            for i in range(5):\n", "                # 設定 proxy\n", "                my_proxies['http'] = ip\n", "                my_proxies['https'] = ip\n", "                \n", "                # 設定隨機請求標頭\n", "                my_headers = {'user-agent': ua.random}\n", "                \n", "                # 請求頁面\n", "                res = req.get(url, headers = my_headers, proxies = my_proxies, timeout = 2)\n", "                \n", "                # 回傳 200 ok，代表 ip 暫時可用\n", "                if res.ok:\n", "                    # 簡單確認回傳結果，若包括 automation 字眼，代表可能被擋，要換 ip\n", "                    regex = r\"Access to this page has been denied\"\n", "                    match = re.search(regex, res.text)\n", "                    if match != None:\n", "                        print(f\"無法使用...\")\n", "                        break\n", "                    else:\n", "                        print(\"***結果: 成功，請求 (request) 已得到回應 (response)***\")\n", "                        print(f\"當前使用 Proxy: {my_proxies}\")\n", "                        sleep( randint(2,5) )\n", "                else:\n", "                    print(f\"第 {i + 1} 次 請求失敗\")\n", "        except:\n", "            print(f\"該 IP 無法使用...\")\n", "            continue"]}, {"cell_type": "code", "execution_count": null, "id": "a795e70d", "metadata": {"scrolled": false}, "outputs": [], "source": ["'''\n", "主程式\n", "'''\n", "if __name__ == \"__main__\":\n", "    time_begin = time() # 開始時間\n", "    getProxyPool()\n", "    checkProxy()\n", "    print(f\"執行花費 {time() - time_begin} 秒\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.12"}}, "nbformat": 4, "nbformat_minor": 5}