{"cells": [{"cell_type": "code", "execution_count": 7, "id": "1b68387f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'log': {'comment': '',\n", "         'creator': {'comment': '',\n", "                     'name': '<PERSON><PERSON><PERSON><PERSON><PERSON> Proxy',\n", "                     'version': '2.1.4'},\n", "         'entries': [],\n", "         'pages': [{'comment': '',\n", "                    'id': 'wine_searcher',\n", "                    'pageTimings': {'comment': ''},\n", "                    'startedDateTime': '2022-03-27T01:32:46.629+08:00',\n", "                    'title': 'wine_searcher'}],\n", "         'version': '1.2'}}\n"]}], "source": ["'''\n", "匯入套件\n", "'''\n", "# 操作 browser 的 API\n", "from selenium import webdriver\n", "\n", "# 處理逾時例外的工具\n", "from selenium.common.exceptions import TimeoutException\n", "\n", "# 面對動態網頁，等待某個元素出現的工具，通常與 exptected_conditions 搭配\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "\n", "# 搭配 WebDriverWait 使用，對元素狀態的一種期待條件，若條件發生，則等待結束，往下一行執行\n", "from selenium.webdriver.support import expected_conditions as EC\n", "\n", "# 期待元素出現要透過什麼方式指定，通常與 EC、WebDriverWait 一起使用\n", "from selenium.webdriver.common.by import By\n", "\n", "# 強制等待 (執行期間休息一下)\n", "from time import sleep\n", "\n", "# 整理 json 使用的工具\n", "import json\n", "\n", "# 執行 command 的時候用的\n", "import os\n", "\n", "# 引入 regular expression 工具\n", "import re\n", "\n", "# 輸出排版美化的工具\n", "from pprint import pprint\n", "\n", "# 瀏覽器代理工具\n", "from browsermobproxy import Server\n", "\n", "# 剖析網址資訊\n", "from urllib import parse\n", "\n", "# 正規表達式\n", "import re\n", "\n", "# 子處理程序，用來取代 os.system 的功能\n", "import subprocess\n", "\n", "# 美化輸出\n", "from pprint import pprint\n", "\n", "'''\n", "啟動瀏覽器工具的選項\n", "'''\n", "# 選項初始設定\n", "options = webdriver.ChromeOptions()\n", "# options.add_argument(\"--headless\")                #不開啟實體瀏覽器背景執行\n", "options.add_argument(\"--start-maximized\")         #最大化視窗\n", "options.add_argument(\"--incognito\")               #開啟無痕模式\n", "options.add_argument(\"--disable-popup-blocking \") #禁用彈出攔截\n", "\n", "# 啟動 proxy server 與 proxy client\n", "dictSetting = {'port': 8090}\n", "server = Server(\n", "    path = r'.\\browsermob-proxy-2.1.4\\bin\\browsermob-proxy.bat',\n", "    options = dictSetting\n", ")\n", "server.start()\n", "proxy = server.create_proxy()\n", "\n", "#\n", "user_agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.82 Safari/537.36'\n", "\n", "# 忽略認證錯誤訊息，以及加入自訂的 proxy\n", "options.add_argument(\"--ignore-certificate-errors\")\n", "options.add_argument(f\"--proxy-server={proxy.proxy}\")\n", "options.add_argument(f'--user-agent={user_agent}')\n", "\n", "# chrome 執行檔路徑 (在 unix-like 環境要用 / 這個斜線)\n", "executable_path = os.getcwd() + \"\\\\\" + \"chromedriver.exe\" \n", "\n", "# 使用 Chrome 的 WebDriver (含 options, executable_path)\n", "driver = webdriver.Chrome(\n", "    options = options, \n", "    executable_path = executable_path\n", ")\n", "\n", "#\n", "url = 'https://www.wine-searcher.com/find/mouton+rothschild+pauillac+medoc+bordeaux+france/2012#t4'\n", "\n", "# 前往指定連結\n", "driver.get(url);\n", "\n", "# 強制等待\n", "sleep(10)\n", "\n", "# 代理機制設定\n", "proxy.new_har('wine_searcher', options = {\n", "    'captureHeaders': True,\n", "    'captureContent': True\n", "})\n", "\n", "# 這裡的強制等待比較特別，等越久，取得的 Network 面板資訊愈多\n", "sleep(10)\n", "\n", "# 取得所有請求與回應的資訊\n", "result = proxy.har\n", "\n", "#\n", "pprint(result)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}}, "nbformat": 4, "nbformat_minor": 5}