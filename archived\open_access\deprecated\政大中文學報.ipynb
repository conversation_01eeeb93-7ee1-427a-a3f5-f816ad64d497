{"cells": [{"cell_type": "code", "execution_count": null, "id": "25080100", "metadata": {}, "outputs": [], "source": ["'''\n", "政大中文學報\n", "http://ctma.nccu.edu.tw/chibulletin/app/paper.php\n", "'''\n", "\n", "'''\n", "匯入套件\n", "'''\n", "# 請求套件\n", "import requests as req\n", "\n", "# 格式化輸出工具\n", "from pprint import pprint as pp\n", "\n", "# HTML Parser\n", "from bs4 import BeautifulSoup as bs\n", "\n", "# 強制等待 (執行期間休息一下)\n", "from time import sleep\n", "\n", "# 整理 json 使用的工具\n", "import json\n", "\n", "# regular expression 工具\n", "import re\n", "\n", "# 子處理程序，用來取代 os.system 的功能\n", "import subprocess\n", "\n", "# 建立隨機數\n", "from random import randint\n", "\n", "# 資料庫 (sqlite3)\n", "import sqlite3\n", "\n", "# Excel 工具\n", "from openpyxl import load_workbook\n", "from openpyxl import Workbook\n", "\n", "# 時間工具\n", "from datetime import datetime\n", "\n", "# 其它\n", "import json, os, sys\n", "\n", "'''\n", "設定\n", "'''\n", "# 主要首頁\n", "prefix = \"http://ctma.nccu.edu.tw/\"\n", "infix = 'chibulletin/app'\n", "url = prefix + infix + '/paper.php'\n", "\n", "# JSON 存檔路徑\n", "json_path = \"./政大中文學報.json\"\n", "\n", "# 自訂標頭\n", "my_headers = {\n", "    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36'\n", "}\n", "\n", "# 整理資料用的變數\n", "listData = []\n", "\n", "# 建立儲存檔案用的資料夾，不存在就新增\n", "folderPath = '政大中文學報'\n", "if not os.path.exists(folderPath):\n", "    os.makedirs(folderPath)\n", "\n", "'''\n", "程式區域\n", "'''\n", "# 取得分頁資訊\n", "def getPaginationLinks():\n", "    # 建立 parser\n", "    res = req.get(url, headers = my_headers)\n", "    res.encoding = 'utf-8'\n", "    soup = bs(res.text, \"lxml\")\n", "    \n", "    # 取得分頁資訊\n", "    regex = r\"\\s(\\d+)\\s\" \n", "    a_elms = soup.select('ol.PageList li a')\n", "    for a in a_elms:\n", "        match = re.search(regex, a.get_text())\n", "        if match != None:\n", "            listData.append({\n", "                \"page\": match[1],\n", "                \"link\": url + '' + a['href']\n", "            })    \n", "\n", "# 取得主要頁面所有連結資訊\n", "def getMainLinks():\n", "    for index, _dict in enumerate(listData):\n", "        # 沒有 sub 屬性，則建立，為了放置細節頁的內容\n", "        if \"sub\" not in listData[index]:\n", "            listData[index]['sub'] = []\n", "        \n", "        # 建立 parser\n", "        res = req.get(_dict['link'], headers = my_headers)\n", "        res.encoding = 'utf-8'\n", "        soup = bs(res.text, \"lxml\")\n", "        \n", "        # 取得主要連結\n", "        a_elms = soup.select('table#RSS_Table_page_paper_1 tbody tr td a')\n", "        for a in a_elms:\n", "            listData[index]['sub'].append({\n", "                \"title\": a.get_text(),\n", "                \"link\": prefix + '/' + infix + '/' + a['href']\n", "            })\n", "    \n", "# 取得 PDF 連結\n", "def getPdfLinks():\n", "    for index, _dict in enumerate(listData):\n", "        for idx, _d in enumerate(listData[index]['sub']):\n", "            # 沒有 sub 屬性，則建立，為了放置細節頁的內容\n", "            if \"downloads\" not in listData[index]['sub'][idx]:\n", "                listData[index]['sub'][idx]['downloads'] = []\n", "                \n", "            # 過濾出 pdf 連結\n", "            regex = r\"https?:\\/\\/.+\\.pdf\"\n", "            \n", "            # 建立 parser\n", "            res = req.get(_d['link'], headers = my_headers)\n", "            res.encoding = 'utf-8'\n", "            soup = bs(res.text, \"lxml\")\n", "\n", "            # 取得該頁的每一個 tr\n", "            tr_elms = soup.select('table#RSS_Table_page_paper_1 tbody tr')\n", "            if len(tr_elms) > 0:\n", "                for tr in tr_elms:\n", "                    # 取得該 tr 下的所有 td\n", "                    td_elms = tr.select('td')\n", "                    \n", "                    # 期數\n", "                    title = td_elms[0].get_text()\n", "                    \n", "                    # 作者\n", "                    author = td_elms[1].get_text()\n", "                    \n", "                    # 頁碼\n", "                    page = td_elms[2].get_text()\n", "                    \n", "                    # 篇名\n", "                    journal_name = td_elms[3].get_text()\n", "                    \n", "                    # 篇名超連結\n", "                    link = prefix + '/' + infix + '/' + td_elms[3].select_one('a')['href']\n", "                    \n", "                    # pdf 連結\n", "                    if len(td_elms[4].select('a')) > 0:\n", "                        pdf_link = prefix + td_elms[4].select('a')[1]['href']\n", "                    else:\n", "                        pdf_link = None\n", "\n", "                    # 整理資料\n", "                    listData[index]['sub'][idx]['downloads'].append({\n", "                        \"title\": title,\n", "                        \"author\": author,\n", "                        \"page\": page,\n", "                        \"journal_name\": journal_name,\n", "                        \"link\": link,\n", "                        \"pdf_link\": pdf_link\n", "                    })\n", "                    \n", "                    # (選項) 下載 PDF\n", "                    os.system(f\"curl {pdf_link} -o {folderPath}/{journal_name}.pdf\")\n", "\n", "# 儲存成 JSON\n", "def save<PERSON><PERSON><PERSON><PERSON>():\n", "    with open(f\"{folderPath}/{json_path}\", \"w\", encoding=\"utf-8\") as file:\n", "        file.write( json.dumps(listData, ensure_ascii=False, indent=4) )\n", "        \n", "'''\n", "執行區域\n", "'''\n", "if __name__ == \"__main__\":\n", "    getPaginationLinks()\n", "    getMainLinks()\n", "    getPdfLinks()\n", "    save<PERSON><PERSON><PERSON><PERSON>()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}}, "nbformat": 4, "nbformat_minor": 5}