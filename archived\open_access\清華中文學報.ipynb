{"cells": [{"cell_type": "markdown", "id": "54508a87", "metadata": {}, "source": ["# 套件安裝"]}, {"cell_type": "code", "execution_count": null, "id": "596a25a5", "metadata": {}, "outputs": [], "source": ["!pip install -U openpyxl selenium beautifulsoup4 lxml requests"]}, {"cell_type": "markdown", "id": "f7f904e0", "metadata": {}, "source": ["# 設定初始化"]}, {"cell_type": "code", "execution_count": null, "id": "9379a09f", "metadata": {}, "outputs": [], "source": ["'''\n", "注意事項:\n", "下載對應的 ChromeDriver (web driver) 到程式檔案同一個目錄下後解壓縮，下載前記得對應版本編號。\n", "連結: https://chromedriver.chromium.org/downloads\n", "\n", "參考網頁:\n", "[1] 國立清華大學 中國文學系 清華中文學報\n", "https://cl.site.nthu.edu.tw/p/403-1401-3772-1.php?Lang=zh-tw  \n", "'''\n", "\n", "\n", "'''\n", "匯入套件\n", "'''\n", "# HTML parser\n", "from bs4 import BeautifulSoup as bs\n", "\n", "# 操作 browser 的 API\n", "from selenium import webdriver\n", "\n", "# 處理逾時例外的工具\n", "from selenium.common.exceptions import TimeoutException\n", "\n", "# 面對動態網頁，等待某個元素出現的工具，通常與 exptected_conditions 搭配\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "\n", "# 搭配 WebDriverWait 使用，對元素狀態的一種期待條件，若條件發生，則等待結束，往下一行執行\n", "from selenium.webdriver.support import expected_conditions as EC\n", "\n", "# 期待元素出現要透過什麼方式指定，通常與 EC、WebDriverWait 一起使用\n", "from selenium.webdriver.common.by import By\n", "\n", "# 處理下拉式選單的工具\n", "from selenium.webdriver.support.ui import Select\n", "\n", "# 強制等待 (執行期間休息一下)\n", "from time import sleep\n", "\n", "# pretty-print\n", "from pprint import pprint\n", "\n", "# 隨機\n", "from random import randint\n", "\n", "# 計時\n", "import time\n", "\n", "# 整理 json 使用的工具\n", "import json\n", "\n", "# 執行 shell command 的時候用的\n", "import os\n", "\n", "# 子處理程序，用來取代 os.system 的功能\n", "import subprocess\n", "\n", "# 正規表達式\n", "import re\n", "\n", "# 編碼\n", "from urllib.parse import quote\n", "\n", "# 存取 Excel 的工具\n", "from openpyxl import load_workbook\n", "from openpyxl import Workbook\n", "\n", "# 啟動瀏覽器工具的選項\n", "my_options = webdriver.ChromeOptions()\n", "# my_options.add_argument(\"--headless\")             #不開啟實體瀏覽器背景執行\n", "my_options.add_argument(\"--start-maximized\")        #最大化視窗\n", "my_options.add_argument(\"--incognito\")              #開啟無痕模式\n", "my_options.add_argument(\"--disable-popup-blocking\") #禁用彈出攔截\n", "my_options.add_argument(\"--disable-notifications\")  #取消通知\n", "my_options.add_argument(\"--lang=zh-TW\")  #設定為正體中文\n", "\n", "# 指定 chromedriver 檔案的路徑\n", "driver_exec_path = './chromedriver.exe'\n", "\n", "# 給 web driver 用的變數\n", "driver = None\n", "\n", "# 來源首頁\n", "prefix_url = 'https://cl.site.nthu.edu.tw/'\n", "url = prefix_url + 'p/403-1401-3772-1.php?Lang=zh-tw'\n", "\n", "# 指定 sheet name\n", "sheetName = 'cl_site_nthu'\n", "\n", "# 指定 excel 檔名\n", "excelFileName = 'cl_site_nthu.xlsx'\n", "\n", "# 指定 json 檔名\n", "jsonFileName = f'{sheetName}.json'\n", "\n", "# 建立儲存圖片、影片的資料夾\n", "folderPath = f'./{sheetName}'\n", "if not os.path.exists(folderPath):\n", "    os.makedirs(folderPath)\n", "\n", "# 判斷 excel 檔案是否存在，不存在就新增\n", "filePath = folderPath + '/' + excelFileName\n", "if not os.path.exists(filePath):\n", "    workbook = Workbook() # 動態新增檔案\n", "    worksheet = workbook.create_sheet(sheetName, 0) # 建立並取得 active sheet\n", "else:\n", "    workbook = load_workbook(filename = filePath)\n", "    worksheet = workbook[sheetName] # 取得 active sheet\n", "\n", "#預設下載路徑\n", "my_options.add_experimental_option(\"prefs\", {\n", "    \"download.default_directory\": folderPath\n", "})\n", "    \n", "# excel 標題\n", "worksheet['A1'] = \"流水號\"\n", "worksheet['B1'] = \"期刊名稱\"\n", "worksheet['C1'] = '網頁連結'\n", "worksheet['D1'] = \"出版日期\"\n", "worksheet['E1'] = \"論文名稱\"\n", "worksheet['F1'] = \"作者名稱\"\n", "worksheet['G1'] = \"論文連結\"\n", "worksheet['H1'] = \"論文連結_curl\"\n", "\n", "# 放置爬取的資料\n", "listData = []"]}, {"cell_type": "markdown", "id": "953be599", "metadata": {}, "source": ["# 自訂函式 (網路爬蟲執行流程)"]}, {"cell_type": "code", "execution_count": null, "id": "adae7d06", "metadata": {}, "outputs": [], "source": ["'''\n", "函式\n", "'''\n", "# 初始化 Web Driver\n", "def init():\n", "    global driver\n", "    # 使用 Chrome 的 WebDriver\n", "    driver = webdriver.Chrome( \n", "        options = my_options, \n", "        executable_path = driver_exec_path\n", "    )\n", "    \n", "# 走訪來源網頁\n", "def visit():\n", "    global driver\n", "    driver.get(url) #進入來源網頁\n", "    try:\n", "        # 等待目標元素出現\n", "        WebDriverWait(driver, 10).until(\n", "            EC.presence_of_element_located(\n", "                (By.CSS_SELECTOR, 'div.row.listBS.boxSD a[href]')\n", "            )\n", "        )\n", "    except TimeoutException as e:\n", "        print('等待逾時: visit')\n", "        \n", "# 滾動頁面\n", "def scroll():\n", "    global driver\n", "    \n", "    '''\n", "    innerHeight => 瀏覽器內部的高度\n", "    offset => 當前捲動的量(高度)\n", "    count => 累計無效滾動次數\n", "    limit => 最大無效滾動次數\n", "    '''\n", "    innerHeight = 0\n", "    offset = 0\n", "    count = 0\n", "    limit = 1\n", "    \n", "    # 在捲動到沒有元素動態產生前，持續捲動\n", "    while count <= limit:\n", "        # 每次移動高度\n", "        offset = driver.execute_script(\n", "            'return window.document.documentElement.scrollHeight;'\n", "        )\n", "\n", "        '''\n", "        或是每次只滾動一點距離，\n", "        以免有些網站會在移動長距離後，\n", "        將先前移動當中的元素隱藏\n", "\n", "        例如將上方的 script 改成:\n", "        offset += 600\n", "        '''\n", "\n", "        # 捲軸往下滑動\n", "        driver.execute_script(f'''\n", "            window.scrollTo({{\n", "                top: {offset}, \n", "                behavior: 'smooth' \n", "            }});\n", "        ''')\n", "        \n", "        # 強制等待，此時若有新元素生成，瀏覽器內部高度會自動增加\n", "        sleep(1)\n", "        \n", "        # 透過執行 js 語法來取得捲動後的當前總高度\n", "        innerHeight = driver.execute_script(\n", "            'return window.document.documentElement.scrollHeight;'\n", "        );\n", "        \n", "        # 經過計算，如果滾動距離(offset)大於等於視窗內部總高度(innerHeight)，代表已經到底了\n", "        if offset == innerHeight:\n", "            count += 1\n", "    \n", "# 剖析內容\n", "def parse():\n", "    global driver\n", "    global listData\n", "    try:\n", "        # 流水號\n", "        sn = 1\n", "        \n", "        # 暫存頁面資訊\n", "        listTmp = []\n", "        \n", "        # 取得主要連結\n", "        for a in driver.find_elements(By.CSS_SELECTOR, 'div.row.listBS.boxSD a[href]'):\n", "            # 網頁連結\n", "            strMainLink = a.get_attribute('href')\n", "            \n", "            # 期刊名稱 與 出版日期\n", "            strMainName = strMainDate = ''\n", "            regexMain = r'.+（(\\d{4}\\.\\d{1,2})）'\n", "            matchMain = re.search(regexMain, a.get_attribute('innerText'))\n", "            if matchMain != None:\n", "                strMainName = matchMain[0] # 期刊名稱\n", "                strMainDate = matchMain[1] # 出版日期\n", "                \n", "            listTmp.append({\n", "                '期刊名稱': str<PERSON>ainName,\n", "                '網頁連結': strMainLink,\n", "                '出版日期': strMainDate,\n", "            })\n", "        \n", "        # 取得資料\n", "        for d in listTmp:\n", "            # 取得內頁資料\n", "            driver.get(d['網頁連結'])\n", "            \n", "            # 等待目標元素出現\n", "            WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located(\n", "                    (By.CSS_SELECTOR, 'div.meditor table tbody tr')\n", "                )\n", "            )\n", "            \n", "            # 取得期刊篇名與作者名稱\n", "            tr_elms = driver.find_elements(By.CSS_SELECTOR, 'div.meditor table tbody tr')\n", "            for tr in tr_elms:                \n", "                # pdf 連結與篇名、作者\n", "                strPdfLink = strJournalTitle = strAuthor = ''\n", "                \n", "                # 若 td 數量等於 2，代表是期刊連結的那一列\n", "                if len(tr.find_elements(By.CSS_SELECTOR, 'td')) == 2:\n", "                    # 取得 td\n", "                    td_elms = tr.find_elements(By.CSS_SELECTOR, 'div.meditor table tbody tr td')\n", "                    \n", "                    # 有 a 才取得資料 (有些 tr 當中的 td 裡面，沒有 a)\n", "                    if len(td_elms[0].find_elements(By.CSS_SELECTOR, 'a')) > 0:\n", "                        a_elm = td_elms[0].find_element(By.CSS_SELECTOR, 'a[href]')\n", "\n", "                        # pdf 連結\n", "                        strPdfLink = a_elm.get_attribute('href')\n", "\n", "                        # 篇名\n", "                        strJournalTitle = td_elms[0].get_attribute('innerText')\n", "                        strJournalTitle = strJournalTitle.strip()\n", "                        strJournalTitle = re.sub(r\"\\n\", \"\", strJournalTitle)\n", "                        \n", "                        # 作者\n", "                        strAuthor = td_elms[1].get_attribute('innerText')\n", "                        strAuthor = strAuthor.strip()\n", "\n", "                        # 整理資料\n", "                        listData.append({\n", "                            '流水號': sn,\n", "                            '期刊名稱': d['期刊名稱'],\n", "                            '網頁連結': d['網頁連結'],\n", "                            '出版日期': d['出版日期'],\n", "                            '論文名稱': strJournalTitle,\n", "                            '作者名稱': s<PERSON><PERSON><PERSON><PERSON>,\n", "                            '論文連結': strPdfLink,\n", "                            '論文連結_curl': strPdfLink\n", "                        })\n", "\n", "                        # 流水號遞增\n", "                        sn += 1\n", "    except TimeoutException as e:\n", "        print('等待逾時: parse')\n", "        \n", "# 關閉瀏覽器\n", "def close():\n", "    driver.quit()\n", "        \n", "# 儲存成 json\n", "def save<PERSON><PERSON>():\n", "    global listData\n", "    with open(f\"{folderPath}/{jsonFileName}\", \"w\", encoding=\"utf-8\") as file:\n", "        file.write( json.dumps( listData, ensure_ascii=False, indent=4 ) )\n", "\n", "# 儲存成 excel\n", "def saveExcel():\n", "    with open(f\"{folderPath}/{jsonFileName}\", \"r\", encoding=\"utf-8\") as file:\n", "        # 從 excel 列號 2 開始寫入資料\n", "        row_num = 2\n", "        \n", "        # 取得 json 內容\n", "        strJson = file.read()\n", "        \n", "        # 將 json 轉成 list\n", "        listJson = json.loads(strJson)\n", "        \n", "        # 逐列寫入\n", "        for myDict in list<PERSON><PERSON>:\n", "            worksheet['A' + str(row_num)] = myDict[\"流水號\"]\n", "            worksheet['B' + str(row_num)] = myDict[\"期刊名稱\"]\n", "            worksheet['C' + str(row_num)] = myDict[\"網頁連結\"]\n", "            worksheet['D' + str(row_num)] = myDict[\"出版日期\"]\n", "            worksheet['E' + str(row_num)] = myDict[\"論文名稱\"]\n", "            worksheet['F' + str(row_num)] = myDict[\"作者名稱\"]\n", "            worksheet['G' + str(row_num)] = myDict[\"論文連結\"]\n", "            worksheet['H' + str(row_num)] = myDict[\"論文連結_curl\"]\n", "            row_num += 1\n", "    \n", "    # 儲存 workbook\n", "    workbook.save(filePath)\n", "\n", "    # 關閉 workbook\n", "    workbook.close()\n", "    \n", "# 下載\n", "def download():\n", "    with open(f\"{folderPath}/{jsonFileName}\", \"r\", encoding=\"utf-8\") as file:      \n", "        # 取得 json 內容\n", "        strJson = file.read()\n", "        \n", "        # 將 json 轉成 list\n", "        listJson = json.loads(strJson)\n", "        \n", "        for myDict in list<PERSON><PERSON>:\n", "            # 等待\n", "            sleep(randint(1,3))\n", "            \n", "            # 下載 pdf\n", "            cmd = ['curl', '-L', myDict[\"論文連結_curl\"], '-o', f'{folderPath}/sn_{myDict[\"流水號\"]}.pdf']\n", "            result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT)\n", "            #output = result.stdout\n", "            #pprint(output)\n", "            print(f'{folderPath}/sn_{myDict[\"流水號\"]}.pdf')"]}, {"cell_type": "markdown", "id": "71f66304", "metadata": {}, "source": ["# 以下函式，請各別依情況分別、陸續執行"]}, {"cell_type": "code", "execution_count": null, "id": "f62216cc", "metadata": {}, "outputs": [], "source": ["# 初始化 Web Driver\n", "init()"]}, {"cell_type": "code", "execution_count": null, "id": "fe9ada78", "metadata": {}, "outputs": [], "source": ["# 走訪來源網頁\n", "visit()"]}, {"cell_type": "code", "execution_count": null, "id": "5f15e66b", "metadata": {}, "outputs": [], "source": ["# 滾動頁面\n", "scroll()"]}, {"cell_type": "code", "execution_count": null, "id": "61cdf462", "metadata": {}, "outputs": [], "source": ["# 剖析內容\n", "parse()"]}, {"cell_type": "code", "execution_count": null, "id": "33224e03", "metadata": {}, "outputs": [], "source": ["# 關閉瀏覽器\n", "close()"]}, {"cell_type": "code", "execution_count": null, "id": "6e909c9a", "metadata": {}, "outputs": [], "source": ["# 儲存成 json\n", "<PERSON><PERSON><PERSON>()"]}, {"cell_type": "code", "execution_count": null, "id": "5f0eeaae", "metadata": {}, "outputs": [], "source": ["# 儲存成 excel\n", "saveExcel()"]}, {"cell_type": "code", "execution_count": null, "id": "1296bde8", "metadata": {}, "outputs": [], "source": ["# 下載\n", "download()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}}, "nbformat": 4, "nbformat_minor": 5}