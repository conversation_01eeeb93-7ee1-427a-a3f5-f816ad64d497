{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["'''\n", "匯入套件\n", "'''\n", "# 操作 browser 的 API\n", "from selenium import webdriver\n", "\n", "# 處理逾時例外的工具\n", "from selenium.common.exceptions import TimeoutException\n", "\n", "# 面對動態網頁，等待某個元素出現的工具，通常與 exptected_conditions 搭配\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "\n", "# 搭配 WebDriverWait 使用，對元素狀態的一種期待條件，若條件發生，則等待結束，往下一行執行\n", "from selenium.webdriver.support import expected_conditions as EC\n", "\n", "# 期待元素出現要透過什麼方式指定，通常與 EC、WebDriverWait 一起使用\n", "from selenium.webdriver.common.by import By\n", "\n", "# 強制等待 (執行期間休息一下)\n", "from time import sleep\n", "\n", "# 整理 json 使用的工具\n", "import json\n", "\n", "# 執行 command 的時候用的\n", "import os\n", "\n", "# 子處理程序，用來取代 os.system 的功能\n", "import subprocess\n", "\n", "# 取得隨機數\n", "import random\n", "\n", "# 啟動瀏覽器工具的選項\n", "my_options = webdriver.ChromeOptions()\n", "my_options.add_argument(\"--start-maximized\") #最大化視窗\n", "\n", "# 使用 Chrome 的 WebDriver\n", "driver = webdriver.Chrome(\n", "    options = my_options,\n", ")\n", "\n", "# 建立儲存圖片、影片的資料夾\n", "folderPath = 'wikiart'\n", "if not os.path.exists(folderPath):\n", "    os.makedirs(folderPath)\n", "\n", "# 放置爬取的資料\n", "set_data = set()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def visit():\n", "    driver.get(\"https://www.wikiart.org/\")\n", "\n", "# 滾動頁面\n", "def scroll():\n", "    innerHeight = 0\n", "    offset = 0\n", "    count = 0\n", "    limit = 2\n", "    move = 0\n", "    \n", "    # 在捲動到沒有元素動態產生前，持續捲動\n", "    while count <= limit:\n", "        # 每次移動的距離\n", "        offset += 800\n", "\n", "        # 捲軸往下滑動\n", "        driver.execute_script(f'''\n", "            window.scrollTo({{\n", "                top: {offset}, \n", "                behavior: 'smooth' \n", "            }});\n", "        ''')\n", "\n", "        # 每次捲動完，就執行一次載入更多的動作\n", "        if move % 5 == 0:\n", "            load_more()\n", "\n", "        # 每次捲動完，就執行一次解析的動作\n", "        parse()\n", "\n", "        # 每次捲動完，就執行一次儲存的動作\n", "        save()\n", "        \n", "        # (重要)強制等待，此時若有新元素生成，瀏覽器內部高度會自動增加\n", "        sleep(random.ran<PERSON>t(2, 3))\n", "        \n", "        # 透過執行 js 語法來取得捲動後的當前總高度\n", "        innerHeight = driver.execute_script(\n", "            'return document.documentElement.scrollHeight;'\n", "        )\n", "\n", "        # 每次捲動完，就將移動次數加 1\n", "        move += 1\n", "\n", "        print(f\"count: {count}, offset: {offset}, innerHeight: {innerHeight}\")\n", "        \n", "        # 經過計算，如果滾動距離(offset)大於等於視窗內部總高度(innerHeight)，代表已經到底了\n", "        if offset >= innerHeight:\n", "            count += 1\n", "            \n", "# 載入更多圖片\n", "def load_more():\n", "    try:\n", "        css_selector_load_more = 'a.masonry-load-more-button[ng-show=\"canLoadMore()\"]'\n", "        if len(driver.find_elements(By.CSS_SELECTOR, css_selector_load_more)) > 0:\n", "            WebDriverWait(driver, 1).until(\n", "                EC.element_to_be_clickable((By.CSS_SELECTOR, css_selector_load_more))\n", "            )\n", "            driver.find_element(By.CSS_SELECTOR, css_selector_load_more).click()\n", "    except TimeoutException:\n", "        print('Load More 按鈕失效')\n", "\n", "# 解析資料\n", "def parse():\n", "    global set_data\n", "    try:\n", "        elements = driver.find_elements(By.CSS_SELECTOR, 'img[src^=https\\:\\/\\/upload]')\n", "        for element in elements:\n", "            # 取得圖片連結\n", "            src = element.get_attribute('src')\n", "            set_data.add(src)\n", "    except:\n", "        pass\n", "\n", "# 儲存資料\n", "def save():\n", "    global set_data\n", "    with open(f'{folderPath}/data.json', 'w', encoding='utf-8') as f:\n", "        f.write(json.dumps(list(set_data), indent=4, ensure_ascii=False))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if __name__ == '__main__':\n", "    visit()\n", "    scroll()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 關閉瀏覽器\n", "driver.quit()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 下載圖片\n", "with open(f'{folderPath}/data.json', 'r', encoding='utf-8') as f:\n", "    # 讀取 json 檔案，變成 list\n", "    list_data = json.loads(f.read())\n", "\n", "    # 逐一下載圖片\n", "    for i, src in enumerate(list_data):\n", "        # 練習期間，下載幾張就好\n", "        if i == 10:\n", "            break\n", "\n", "        # 清除圖片網址的 !PinterestSmall.jpg，取得最高畫質的圖片連結\n", "        src = src.replace('!PinterestSmall.jpg', '')\n", "\n", "        # 取得圖片檔名\n", "        file_name = src.split('/')[-1]\n", "        \n", "        print(f'第 {i+1} 張：{src}')\n", "        \n", "        # 下載圖片\n", "        std = subprocess.run(['curl', src, '-o', f'{folderPath}/{file_name}'])\n", "        if std.returncode == 0:\n", "            print(f'{file_name} 下載成功！')\n", "\n", "        # 每下載一張就強制等待\n", "        sleep(1)"]}], "metadata": {"kernelspec": {"display_name": "web_scraping", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 2}