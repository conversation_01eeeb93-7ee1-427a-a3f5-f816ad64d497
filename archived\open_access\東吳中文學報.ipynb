{"cells": [{"cell_type": "code", "execution_count": null, "id": "bcc8121f", "metadata": {}, "outputs": [], "source": ["!pip install -U openpyxl selenium beautifulsoup4 lxml requests"]}, {"cell_type": "code", "execution_count": null, "id": "f2cc5a41", "metadata": {}, "outputs": [], "source": ["'''\n", "注意事項:\n", "下載對應的 ChromeDriver (web driver) 到程式檔案同一個目錄下後解壓縮，下載前記得對應版本編號。\n", "連結: https://chromedriver.chromium.org/downloads\n", "\n", "參考網頁:\n", "[1] 東吳中文學報\n", "https://web-ch.scu.edu.tw/chinese/file/3423\n", "[2] sqlite3 --- SQLite 数据库 DB-API 2.0 接口模块\n", "https://docs.python.org/zh-tw/3/library/sqlite3.html\n", "'''\n", "\n", "\n", "'''\n", "匯入套件\n", "'''\n", "# HTML parser\n", "from bs4 import BeautifulSoup as bs\n", "\n", "# 操作 browser 的 API\n", "from selenium import webdriver\n", "\n", "# 處理逾時例外的工具\n", "from selenium.common.exceptions import TimeoutException\n", "\n", "# 面對動態網頁，等待某個元素出現的工具，通常與 exptected_conditions 搭配\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "\n", "# 搭配 WebDriverWait 使用，對元素狀態的一種期待條件，若條件發生，則等待結束，往下一行執行\n", "from selenium.webdriver.support import expected_conditions as EC\n", "\n", "# 期待元素出現要透過什麼方式指定，通常與 EC、WebDriverWait 一起使用\n", "from selenium.webdriver.common.by import By\n", "\n", "# 處理下拉式選單的工具\n", "from selenium.webdriver.support.ui import Select\n", "\n", "# 強制等待 (執行期間休息一下)\n", "from time import sleep\n", "\n", "# pretty-print\n", "from pprint import pprint\n", "\n", "# 隨機\n", "from random import randint\n", "\n", "# 計時\n", "import time\n", "\n", "# 整理 json 使用的工具\n", "import json\n", "\n", "# 執行 shell command 的時候用的\n", "import os\n", "\n", "# 取得錯誤訊息\n", "import sys, traceback\n", "\n", "# 子處理程序，用來取代 os.system 的功能\n", "import subprocess\n", "\n", "# 正規表達式\n", "import re\n", "\n", "# 編碼\n", "from urllib.parse import quote\n", "\n", "# SQLite 資料庫\n", "import sqlite3\n", "\n", "# 存取 Excel 的工具\n", "from openpyxl import load_workbook\n", "from openpyxl import Workbook\n", "\n", "# 取得系統時間的工具\n", "from datetime import datetime\n", "\n", "# 引入 hashlib 模組\n", "import hashlib\n", "\n", "# 啟動瀏覽器工具的選項\n", "my_options = webdriver.ChromeOptions()\n", "# my_options.add_argument(\"--headless\")             #不開啟實體瀏覽器背景執行\n", "my_options.add_argument(\"--start-maximized\")        #最大化視窗\n", "my_options.add_argument(\"--incognito\")              #開啟無痕模式\n", "my_options.add_argument(\"--disable-popup-blocking\") #禁用彈出攔截\n", "my_options.add_argument(\"--disable-notifications\")  #取消通知\n", "my_options.add_argument(\"--lang=zh-TW\")  #設定為正體中文\n", "\n", "# 指定 chromedriver 檔案的路徑\n", "driver_exec_path = './chromedriver.exe'\n", "\n", "# 給 web driver 用的變數\n", "driver = None\n", "\n", "# 來源首頁\n", "prefix_url = 'https://web-ch.scu.edu.tw'\n", "url = prefix_url + '/chinese/file/3423'\n", "\n", "# 指定 sheet name\n", "sheetName = 'web_ch_scu'\n", "\n", "# 指定 excel 檔名\n", "excelFileName = 'web_ch_scu.xlsx'\n", "\n", "# 指定 json 檔名\n", "jsonFileName = f'{sheetName}.json'\n", "\n", "# 建立儲存檔案用的資料夾\n", "folderPath = f'./{sheetName}'\n", "if not os.path.exists(folderPath):\n", "    os.makedirs(folderPath)\n", "\n", "#預設下載路徑\n", "my_options.add_experimental_option(\"prefs\", {\n", "    \"download.default_directory\": folderPath\n", "})\n", "\n", "# 放置爬取的資料\n", "listData = []\n", "\n", "# 分頁網址\n", "listPage = []"]}, {"cell_type": "code", "execution_count": null, "id": "d832b5be", "metadata": {}, "outputs": [], "source": ["'''\n", "函式\n", "'''\n", "# md5 (用來為每一筆資料建立唯一代號)\n", "def md5(string):\n", "    m = hashlib.md5()\n", "    m.update(string.encode(\"utf-8\"))\n", "    return m.hexdigest()\n", "\n", "# 初始化 Web Driver\n", "def init():\n", "    global driver\n", "    # 使用 Chrome 的 WebDriver\n", "    driver = webdriver.Chrome( \n", "        options = my_options, \n", "        executable_path = driver_exec_path\n", "    )\n", "    \n", "# 取得每個分頁連結\n", "def getPagination():\n", "    global driver\n", "    global listPage\n", "    \n", "    try:\n", "        # 走訪首頁\n", "        driver.get(url)\n", "        \n", "        # 因為進去即是第 1 頁，所以要先預設第 1 個連結\n", "        setPage = set()\n", "        setPage.add(f\"{url}?page=1\")\n", "    \n", "        # 等待目標元素出現\n", "        WebDriverWait(driver, 10).until(\n", "            EC.presence_of_element_located(\n", "                (By.CSS_SELECTOR, 'ul.pagination > li a')\n", "            )\n", "        )\n", "        \n", "        # 取得分頁列表\n", "        a_elms = driver.find_elements(\n", "            By.CSS_SELECTOR, 'ul.pagination > li a[href]'\n", "        )\n", "        \n", "        # 開啟分頁\n", "        for a in a_elms:\n", "            # 取得不重複的分頁\n", "            setPage.add(a.get_attribute('href'))\n", "            \n", "        # set 轉成 list\n", "        listPage = list(setPage)\n", "        listPage.sort()\n", "        \n", "        pprint(listPage)\n", "            \n", "    except TimeoutException as e:\n", "        print('等待逾時: getPagination')\n", "    \n", "# 剖析內容\n", "def parse():\n", "    global driver, listData, listPage\n", "    try:\n", "        # 流水號\n", "        sn = 1\n", "        \n", "        # 走訪每一分頁，同時為分頁底下的期刊連結開啟分頁\n", "        for idx, link in enumerate(listPage):\n", "            # 切換到初始分頁\n", "            driver.switch_to.window(driver.window_handles[0])\n", "            \n", "            # 等待\n", "            sleep(2)\n", "            \n", "            # 各自走訪首頁\n", "            driver.get(link)\n", "            print(link)\n", "            \n", "            # 等待目標元素出現\n", "            WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located(\n", "                    (\n", "                        By.CSS_SELECTOR, \n", "                        'table.table.table-striped.table-bordered tr[class]:not([class=\"info\"])'\n", "                    )\n", "                )\n", "            )\n", "                \n", "            # 開啟分頁，各別取得內頁資料\n", "            a_elms = driver.find_elements(By.CSS_SELECTOR, 'table.table.table-striped.table-bordered tr[class]:not([class=\"info\"]) td a')\n", "            for index, a in enumerate(a_elms):\n", "                # 開啟新分頁\n", "                driver.execute_script(f'window.open(\"about:blank\", \"_blank\");')\n", "                \n", "            # 將所有 tabs 轉址到分頁網址去，以便取得對應 a 列表\n", "            for index, window in enumerate(driver.window_handles):\n", "                if index == 0: continue\n", "                driver.switch_to.window(driver.window_handles[index])\n", "                driver.get(link)\n", "                \n", "            # 切換到初始分頁\n", "            driver.switch_to.window(driver.window_handles[0])\n", "                \n", "            # 取得所有 tr\n", "            tr_elms = driver.find_elements(By.CSS_SELECTOR, 'table.table.table-striped.table-bordered tr[class]:not([class=\"info\"])')\n", "            \n", "            # 取得每一個 tr 底下的 td，並為 td 當中的 a 開啟分頁，之後在每一個分頁按下對應的 a，進入內頁\n", "            for index, tr in enumerate(tr_elms):\n", "                # 跳到指定分頁\n", "                driver.switch_to.window(driver.window_handles[index + 1])\n", "                \n", "                # 等待\n", "                driver.implicitly_wait(10)\n", "\n", "                # 依連結順序按下連結\n", "                a_elms = driver.find_elements(By.CSS_SELECTOR, 'table.table.table-striped.table-bordered tr[class]:not([class=\"info\"]) td a')\n", "                \n", "                # 按下對應順序的連結，進入各自的內頁\n", "                a_elms[index].click()\n", "                \n", "                print(a_elms[index].get_attribute('innerText'))\n", "\n", "            # 切換到初始分頁\n", "            driver.switch_to.window(driver.window_handles[0])\n", "            \n", "            # 取得所有 tr\n", "            tr_elms = driver.find_elements(By.CSS_SELECTOR, 'table.table.table-striped.table-bordered tr[class]:not([class=\"info\"])')\n", "            \n", "            # 取得所有 metadata，包括超連結\n", "            for index, tr in enumerate(tr_elms):\n", "                td_elms = tr.find_elements(By.CSS_SELECTOR, 'td')\n", "                \n", "                # 篇名\n", "                strJournalTitle = td_elms[0].get_attribute('innerText')\n", "                strJournalTitle = strJournalTitle.strip()\n", "                \n", "                # 出版日期\n", "                strPublishDate = ''\n", "                regexPublishDate = r'（(\\d{4}\\.\\d{1,2})）|\\((\\d{4}\\.\\d{1,2}\\.\\d{1,2})\\)'\n", "                matchPublishDate = re.search(regexPublishDate, td_elms[1].get_attribute('innerText'))\n", "                if matchPublishDate[1] != None:\n", "                    strPublishDate = matchPublishDate[1]\n", "                elif matchPublishDate[2] != None:\n", "                    strPublishDate = matchPublishDate[2]\n", "                    \n", "                # 最後修訂日期\n", "                strLastModifiedDate = td_elms[2].get_attribute('innerText')\n", "                strLastModifiedDate = strLastModifiedDate.strip()\n", "\n", "                # 跳到指定分頁\n", "                driver.switch_to.window(driver.window_handles[index + 1])\n", "\n", "                # 依連結順序按下連結\n", "                strPdfLink = ''\n", "                if len(driver.find_elements(By.CSS_SELECTOR, 'div#news_header ul li a[href]')) > 0:\n", "                    a_elm = driver.find_element(By.CSS_SELECTOR, 'div#news_header ul li a[href]')\n", "                    strPdfLink = a_elm.get_attribute('href')\n", "                \n", "                # 整理資料\n", "                listData.append({\n", "                    \"id\": md5(strJournalTitle),\n", "                    \"流水號\": sn,\n", "                    \"篇名\": strJournalTitle,\n", "                    \"出版日期\": strPublishDate,\n", "                    \"最後修訂日期\": strLastModifiedDate,\n", "                    \"檔案連結_原始\": strPdfLink,\n", "                    \"檔案連結_curl\": strPdfLink\n", "                })\n", "                \n", "                # 遞增流水號\n", "                sn += 1\n", "                \n", "                # 切換到初始分頁\n", "                driver.switch_to.window(driver.window_handles[0])\n", "                \n", "            # 把所有分頁關掉\n", "            while True:\n", "                windows = driver.window_handles\n", "                if len(windows) == 1: break\n", "                driver.switch_to.window(windows[len(windows) - 1])\n", "                driver.close()\n", "                \n", "            # 切換到初始分頁\n", "            driver.switch_to.window(driver.window_handles[0])\n", "            \n", "    except TimeoutException as e:\n", "        print('等待逾時: parse')\n", "        \n", "# 關閉瀏覽器\n", "def close():\n", "    driver.quit()\n", "        \n", "# 儲存成 json\n", "def save<PERSON><PERSON>():\n", "    global listData\n", "    with open(f\"{folderPath}/{jsonFileName}\", \"w\", encoding=\"utf-8\") as file:\n", "        file.write( json.dumps( listData, ensure_ascii=False, indent=4 ) )\n", "    \n", "# 儲存 .db\n", "def saveDB():\n", "    with open(f\"{folderPath}/{jsonFileName}\", \"r\", encoding=\"utf-8\") as file:      \n", "        # 取得 json 內容\n", "        strJson = file.read()\n", "        \n", "        # 將 json 轉成 list\n", "        listJson = json.loads(strJson)\n", "        \n", "    # 寫入對話記錄\n", "    conn = sqlite3.connect(f\"{folderPath}/{sheetName}.db\")\n", "    \n", "    # 建立 cursor 物件\n", "    cursor = conn.cursor()\n", "\n", "    # 執行 SQL 語法\n", "    try:\n", "        # 查詢特定資料\n", "        sql_query = f'''\n", "        SELECT id, title, link_curl, is_downloaded\n", "        FROM journals\n", "        WHERE id = ?\n", "        '''\n", "        \n", "        # 寫入資料\n", "        sql_insert = f'''\n", "        INSERT INTO journals (\n", "            id, title, publish_date, last_modified_date, link, \n", "            link_curl, is_downloaded, created_at, updated_at\n", "        ) VALUES ( \n", "            ?,?,?,?,?,\n", "            ?,?,?,?\n", "        )\n", "        '''\n", "        \n", "        for myDict in list<PERSON><PERSON>:\n", "            if cursor.execute(sql_query, (myDict[\"id\"],)).fetchone() == None:\n", "                cursor.execute(sql_insert, (\n", "                    myDict['id'], \n", "                    myDict['篇名'],\n", "                    myDict['出版日期'],\n", "                    myDict['最後修訂日期'],\n", "                    myDict['檔案連結_原始'],\n", "                    myDict['檔案連結_curl'],\n", "                    0,\n", "                    datetime.today().strftime(\"%Y-%m-%d %H-%M-%S\"),\n", "                    datetime.today().strftime(\"%Y-%m-%d %H-%M-%S\")\n", "                ))\n", "        \n", "        conn.commit()\n", "    except sqlite3.<PERSON>rror as err: \n", "        # 回滾\n", "        conn.rollback()\n", "\n", "        # SQLite3 例外處理\n", "        exc_type, exc_value, exc_tb = sys.exc_info()\n", "        strErrorMsg = f'''SQLite error: {' '.join(err.args)}\\n\\n\n", "        SQLite traceback: {traceback.format_exception(exc_type, exc_value, exc_tb)}\n", "        '''\n", "        print(strErrorMsg)\n", "\n", "    # 關閉 sqlite\n", "    conn.close()\n", "    \n", "# 下載\n", "def download():\n", "    # 寫入對話記錄\n", "    conn = sqlite3.connect(f\"{folderPath}/{sheetName}.db\")\n", "    conn.row_factory = sqlite3.Row\n", "    \n", "    # 建立 cursor 物件\n", "    cursor = conn.cursor()\n", "\n", "    # 執行 SQL 語法\n", "    try:\n", "        # 查詢特定資料\n", "        sql_query = f'''\n", "        SELECT sn, id, title, link_curl, is_downloaded\n", "        FROM journals\n", "        WHERE `is_downloaded` = 0\n", "        '''\n", "        \n", "        # 更新資料的欄位(狀態)\n", "        sql_update = f'''\n", "        UPDATE `journals` \n", "        SET \n", "            `is_downloaded` = 1 ,\n", "            `updated_at` = ?\n", "        WHERE `id` = ?\n", "        '''\n", "        \n", "        # 取得所有未下載的資料\n", "        list_results = []\n", "        for item in cursor.execute(sql_query).fetchall(): \n", "            list_results.append({k: item[k] for k in item.keys()})\n", "        for myDict in list_results:\n", "            # 等待\n", "            sleep(randint(1,3))\n", "\n", "            # 下載 pdf\n", "            cmd = ['curl', '-L', myDict[\"link_curl\"], '-o', f'{folderPath}/{myDict[\"sn\"]}_{myDict[\"id\"]}.pdf']\n", "            result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT)\n", "            #output = result.stdout\n", "            #pprint(output)\n", "            print(f'{folderPath}/{myDict[\"sn\"]}_{myDict[\"id\"]}.pdf')\n", "\n", "            # 將 is_downloaded 改成 1，代表已下載過\n", "            cursor.execute(sql_update, (datetime.today().strftime(\"%Y-%m-%d %H-%M-%S\"), myDict[\"id\"],))\n", "                \n", "        conn.commit()\n", "    except sqlite3.<PERSON>rror as err: \n", "        # 回滾\n", "        conn.rollback()\n", "\n", "        # SQLite3 例外處理\n", "        exc_type, exc_value, exc_tb = sys.exc_info()\n", "        strErrorMsg = f'''SQLite error: {' '.join(err.args)}\\n\\n\n", "        SQLite traceback: {traceback.format_exception(exc_type, exc_value, exc_tb)}\n", "        '''\n", "        print(strErrorMsg)\n", "\n", "    # 關閉 sqlite\n", "    conn.close()"]}, {"cell_type": "code", "execution_count": null, "id": "1c319fb0", "metadata": {}, "outputs": [], "source": ["# 初始化 Web Driver\n", "init()"]}, {"cell_type": "code", "execution_count": null, "id": "ba16afde", "metadata": {}, "outputs": [], "source": ["# 取得每個分頁連結\n", "getPagination()"]}, {"cell_type": "code", "execution_count": null, "id": "0a34d3a1", "metadata": {"scrolled": false}, "outputs": [], "source": ["# 剖析內容\n", "parse()"]}, {"cell_type": "code", "execution_count": null, "id": "502dfef4", "metadata": {}, "outputs": [], "source": ["# 關閉瀏覽器\n", "close()"]}, {"cell_type": "code", "execution_count": null, "id": "a7e45ad3", "metadata": {}, "outputs": [], "source": ["# 儲存成 json\n", "<PERSON><PERSON><PERSON>()"]}, {"cell_type": "code", "execution_count": null, "id": "9dca0272", "metadata": {}, "outputs": [], "source": ["# 儲存 .db\n", "saveDB()"]}, {"cell_type": "code", "execution_count": null, "id": "69e99600", "metadata": {}, "outputs": [], "source": ["# 下載\n", "download()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 5}