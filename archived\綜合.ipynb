{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 練習: 將 list 當中重複的 dict 去除，並透過指定 dict key 來排序"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'id': '380512238',\n", "  'link': 'https://stickershop.line-scdn.net/stickershop/v1/sticker/380512238/android/sticker.png'},\n", " {'id': '380512238',\n", "  'link': 'https://stickershop.line-scdn.net/stickershop/v1/sticker/380512238/android/sticker.png'},\n", " {'id': '380512239',\n", "  'link': 'https://stickershop.line-scdn.net/stickershop/v1/sticker/380512239/android/sticker.png'}]\n"]}], "source": ["import pprint\n", "\n", "'''\n", "流程 1\n", "'''\n", "\n", "# 假設我們有 3 個 dict，每個 dict 都是 LINE 官方貼圖(靜態圖片，無動畫、無聲音)\n", "dict01 = {\n", "    \"link\": \"https://stickershop.line-scdn.net/stickershop/v1/sticker/380512238/android/sticker.png\",\n", "    \"id\": \"380512238\"\n", "}\n", "\n", "dict02 = {\n", "    \"link\": \"https://stickershop.line-scdn.net/stickershop/v1/sticker/380512238/android/sticker.png\",\n", "    \"id\": \"380512238\"\n", "}\n", "\n", "dict03 = {\n", "    \"link\": \"https://stickershop.line-scdn.net/stickershop/v1/sticker/380512239/android/sticker.png\",\n", "    \"id\": \"380512239\"\n", "}\n", "\n", "# 接下來，我們把這三個 dict，都放到一個 list 當中\n", "listLineStickers = []\n", "listLineStickers.append(dict01)\n", "listLineStickers.append(dict02)\n", "listLineStickers.append(dict03)\n", "\n", "# 檢視一下當前內容\n", "pprint.pprint(listLineStickers)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{(('link',\n", "   'https://stickershop.line-scdn.net/stickershop/v1/sticker/380512238/android/sticker.png'),\n", "  ('id', '380512238')),\n", " (('link',\n", "   'https://stickershop.line-scdn.net/stickershop/v1/sticker/380512239/android/sticker.png'),\n", "  ('id', '380512239'))}\n"]}], "source": ["'''\n", "流程 2\n", "'''\n", "# 建立一個 Set 物件，準備 add 所有 tuple，這些 tuple 裡面都有 dict_items 物件\n", "_set = set()\n", "\n", "'''\n", "一、dict.items()\n", "說明:\n", "    items() 方法把字典中每一對 key 和 value 組成一個 tuple\n", "例如:\n", "    dict_items([\n", "        ('link', 'https://stickershop.line-scdn.net/stickershop/v1/sticker/318800558/android/sticker.png'), \n", "        ('id', '318800558')\n", "    ])\n", "\n", "\n", "二、tuple(dict.items())\n", "說明: \n", "    1. 將 dict_items 格式轉成 tuple，目前是為了「讓 set 可以使用 .add() 方法，來去除重複」。\n", "    2. 之所以要將轉換格式，是因為 tuple 可以被新增到 set 當中，dict 和 dict_items 不行. \n", "    3. tuple 是可以雜湊的(hashable)，可雜湊代表「雜湊值不可變動」，不可變動才能拿來判斷是否相同或比較（equal or compare）。\n", "    4. 可變動的資料型態，例如 list 可以 append()、remove()，或是像 dict 等透過指定 key 來新增修改、刪除資料的格式。\n", "例如:\n", "    (\n", "        ('link','https://stickershop.line-scdn.net/stickershop/v1/sticker/318800558/android/sticker.png'),\n", "        ('id', '318800558')\n", "    )\n", "'''\n", "\n", "# 將放置 LINE 貼圖的 dict 各別轉換成為 dict_items 物件，再各別轉換成 tuple，最後新增到 Set 當中\n", "for dictLineSticker in listLineStickers:\n", "    dict_items = dictLineSticker.items()\n", "    _tuple = tuple(dict_items)\n", "    _set.add(_tuple)\n", "\n", "pprint.pprint(_set)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'id': '380512238',\n", "  'link': 'https://stickershop.line-scdn.net/stickershop/v1/sticker/380512238/android/sticker.png'},\n", " {'id': '380512239',\n", "  'link': 'https://stickershop.line-scdn.net/stickershop/v1/sticker/380512239/android/sticker.png'}]\n"]}], "source": ["'''\n", "流程 3\n", "'''\n", "# 新增 list，準備將去掉重複的 dict 資料各別 append 進去\n", "listResult = []\n", "\n", "'''\n", "三、dict(t)\n", "說明:\n", "    原先的 tuple(dict.items()) 的結果，透過 dict() 轉型，變成原先 dict 的 key-value 格式\n", "例如:\n", "    {\n", "        'id': '318800558',\n", "        'link': 'https://stickershop.line-scdn.net/stickershop/v1/sticker/318800558/android/sticker.png'\n", "    }\n", "'''\n", "\n", "# 此時 set 應該已經去除重複的 tuple，此時將 tuple 各別轉回原本的 dict，並寫入新的 list 當中\n", "for _tuple in _set:\n", "    dictLineSticker = dict(_tuple)\n", "    listResult.append(dictLineSticker)\n", "\n", "pprint.pprint(listResult)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'id': '380512238',\n", "  'link': 'https://stickershop.line-scdn.net/stickershop/v1/sticker/380512238/android/sticker.png'},\n", " {'id': '380512239',\n", "  'link': 'https://stickershop.line-scdn.net/stickershop/v1/sticker/380512239/android/sticker.png'}]\n"]}], "source": ["'''\n", "流程 4\n", "'''\n", "# 使用 sorted，並指定每個 dict 當中的 id 索引進行排序\n", "listResult = sorted(listResult, key=lambda myDict: myDict['id'], reverse=False)\n", "\n", "pprint.pprint(listResult)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}}, "nbformat": 4, "nbformat_minor": 4}