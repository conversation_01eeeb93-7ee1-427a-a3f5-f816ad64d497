{"cells": [{"cell_type": "markdown", "id": "76f875a5", "metadata": {}, "source": ["# PyAutoGUI\n", "控制電腦的鍵盤、滑鼠，對圖形化介面進行操作的工具\n", "\n", "## 安裝套件\n", "- 一般安裝 \n", "\n", "`pip install pya<PERSON><PERSON>i`\n", "\n", "\n", "- 影像處理\n", "\n", "```\n", "pip install opencv-python\n", "pip install opencv-contrib-python\n", "```\n", "\n", "Optional: 程式有影像處理的需求，則需要安裝 [OpenCV](https://opencv.org/releases/ \"OpenCV\")\n", "\n", "\n", "- 鍵盤操作\n", "\n", "`pip install keyboard`\n", "\n", "\n", "\n", "\n", "## 使用方法\n", "- 滑鼠功能\n", "\n", "|       方法       |       說明       |\n", "| :-------------- | :-------------- |\n", "| pyautogui.position() | 取得滑鼠游標座標 |\n", "| pyautogui.size() | 目前螢幕大小 |\n", "| pyautogui.onScreen(x, y) | 判斷游標座標是否在螢幕範圍內 |\n", "| pyautogui.PAUSE = 2.5 | 設定 PyAutoGUI 每一個動作間隔 2.5 秒; 預設 0.1 |\n", "| pyautogui.moveTo(x, y, duration=num_seconds) | 移動滑鼠游標到 x,y |\n", "| pyautogui.moveRel(xOffset, yOffset, duration=num_seconds) | 在目前游標座標偏移相對位置 |\n", "| pyautogui.dragTo(x, y, duration=num_seconds, button='left') | 按住滑鼠左鍵，拖曳滑鼠到 x,y|\n", "| pyautogui.dragRel(xOffset, yOffset, duration=num_seconds, button='left') | 按住滑鼠左鍵，在目前游標座標拖曳滑鼠到相對位置 |\n", "| pyautogui.click(x=moveToX, y=moveToY, clicks=num_of_clicks, interval=secs_between_clicks, button='left') | 點擊滑鼠 |\n", "| pyautogui.rightClick(x=moveToX, y=moveToY) | 在指定座標按下滑鼠右鍵 |\n", "| pyautogui.middleClick(x=moveToX, y=moveToY) | 在指定座標按下滑鼠中間鍵 |\n", "| pyautogui.doubleClick(x=moveToX, y=moveToY) | 在指定座標連點兩下滑鼠左鍵 |\n", "| pyautogui.tripleClick(x=moveToX, y=moveToY) | 在指定座標連點三下滑鼠左鍵 |\n", "| pyautogui.scroll(amount_to_scroll, x=moveToX, y=moveToY) | 游標移到 x,y，滾動 amount_to_scroll 次; 正值向上，負值向下 |\n", "| pyautogui.mouseDown(x=moveToX, y=moveToY, button='left') | 在指定座標按下滑鼠按鍵 |\n", "| pyautogui.mouseUp(x=moveToX, y=moveToY, button='left') | 在指定座標放開滑鼠按鍵 |\n", "\n", "\n", "\n", "\n", "- 鍵盤功能\n", "\n", "|       方法       |       說明       |\n", "| :-------------- | :-------------- |\n", "| pyautogui.typewrite('Hello world!\\n', interval=secs_between_keys) | 在遊標所在位置輸入文字 |\n", "| pyautogui.typewrite(['a', 'b', 'c', 'left', 'backspace', 'enter', 'f1'], interval=secs_between_keys) | 在游標所在位置分別輸入鍵盤按鍵 |\n", "| pyautogui.write('Hello world!', interval=0.25) | 每 0.25 秒輸入一次文字，但要先手機將焦點移到文字欄位當中 |\n", "| pyautogui.hotkey('ctrl', 'c') | 組合鍵 ctrl + c (複製) |\n", "| pyautogui.hotkey('ctrl', 'v') | 組合鍵 ctrl + v (貼上) |\n", "| pyautogui.hotkey('ctrl', 'alt', 'delete') | 組合鍵 ctrl + alt + delete |\n", "| pyautogui.keyDown(key_name) | 按下指定鍵 |\n", "| pyautogui.keyUp(key_name) | 放開指定鍵 |\n", "| pyautogui.press('enter') | 按下 enter |\n", "\n", "\n", "\n", "\n", "- 訊息框功能\n", "\n", "|       方法       |       說明       |\n", "| :-------------- | :-------------- |\n", "| pyautogui.alert('This displays some text with an OK button.') | 警示訊息 |\n", "| pyautogui.confirm('This displays text and has an OK and Cancel button.') | 確認訊息 (會回傳值) |\n", "| pyautogui.prompt('This lets the user type in a string and press OK.') | 提示訊息 (會回傳值) |\n", "\n", "\n", "\n", "\n", "- 截圖功能\n", "\n", "|       方法       |       說明       |\n", "| :-------------- | :-------------- |\n", "| pyautogui.screenshot(region=( left, top, width, height)) | 局部螢幕快照。回傳 Pillow or PIL Image 物件 |\n", "| pyautogui.screenshot('foo.png') | 回傳 Pillow or PIL Image 物件，並儲存檔案 |\n", "| pyautogui.locateOnScreen('looksLikeThis.png') | 若畫面上有跟照片相同的圖案，回傳它的 Pillow or PIL Image 物件，並顯示它的 x,y |\n", "| pyautogui.locateAllOnScreen('looksLikeThis.png') | 若畫面上有跟照片相同的圖案，，回傳它的 Pillow or PIL Image 物，此方法會回傳全部圖片的 left, top, width, height |\n", "| pyautogui.locateCenterOnScreen('looksLikeThis.png') | 若畫面上有跟照片相同的圖案，則取得該圖案在螢幕當中的中心點座標 |\n", "| pyautogui.center( pyautogui.locateOnScreen('looksLikeThis.png') ) | 取得 Pillow or PIL Image 物件的中心點座標 |\n", "| pyautogui.pixel(x, y) | 回傳該座標的 (red, green, blu) |\n", "| | |\n", "\n", "\n", "\n", "## Py<PERSON><PERSON><PERSON><PERSON> - key_name 名稱\n", "\n", "```<PERSON>\n", "['\\t', '\\n', '\\r', ' ', '!', '\"', '#', '$', '%', '&', \"'\", '(',\n", "')', '*', '+', ',', '-', '.', '/', '0', '1', '2', '3', '4', '5', '6', '7',\n", "'8', '9', ':', ';', '<', '=', '>', '?', '@', '[', '\\\\', ']', '^', '_', '`',\n", "'a', 'b', 'c', 'd', 'e','f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o',\n", "'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', '{', '|', '}', '~',\n", "'accept', 'add', 'alt', 'altleft', 'altright', 'apps', 'backspace',\n", "'browserback', 'browserfavorites', 'browserforward', 'browserhome',\n", "'browserrefresh', 'browsersearch', 'browserstop', 'capslock', 'clear',\n", "'convert', 'ctrl', 'ctrlleft', 'ctrlright', 'decimal', 'del', 'delete',\n", "'divide', 'down', 'end', 'enter', 'esc', 'escape', 'execute', 'f1', 'f10',\n", "'f11', 'f12', 'f13', 'f14', 'f15', 'f16', 'f17', 'f18', 'f19', 'f2', 'f20',\n", "'f21', 'f22', 'f23', 'f24', 'f3', 'f4', 'f5', 'f6', 'f7', 'f8', 'f9',\n", "'final', 'fn', 'hanguel', 'hangul', 'hanja', 'help', 'home', 'insert', 'junja',\n", "'kana', 'kanji', 'launchapp1', 'launchapp2', 'launchmail',\n", "'launchmediaselect', 'left', 'modechange', 'multiply', 'nexttrack',\n", "'nonconvert', 'num0', 'num1', 'num2', 'num3', 'num4', 'num5', 'num6',\n", "'num7', 'num8', 'num9', 'numlock', 'pagedown', 'pageup', 'pause', 'pgdn',\n", "'pgup', 'playpause', 'prevtrack', 'print', 'printscreen', 'prntscrn',\n", "'prtsc', 'prtscr', 'return', 'right', 'scrolllock', 'select', 'separator',\n", "'shift', 'shiftleft', 'shiftright', 'sleep', 'space', 'stop', 'subtract', 'tab',\n", "'up', 'volumedown', 'volumemute', 'volumeup', 'win', 'winleft', 'winright', 'yen',\n", "'command', 'option', 'optionleft', 'optionright']\n", "```\n", "\n", "\n", "## 參考資料\n", "- [Welcome to PyAutoGUI’s documentation!](https://pyautogui.readthedocs.io/en/latest/index.html \"Welcome to PyAutoGUI’s documentation!\")\n", "-[PyAutoGUI - Installation](https://pyautogui.readthedocs.io/en/latest/install.html \"PyAutoGUI - Installation\")\n", "- [Python：用 PyAutoGUI 來操控滑鼠及鍵盤](https://yang10001.yia.app/wp/2021/03/06/python-%E7%94%A8-pyautogui-%E4%BE%86%E6%93%8D%E6%8E%A7%E6%BB%91%E9%BC%A0%E5%8F%8A%E9%8D%B5%E7%9B%A4/ \"Python：用 PyAutoGUI 來操控滑鼠及鍵盤\")\n", "- [Python自動化工具 – PyAutoGUI 釋放你的雙手](http://13.231.129.69/2020/08/13/python-%E8%87%AA%E5%8B%95%E5%8C%96%E5%B7%A5%E5%85%B7-pyautogui-%E9%87%8B%E6%94%BE%E4%BD%A0%E7%9A%84%E9%9B%99%E6%89%8B/ \"Python自動化工具 – PyAutoGUI 釋放你的雙手\")\n", "- [PyAutoGUI : 使用Python控制電腦](https://yanwei-liu.medium.com/pyautogui-%E4%BD%BF%E7%94%A8python%E6%93%8D%E6%8E%A7%E9%9B%BB%E8%85%A6-662cc3b18b80 \"PyAutoGUI : 使用Python控制電腦\")\n", "- [【PYTHON】pyautogui如何增加每秒的點選次數？](https://www.796t.com/post/ZTVueDA=.html \"【PYTHON】pyautogui如何增加每秒的點選次數？\")\n", "- [pyautogui 文檔（五）：截圖及定位功能](https://www.itread01.com/content/1556528332.html \"pyautogui 文檔（五）：截圖及定位功能\")\n", "\n", "## 補充資料\n", "- [GitHub: keyboard](https://github.com/boppreh/keyboard \"GitHub: keyboard\")\n", "- [GitHub: mouse](https://github.com/boppreh/mouse \"GitHub: mouse\")\n", "\n", "## 延伸議題\n", "- [為應用程式設計圖形化介面，使用Python Tkinter 模組](https://www.rs-online.com/designspark/python-tkinter-cn \"為應用程式設計圖形化介面，使用Python Tkinter 模組\")"]}, {"cell_type": "code", "execution_count": null, "id": "bc985194", "metadata": {}, "outputs": [], "source": ["'''\n", "安裝套件\n", "'''\n", "!pip install pyautogui opencv-python opencv-contrib-python keyboard"]}, {"cell_type": "code", "execution_count": null, "id": "a865e8d6", "metadata": {}, "outputs": [], "source": ["'''\n", "匯入工具\n", "'''\n", "import pyautogui\n", "from time import sleep, time\n", "import keyboard\n", "from IPython.display import clear_output"]}, {"cell_type": "code", "execution_count": null, "id": "851b095b", "metadata": {}, "outputs": [], "source": ["'''\n", "取得目前滑鼠座標\n", "'''\n", "while True:\n", "    #自動清除此格的文字輸出\n", "    clear_output(wait=True)\n", "    \n", "    #取得滑鼠游標的座標，並加以輸出\n", "    x, y = pyautogui.position()\n", "    print(f\"x={x}, y={y}\")\n", "    \n", "    # 睡一下，以免吃光記憶體\n", "    sleep(0.01)"]}, {"cell_type": "code", "execution_count": null, "id": "3c50cfce", "metadata": {}, "outputs": [], "source": ["'''\n", "記錄滑鼠軌跡\n", "'''\n", "\n", "# 記錄軌跡用的變數\n", "listMove = []\n", "\n", "# 記錄軌跡\n", "def record():\n", "    # 監聽事件\n", "    while True:\n", "        if keyboard.is_pressed('ctrl'): # 按下 Ctrl 錄製軌跡，放開就不進行錄製\n", "            x, y = pyautogui.position()\n", "            listMove.append(f\"{x},{y}\")\n", "            sleep(0.01)\n", "        elif keyboard.is_pressed('alt'): # 按下 alt，結束程式\n", "            break\n", "    \n", "    # 寫入檔案\n", "    with open(\"move.txt\", \"w\", encoding=\"utf-8\") as file:\n", "        file.write( '\\n'.join(listMove) )\n", "           \n", "'''\n", "主程式\n", "'''\n", "if __name__ == \"__main__\":\n", "    record()"]}, {"cell_type": "code", "execution_count": null, "id": "034895be", "metadata": {}, "outputs": [], "source": ["'''\n", "讀取滑鼠軌跡，並模擬滑鼠遊標移動\n", "'''\n", "\n", "# 模擬移動\n", "def move():\n", "    # 讀取軌跡檔\n", "    with open(\"move.txt\", \"r\", encoding=\"utf-8\") as file:\n", "        # 逐行讀取\n", "        for line in file:\n", "            # 切割文字，找出 x,y\n", "            x, y = line.strip().split(\",\")\n", "            \n", "            # 移動 (設定移動時間)\n", "            pyautogui.moveTo( int(x), int(y), duration=0.01)\n", "            \n", "            # 提前結束\n", "            if keyboard.is_pressed('alt'):\n", "                break\n", "            \n", "'''\n", "主程式\n", "'''\n", "if __name__ == \"__main__\":\n", "    move()"]}, {"cell_type": "code", "execution_count": null, "id": "00af086d", "metadata": {}, "outputs": [], "source": ["'''\n", "每秒判斷桌面上的圖片(座標)是否存在\n", "補充: 請先開小算盤(計算機)\n", "'''\n", "while True:\n", "    # 睡一下\n", "    sleep(0.01)\n", "    \n", "    #自動清除此格的文字輸出\n", "    clear_output(wait=True)\n", "\n", "    try:\n", "        # 取得 Box 物件\n", "        btn_2_loc = pyautogui.locateOnScreen('images/2.png', confidence=0.9)\n", "        \n", "        # 輸出\n", "        print(btn_2_loc)\n", "    except:\n", "        pass"]}, {"cell_type": "code", "execution_count": null, "id": "947f128d", "metadata": {}, "outputs": [], "source": ["'''\n", "自動使用計算機\n", "\n", "補充: 此時計算機應該明顯出現在桌面上，不然會有判斷錯誤的問題 \n", "'''\n", "\n", "# 設定每一個動作，都暫停若干秒\n", "pyautogui.PAUSE = 0.5\n", "\n", "# 比對所有圖片，取得顯示在桌面的圖片物件 (pillow / PIL Image)\n", "btn_0_loc = pyautogui.locateOnScreen(\n", "    'images/0.png', confidence=0.9\n", ")\n", "btn_1_loc = pyautogui.locateOnScreen(\n", "    'images/1.png', confidence=0.9\n", ")\n", "btn_2_loc = pyautogui.locateOnScreen(\n", "    'images/2.png', confidence=0.9\n", ")\n", "btn_3_loc = pyautogui.locateOnScreen(\n", "    'images/3.png', \n", "    confidence=0.9\n", ")\n", "btn_4_loc = pyautogui.locateOnScreen(\n", "    'images/4.png', \n", "    confidence=0.9\n", ")\n", "btn_5_loc = pyautogui.locateOnScreen(\n", "    'images/5.png', \n", "    confidence=0.9\n", ")\n", "\n", "# 取得每一個圖片的中心點\n", "btn_0_pt = pyautogui.center(btn_0_loc)\n", "btn_1_pt = pyautogui.center(btn_1_loc)\n", "btn_2_pt = pyautogui.center(btn_2_loc)\n", "btn_3_pt = pyautogui.center(btn_3_loc)\n", "btn_4_pt = pyautogui.center(btn_4_loc)\n", "btn_5_pt = pyautogui.center(btn_5_loc)\n", "\n", "# 按下 5201314\n", "pyautogui.click(btn_5_pt.x, btn_5_pt.y)\n", "pyautogui.click(btn_2_pt.x, btn_2_pt.y)\n", "pyautogui.click(btn_0_pt.x, btn_0_pt.y)\n", "pyautogui.click(btn_1_pt.x, btn_1_pt.y)\n", "pyautogui.click(btn_3_pt.x, btn_3_pt.y)\n", "pyautogui.click(btn_1_pt.x, btn_1_pt.y)\n", "pyautogui.click(btn_4_pt.x, btn_4_pt.y)"]}, {"cell_type": "code", "execution_count": null, "id": "4924c973", "metadata": {}, "outputs": [], "source": ["'''\n", "打擊遊戲 - 魔術鋼琴鏄\n", "https://html5.gamedistribution.com/rvvASMiM/44fbc5e5c6e54ac9985d1c81f1fb8121/index.html\n", "'''\n", "\n", "# 設定每一個動作，都暫停若干秒\n", "pyautogui.PAUSE = 0.1\n", "\n", "# 打擊點\n", "listPoint = [\n", "    [620, 600],\n", "    [720, 600],\n", "    [820, 600],\n", "    [920, 600]\n", "]\n", "\n", "# 點擊\n", "def click():\n", "    for _ in listPoint:\n", "        # 取得指定打擊點的 RGB 值\n", "        coord = pyautogui.pixel( _[0], _[1] )\n", "\n", "        # coord[0] 代表 Red，coord[1] 代表 Green，coord[2] 代表 Blue\n", "        if coord[0] == 0 and coord[1] == 0:\n", "            pyautogui.mouseDown(_[0], _[1])\n", "            pyautogui.mouseUp(_[0], _[1])\n", "    \n", "#主程式區域\n", "if __name__ == \"__main__\":\n", "    while True:\n", "        # 當我們按下 ctrl 鍵時，自動點擊，按下 alt 則結束迴圈\n", "        if keyboard.is_pressed('ctrl'):\n", "            click()\n", "        elif keyboard.is_pressed('alt'):\n", "            break"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}