{"cells": [{"cell_type": "code", "execution_count": null, "id": "488c47d0", "metadata": {}, "outputs": [], "source": ["'''匯入套件'''\n", "import requests as req\n", "from bs4 import BeautifulSoup as bs\n", "import json, os, pprint, re\n", "\n", "# 隨機取得 User-Agent\n", "'''\n", "# 從外部資料來取得清單，清單預設儲存路徑: /tmp\n", "ua = UserAgent(use_external_data=True)\n", "# 從外部資料來取得清單，儲存在指定路徑\n", "ua = UserAgent(use_external_data=True, cache_path=/home/<USER>\n", "\n", "更詳細的說明，請見以下網頁:\n", "https://pypi.org/project/fake-useragent/\n", "'''\n", "from fake_useragent import UserAgent\n", "ua = UserAgent(use_external_data=True)\n", "\n", "'''放置 金庸小說 metadata 的資訊'''\n", "listData = []\n", "\n", "'''金庸小說的網址'''\n", "prefix = 'https://tw.ixdzs.com'\n", "url = prefix + '/author/金庸'\n", "\n", "'''設定標頭'''\n", "my_headers = {\n", "    'user-agent': ua.random\n", "}\n", "\n", "# 沒有放置 txt 檔的資料夾，就建立起來\n", "folderPath = 'jinyong'\n", "if not os.path.exists(folderPath):\n", "    os.makedirs(folderPath)"]}, {"cell_type": "code", "execution_count": null, "id": "c90bad95", "metadata": {}, "outputs": [], "source": ["'''\n", "自訂函式\n", "'''\n", "# 取得小說的主要連結\n", "def getMainLinks():    \n", "    # 清除 list 內容\n", "    listData.clear()\n", "    \n", "    # 走訪首頁\n", "    res = req.get(url = url, headers = my_headers)\n", "    soup = bs(res.text, \"lxml\")\n", "    \n", "    # 定義欲取得的小說名稱\n", "    list_novel_names = [\n", "        '倚天屠龍記', '連城訣', '書劍恩仇錄', '碧血劍', '鹿鼎記', \n", "        '俠客行', '射鵰英雄傳', '雪山飛狐', '飛狐外傳', '天龍八部', \n", "        '白馬嘯西風', '神鵰俠侶', '越女劍', '鴛鴦刀', '笑傲江湖'\n", "    ]\n", "    \n", "    # 取得每一本指定的小說連結\n", "    for novel_name in list_novel_names:\n", "        # 取得超連結\n", "        a = soup.select_one(f'a[href][title={novel_name}]')\n", "        \n", "        # 取得主要連結相關資訊\n", "        listData.append({\n", "            'title': a['title'], # 或是 a.get_text() 取得 innerText\n", "            'link': prefix + a['href'],\n", "            'sub': [] # 之後會放置每一本小說的章回資訊\n", "        })\n", "        \n", "    # 預覽結果\n", "    pprint.pprint(listData)\n", "    \n", "\n", "# 取得所有小說的獨立連結\n", "def getSubLinks():\n", "    # 取得章回列表\n", "    for index in range( len(listData) ):\n", "        # 取得 bid\n", "        bid = re.search(r'\\/read\\/(\\d+)\\/', listData[index]['link'])[1]\n", "        \n", "        # post 請求，取得章回列表\n", "        my_data = {'bid': bid}\n", "        res = req.post(url = 'https://tw.ixdzs.com/novel/clist/', data = my_data)\n", "        obj_json = res.json()\n", "        \n", "        # 如果回傳訊息為 200 (這個網站自訂的)\n", "        if obj_json['rs'] == 200:\n", "            # 取得章節連結相關資訊\n", "            for obj_data in obj_json['data']:\n", "                if obj_data['ctype'] != '1':\n", "                    listData[index]['sub'].append({\n", "                        'title': obj_data['title'],\n", "                        'link': prefix + f'/read/{bid}/p{obj_data[\"ordernum\"]}.html',\n", "                        'content': '' # 預留給小說內文\n", "                    })\n", "    \n", "    # 預覽結果\n", "    pprint.pprint(listData)\n", "\n", "\n", "# 建立金庸小說的 json 檔\n", "def save<PERSON><PERSON>():\n", "    with open(f\"{folderPath}/ixdzs_jinyong_post_requests.json\", \"w\", encoding=\"utf-8\") as file:\n", "        file.write( json.dumps(listData, ensure_ascii=False, indent=4) )"]}, {"cell_type": "code", "execution_count": null, "id": "f32ecc2b", "metadata": {"scrolled": false}, "outputs": [], "source": ["# 主程式\n", "if __name__ == \"__main__\":\n", "    getMainLinks()\n", "    getSubLinks()\n", "    <PERSON><PERSON><PERSON>()"]}, {"cell_type": "markdown", "id": "8ae3b392", "metadata": {}, "source": ["# 議題思考\n", "- **這個範例沒有直接抓小說內文，如果是你，該怎麼進去每一個內頁去取得文章？**\n", "\n", "```\n", "參考:\n", "for index in range( len(listData) ):\n", "    for idx in range( len(listData[index]['sub']) ):\n", "        res = req.get(url = listData[index]['sub'][idx]['link'])\n", "        ...\n", "```\n", "\n", "- **有些連結可能沒有用，例如正文、卷○、後記等，當你用 requests 進去瀏覽時，如何略過？**\n", "\n", "```\n", "參考:\n", "res = req.get(url = listData[index]['sub'][idx]['link'])\n", "soup = bs(res.text, \"lxml\")\n", "\n", "# 判斷小說內文的區域是否存在\n", "if len(soup.select('article.page-content')) > 0:\n", "    title = soup.select_one('article.page-content h3').get_text()\n", "    content = soup.select_one('article.page-content section').get_text()\n", "```\n", "\n", "- **如果你能取得小說內文，有辦法儲存在對應的 content 當中嗎？有辦法另外將內文獨立儲存在各別的 txt 檔當中嗎？**\n", "\n", "```\n", "參考:\n", "content = soup.select_one('article.page-content section').get_text()\n", "\n", "# 儲存在對應的 key (content)，作為 value\n", "listData[index]['sub'][idx]['content'] = content\n", "\n", "# 寫入檔案\n", "with open(f\"{folderPath}/{title}.txt\", \"w\", encoding=\"utf-8\") as file:\n", "    file.write(content)\n", "```"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}