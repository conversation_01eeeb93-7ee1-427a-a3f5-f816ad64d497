{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests as req\n", "import os, json\n", "from bs4 import BeautifulSoup as bs\n", "\n", "# 建立儲存圖片的資料夾，不存在就新增\n", "folderPath = 'line_stickers'\n", "if not os.path.exists(folderPath):\n", "    os.makedirs(folderPath)\n", "    \n", "#放貼圖資訊用\n", "list_line_stickers = []\n", "\n", "# 官方 LINE 貼圖的網址\n", "url = 'https://store.line.me/stickershop/product/24329/zh-Hant'\n", "\n", "# 將自訂標頭加入 GET 請求中\n", "res = req.get(url)\n", "\n", "# 建立 soup 物件\n", "soup = bs(res.text, 'lxml')\n", "\n", "'''\n", "備註:\n", "1. soup.select()：回傳的結果是元素集合（list 型態，BeautifulSoup ResultSet）\n", "2. soup.select_one()：回傳的結果是單一元素（BeautifulSoup Result）\n", "'''\n", "# 取得放置貼圖的 li 元素 (list 型態)\n", "li_elements = soup.select(\"ul.mdCMN09Ul.FnStickerList > li.mdCMN09Li.FnStickerPreviewItem\")\n", "\n", "\n", "# 逐一取得 li 元素中的 data-preview 資訊\n", "for li in li_elements:\n", "    # 取得 data-preview 屬性的值(字串)\n", "    strJson = li['data-preview'] # 另一種寫法：li.get(\"data-preview\")\n", "    \n", "    #把屬性的值(字串)轉成物件 \n", "    obj = json.loads(strJson)\n", "    \n", "    # 將重要資訊放置在 list 當中，幫助我們稍候進行資料下載與儲存\n", "    list_line_stickers.append(obj)\n", "\n", "# 下載圖片\n", "'''\n", "範例指令:\n", "$ curl \"https://stickershop.line-scdn.net/stickershop/v1/sticker/658881986/android/sticker.png?v=1\" -o ./test.png\n", "'''\n", "for obj in list_line_stickers: \n", "    os.system(f\"curl {obj['staticUrl']} -o {folderPath}/{obj['id']}.png\")\n", "    print(f\"貼圖ID: {obj['id']}, 下載連結: {obj['staticUrl']}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}, "vscode": {"interpreter": {"hash": "93b032fe74b295ac9f9c7e1fb2471d07c5e0ee3078e59d1cdc5fc80e32fb2057"}}}, "nbformat": 4, "nbformat_minor": 4}