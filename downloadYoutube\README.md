# YouTube 批量下載器

這是一個基於 `yt-dlp` 的 YouTube 影片批量下載工具，可以讀取文字檔案中的 YouTube 網址並批量下載影片。

## 功能特色

- 📁 讀取 `download.txt` 檔案中的 YouTube 網址
- 🔍 自動驗證 YouTube 網址的有效性
- 📥 自動下載 `yt-dlp` 執行檔（如果不存在）
- 🎬 下載最佳品質的 MP4 影片
- 🖼️ 同時下載影片縮圖和資訊檔案
- 📊 顯示下載進度和結果統計
- 💬 支援中文介面

## 系統需求

- Python 3.6 或更高版本
- Windows 作業系統（其他系統需修改 yt-dlp 執行檔路徑）
- 網路連線

## 安裝依賴套件

```bash
pip install wget
```

## 使用方法

### 1. 準備下載清單

編輯 `download.txt` 檔案，每行放一個 YouTube 網址：

```
# 這是註解，會被跳過
https://www.youtube.com/watch?v=VIDEO_ID1
https://youtu.be/VIDEO_ID2
https://www.youtube.com/watch?v=VIDEO_ID3

# 支援的網址格式：
# - https://www.youtube.com/watch?v=VIDEO_ID
# - https://youtu.be/VIDEO_ID  
# - https://m.youtube.com/watch?v=VIDEO_ID
```

### 2. 執行下載

```bash
python youtube_batch_downloader.py
```

或指定自訂的下載清單檔案：

```bash
python youtube_batch_downloader.py my_download_list.txt
```

### 3. 下載結果

- 影片會下載到 `youtube_downloads` 資料夾
- 檔名格式：`影片標題.mp4`
- 同時會下載縮圖（`.jpg`）和影片資訊（`.info.json`）

## 檔案結構

```
.
├── youtube_batch_downloader.py  # 主程式
├── download.txt                 # 下載清單（範例）
├── README.md                   # 說明文件
├── yt-dlp.exe                  # yt-dlp 執行檔（自動下載）
└── youtube_downloads/          # 下載的影片資料夾
    ├── 影片標題1.mp4
    ├── 影片標題1.jpg
    ├── 影片標題1.info.json
    └── ...
```

## 注意事項

1. **網路連線**：需要穩定的網路連線來下載影片
2. **儲存空間**：確保有足夠的硬碟空間儲存影片
3. **版權問題**：請確保您有權下載這些影片，並遵守相關法律法規
4. **下載速度**：下載速度取決於您的網路速度和 YouTube 伺服器狀況

## 錯誤處理

- 無效的 YouTube 網址會被自動跳過
- 下載失敗的影片會顯示錯誤訊息
- 程式結束時會顯示成功/失敗的統計資訊

## 自訂設定

您可以修改 `YouTubeBatchDownloader` 類別的參數來自訂行為：

```python
downloader = YouTubeBatchDownloader(
    download_file='my_list.txt',      # 自訂下載清單檔案
    output_folder='my_videos'         # 自訂輸出資料夾
)
```

## 疑難排解

### 問題：yt-dlp 下載失敗
**解決方法**：手動下載 yt-dlp.exe 並放在程式同一資料夾

### 問題：影片下載失敗
**解決方法**：
1. 檢查網址是否正確
2. 確認網路連線正常
3. 某些影片可能有地區限制或已被刪除

### 問題：中文檔名亂碼
**解決方法**：確保系統支援 UTF-8 編碼

## 授權

本專案僅供學習和個人使用，請遵守 YouTube 的服務條款和相關法律法規。
