{"cells": [{"cell_type": "code", "execution_count": null, "id": "0a06d755", "metadata": {}, "outputs": [], "source": ["'''匯入套件'''\n", "from selenium import webdriver\n", "from selenium.webdriver.chrome.service import Service\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "from selenium.common.exceptions import TimeoutException\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.webdriver.common.by import By\n", "import json, os, time, re\n", "from pprint import pprint\n", "from urllib import parse\n", "\n", "# 隨機取得 User-Agent\n", "from fake_useragent import UserAgent\n", "ua = UserAgent()\n", "\n", "'''設定 Chrome 瀏覽器開啟時的狀態'''\n", "my_options = webdriver.ChromeOptions()\n", "my_options.add_argument('--start-maximized')\n", "my_options.add_argument('--incognito')\n", "my_options.add_argument('--disable-popup-blocking')\n", "my_options.add_argument(f'--user-agent={ua.random}')\n", "\n", "'''建立操控 Chrome 瀏覽器的變數'''\n", "# 使用 Chrome 的 WebDriver\n", "driver = webdriver.Chrome(\n", "    options = my_options,\n", "    service = Service(ChromeDriverManager().install())\n", ")\n", "'''\n", "補充: 沒有特別設定，只要電腦有安裝 Chrome，就可以直接使用\n", "driver = webdriver.Chrome()\n", "'''\n", "\n", "'''放置 金庸小說 metadata 的資訊'''\n", "listData = []\n", "\n", "'''小庸小說的網址'''\n", "url = 'https://www.bookwormzz.com/ky'\n", "\n", "# 沒有放置 txt 檔的資料夾，就建立起來\n", "folderPath = 'jinyong'\n", "if not os.path.exists(folderPath):\n", "    os.makedirs(folderPath)"]}, {"cell_type": "code", "execution_count": null, "id": "9c5a6200", "metadata": {}, "outputs": [], "source": ["# 取得小說的主要連結\n", "def getMainLinks():\n", "    # 走訪首頁\n", "    driver.get(url)\n", "    \n", "    # 取得主要連結\n", "    a_elms = driver.find_elements(By.CSS_SELECTOR, 'a[data-ajax=\"false\"]')\n", "    \n", "    # 整理主要連結資訊\n", "    for a in a_elms:\n", "        listData.append({\n", "            \"title\": a.get_attribute('innerText'),\n", "            \"link\": parse.unquote( a.get_attribute('href') ) + \"#book_toc\",\n", "            \"sub\": [] # 為了放置各個章回小說的內頁資料，下一個步驟會用到\n", "        })\n", "\n", "# 取得所有章回小說的連結\n", "def getSubLinks():\n", "    for i in range( len(listData) ):\n", "        # 走訪章回小說內頁\n", "        driver.get(listData[i][\"link\"])\n", "        \n", "        # 若是走訪網頁時，等待不到特定的元素，視為沒有資料，continue 到 for 的下一個 index 去\n", "        try:\n", "            # 等待元素\n", "            WebDriverWait(driver, 5).until(\n", "                EC.presence_of_element_located(\n", "                    (By.CSS_SELECTOR, 'div[data-theme=\"b\"][data-content-theme=\"c\"] a[rel=\"external\"]')\n", "                )\n", "            )\n", "            \n", "            # 整理章回小說\n", "            a_elms = driver.find_elements(By.CSS_SELECTOR, 'div[data-theme=\"b\"][data-content-theme=\"c\"] a[rel=\"external\"]')\n", "            for a in a_elms:\n", "                listData[i][\"sub\"].append({\n", "                    \"sub_title\": a.get_attribute(\"innerText\"),\n", "                    \"sub_link\": parse.unquote( a.get_attribute(\"href\") )\n", "                })\n", "        except TimeoutException as e:\n", "            continue\n", "\n", "# 建立金庸小說的 json 檔\n", "def save<PERSON><PERSON>():\n", "    with open(f\"{folderPath}/jinyong.json\", \"w\", encoding=\"utf-8\") as file:\n", "        file.write( json.dumps(listData, ensure_ascii=False) )\n", "\n", "# 將金庸小說所有章回的內容，各自寫到 txt 與 json 中\n", "def writeTxt():  \n", "    # 稍候建立 train.json 前的程式變數\n", "    listContent = []\n", "\n", "    # 開啟 金庸小說 metadata 的 json 檔\n", "    with open(f\"{folderPath}/jinyong.json\", \"r\", encoding=\"utf-8\") as file:\n", "        strJson = file.read()\n", "\n", "    # 走訪所有章回的小說文字內容\n", "    listResult = json.loads(strJson)\n", "    for i in range( len(listResult) ):\n", "        for j in range( len(listResult[i][\"sub\"]) ):\n", "            # 走訪內頁\n", "            driver.get( listResult[i]['sub'][j]['sub_link'] )\n", "            div = driver.find_element(By.CSS_SELECTOR, 'div#html > div')\n", "            \n", "            # 取得內文\n", "            strContent = div.get_attribute('innerText')\n", "            \n", "            # 資料預處理\n", "            strContent = re.sub(r\" |\\r|\\n|　|\\s\", '', strContent)\n", "\n", "            # 決定 txt 的檔案名稱\n", "            fileName = f\"{listResult[i]['title']}_{listResult[i]['sub'][j]['sub_title']}.txt\"\n", "\n", "            # 將小說內容存到 txt 中\n", "            with open(f\"{folderPath}/{fileName}\", \"w\", encoding=\"utf-8\") as file:\n", "                file.write( strContent )\n", "\n", "            # 額外將小說內容放到 list 當中，建立 train.json\n", "            listContent.append(strContent)\n", "\n", "    # 延伸之後的教學，在此建立訓練資料\n", "    with open(f\"{folderPath}/train.json\", \"w\", encoding=\"utf-8\") as file:\n", "        file.write( json.dumps(listContent, ensure_ascii=False) )\n", "\n", "# 關閉瀏覽器\n", "def close():\n", "    driver.quit()"]}, {"cell_type": "code", "execution_count": null, "id": "d36a7209", "metadata": {}, "outputs": [], "source": ["# 主程式\n", "if __name__ == \"__main__\":\n", "    time1 = time.time()\n", "    getMainLinks()\n", "    getSubLinks()\n", "    <PERSON><PERSON><PERSON>()\n", "    writeTxt()\n", "    close()\n", "    print(f\"執行總花費時間: {time.time() - time1}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 5}