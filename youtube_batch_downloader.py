#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YouTube 批量下載器
讀取 download.txt 檔案中的 YouTube 網址並批量下載影片
"""

import os
import subprocess
import wget
from urllib.parse import urlparse, parse_qs
import sys

class YouTubeBatchDownloader:
    def __init__(self, download_file='download.txt', output_folder='youtube_downloads'):
        """
        初始化下載器
        
        Args:
            download_file (str): 包含YouTube網址的文字檔案路徑
            output_folder (str): 下載影片的輸出資料夾
        """
        self.download_file = download_file
        self.output_folder = output_folder
        self.yt_dlp_path = './yt-dlp.exe'
        
        # 建立輸出資料夾
        if not os.path.exists(self.output_folder):
            os.makedirs(self.output_folder)
            print(f"已建立輸出資料夾: {self.output_folder}")
    
    def download_yt_dlp(self):
        """下載 yt-dlp 執行檔"""
        if not os.path.exists(self.yt_dlp_path):
            print('[正在下載 yt-dlp...]')
            try:
                wget.download(
                    'https://github.com/yt-dlp/yt-dlp/releases/latest/download/yt-dlp.exe', 
                    self.yt_dlp_path
                )
                print('\n[yt-dlp 下載完成]')
            except Exception as e:
                print(f'\n[錯誤] 下載 yt-dlp 失敗: {e}')
                return False
        else:
            print('[yt-dlp 已存在，跳過下載]')
        return True
    
    def validate_youtube_url(self, url):
        """
        驗證是否為有效的YouTube網址
        
        Args:
            url (str): 要驗證的網址
            
        Returns:
            bool: 是否為有效的YouTube網址
        """
        try:
            parsed_url = urlparse(url.strip())
            if parsed_url.netloc in ['www.youtube.com', 'youtube.com', 'youtu.be', 'm.youtube.com']:
                return True
            return False
        except:
            return False
    
    def read_download_list(self):
        """
        讀取 download.txt 檔案中的YouTube網址
        
        Returns:
            list: 有效的YouTube網址列表
        """
        if not os.path.exists(self.download_file):
            print(f'[錯誤] 找不到檔案: {self.download_file}')
            return []
        
        urls = []
        try:
            with open(self.download_file, 'r', encoding='utf-8') as file:
                lines = file.readlines()
                
            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                
                # 跳過空行和註解行
                if not line or line.startswith('#'):
                    continue
                
                if self.validate_youtube_url(line):
                    urls.append(line)
                    print(f'[第{line_num}行] 有效網址: {line}')
                else:
                    print(f'[第{line_num}行] 無效網址，已跳過: {line}')
                    
        except Exception as e:
            print(f'[錯誤] 讀取檔案失敗: {e}')
            return []
        
        print(f'\n總共找到 {len(urls)} 個有效的YouTube網址')
        return urls
    
    def download_video(self, url, index, total):
        """
        下載單個YouTube影片
        
        Args:
            url (str): YouTube網址
            index (int): 當前下載的索引
            total (int): 總下載數量
            
        Returns:
            bool: 下載是否成功
        """
        print(f'\n[{index}/{total}] 正在下載: {url}')
        
        # 定義下載指令
        cmd = [
            self.yt_dlp_path,
            url,
            '-f', 'best[ext=mp4]/best',  # 優先下載mp4格式，否則下載最佳品質
            '-o', f'{self.output_folder}/%(title)s.%(ext)s',  # 使用影片標題作為檔名
            '--no-playlist',  # 不下載播放清單
            '--write-info-json',  # 寫入影片資訊JSON檔
            '--write-thumbnail',  # 下載縮圖
        ]
        
        try:
            # 執行下載指令
            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
            
            if result.returncode == 0:
                print(f'[{index}/{total}] ✅ 下載成功!')
                return True
            else:
                print(f'[{index}/{total}] ❌ 下載失敗!')
                print(f'錯誤訊息: {result.stderr}')
                return False
                
        except Exception as e:
            print(f'[{index}/{total}] ❌ 下載過程發生錯誤: {e}')
            return False
    
    def batch_download(self):
        """執行批量下載"""
        print('=== YouTube 批量下載器 ===\n')
        
        # 下載 yt-dlp
        if not self.download_yt_dlp():
            return
        
        # 讀取下載清單
        urls = self.read_download_list()
        if not urls:
            print('沒有找到有效的YouTube網址，程式結束。')
            return
        
        # 確認是否繼續
        user_input = input(f'\n是否要下載這 {len(urls)} 個影片？(y/N): ')
        if user_input.lower() not in ['y', 'yes', '是']:
            print('已取消下載。')
            return
        
        # 開始批量下載
        print('\n開始批量下載...\n')
        success_count = 0
        failed_count = 0
        
        for index, url in enumerate(urls, 1):
            if self.download_video(url, index, len(urls)):
                success_count += 1
            else:
                failed_count += 1
        
        # 顯示下載結果統計
        print('\n=== 下載完成 ===')
        print(f'成功下載: {success_count} 個影片')
        print(f'下載失敗: {failed_count} 個影片')
        print(f'總計處理: {len(urls)} 個網址')
        print(f'影片儲存位置: {os.path.abspath(self.output_folder)}')

def main():
    """主程式"""
    # 檢查命令列參數
    download_file = 'download.txt'
    if len(sys.argv) > 1:
        download_file = sys.argv[1]
    
    # 建立下載器並執行
    downloader = YouTubeBatchDownloader(download_file=download_file)
    downloader.batch_download()

if __name__ == '__main__':
    main()
