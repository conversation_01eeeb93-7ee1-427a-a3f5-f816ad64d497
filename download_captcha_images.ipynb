{"cells": [{"cell_type": "code", "execution_count": null, "id": "846a7b48", "metadata": {}, "outputs": [], "source": ["!pip install -U openpyxl selenium beautifulsoup4 lxml requests python-dotenv tika"]}, {"cell_type": "code", "execution_count": null, "id": "512e5809", "metadata": {}, "outputs": [], "source": ["'''\n", "注意事項:\n", "下載對應的 ChromeDriver (web driver) 到程式檔案同一個目錄下後解壓縮，下載前記得對應版本編號。\n", "連結: https://chromedriver.chromium.org/downloads\n", "'''\n", "\n", "\n", "'''\n", "匯入套件\n", "'''\n", "# 網路請求工具\n", "import requests as req\n", "\n", "# HTML parser\n", "from bs4 import BeautifulSoup as bs\n", "\n", "# 操作 browser 的 API\n", "from selenium import webdriver\n", "from selenium.webdriver.chrome.service import Service\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "# 處理逾時例外的工具\n", "from selenium.common.exceptions import TimeoutException\n", "\n", "# 面對動態網頁，等待某個元素出現的工具，通常與 exptected_conditions 搭配\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "\n", "# 搭配 WebDriverWait 使用，對元素狀態的一種期待條件，若條件發生，則等待結束，往下一行執行\n", "from selenium.webdriver.support import expected_conditions as EC\n", "\n", "# 期待元素出現要透過什麼方式指定，通常與 EC、WebDriverWait 一起使用\n", "from selenium.webdriver.common.by import By\n", "\n", "# 處理下拉式選單的工具\n", "from selenium.webdriver.support.ui import Select\n", "\n", "# 強制等待 (執行期間休息一下)\n", "from time import sleep\n", "\n", "# pretty-print\n", "from pprint import pprint\n", "\n", "# 隨機\n", "from random import randint\n", "\n", "# 計時\n", "import time\n", "\n", "# 整理 json 使用的工具\n", "import json\n", "\n", "# 執行 shell command 的時候用的\n", "import os\n", "\n", "# 取得錯誤訊息\n", "import sys, traceback\n", "\n", "# 子處理程序，用來取代 os.system 的功能\n", "import subprocess\n", "\n", "# 正規表達式\n", "import re\n", "\n", "# 編碼\n", "from urllib.parse import quote\n", "\n", "# SQLite 資料庫\n", "import sqlite3\n", "\n", "# 存取 Excel 的工具\n", "from openpyxl import load_workbook\n", "from openpyxl import Workbook\n", "\n", "# 取得系統時間的工具\n", "from datetime import datetime\n", "\n", "# 引入 hashlib 模組\n", "import hashlib\n", "\n", "# 高階文件操作工具\n", "import shutil\n", "\n", "# 檔案剖析工具\n", "from tika import parser\n", "\n", "# 讀取 .env\n", "from dotenv import load_dotenv\n", "\n", "# 取得網路資源的工具\n", "import urllib \n", "\n", "# 隨機取得 User-Agent\n", "from fake_useragent import UserAgent\n", "ua = UserAgent(cache=True) # cache=True 表示從已經儲存的列表中提取"]}, {"cell_type": "code", "execution_count": null, "id": "35d29bc1", "metadata": {}, "outputs": [], "source": ["# 啟動瀏覽器工具的選項\n", "my_options = webdriver.ChromeOptions()\n", "# my_options.add_argument(\"--headless\")             #不開啟實體瀏覽器背景執行\n", "my_options.add_argument(\"--start-maximized\")        #最大化視窗\n", "my_options.add_argument(\"--incognito\")              #開啟無痕模式\n", "my_options.add_argument(\"--disable-popup-blocking\") #禁用彈出攔截\n", "my_options.add_argument(\"--disable-notifications\")  #取消通知\n", "my_options.add_argument(\"--lang=zh-TW\")  #設定為正體中文\n", "my_options.add_argument('--disable-gpu')\n", "my_options.add_argument('--disable-software-rasterizer')\n", "my_options.add_argument('--user-agent=\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Safari/537.36\"')\n", "\n", "# 給 web driver 用的變數\n", "driver = None\n", "\n", "# 來源首頁\n", "root_url = 'https://ndltd.ncl.edu.tw'\n", "prefix_url = root_url + ''\n", "path_url = prefix_url + ''\n", "url = path_url + ''\n", "\n", "# 指定 sheet name\n", "folderName = sheetName = 'ndltd_ncl_edu_tw'\n", "\n", "# 指定 json 檔名\n", "jsonFileName = f'{folderName}.json'\n", "\n", "# 建立儲存檔案用的資料夾\n", "folderPath = f'./{folderName}'\n", "if not os.path.exists(folderPath):\n", "    os.makedirs(folderPath)\n", "    \n", "# 設定 Chrome 下載路徑 (需要絕對路徑)\n", "fullDownloadPath = os.getcwd() + '\\\\' + folderName\n", "\n", "#預設下載路徑\n", "my_options.add_experimental_option(\"prefs\", {\n", "    \"download.default_directory\": fullDownloadPath,\n", "    \"download.prompt_for_download\": <PERSON>als<PERSON>,\n", "    \"download.directory_upgrade\": True,\n", "    \"safebrowsing_for_trusted_sources_enabled\": False,\n", "    \"safebrowsing.enabled\": <PERSON><PERSON><PERSON>,\n", "    \"plugins.always_open_pdf_externally\": True\n", "})\n", "\n", "# 請求標頭\n", "my_headers = {\n", "    'User-Agent': ua.random\n", "}\n", "\n", "# 放置爬取的資料\n", "listData = []"]}, {"cell_type": "code", "execution_count": null, "id": "15b089a8", "metadata": {}, "outputs": [], "source": ["'''\n", "函式\n", "'''\n", "# md5 (用來為每一筆資料建立唯一代號)\n", "def md5(string):\n", "    m = hashlib.md5()\n", "    m.update(string.encode(\"utf-8\"))\n", "    return m.hexdigest()\n", "\n", "# 初始化 Web Driver\n", "def init():\n", "    global driver\n", "    # 使用 Chrome 的 WebDriver\n", "    \n", "    driver = webdriver.Chrome( \n", "        options = my_options, \n", "        service = Service(ChromeDriverManager().install())\n", "    )\n", "    \n", "# 走訪頁面\n", "def visit():\n", "    global driver\n", "    \n", "    try:\n", "        # 走訪首頁\n", "        driver.get(url)\n", "\n", "        # 等待目標元素出現\n", "        WebDriverWait(driver, 10).until(\n", "            EC.presence_of_element_located(\n", "                (By.CSS_SELECTOR, 'div.user_area a[href][title=\"登入\"]')\n", "            )\n", "        )\n", "        \n", "        # 點按登入連結\n", "        driver.find_element(By.CSS_SELECTOR, 'div.user_area a[href][title=\"登入\"]').click()\n", "        \n", "        # 等待目標元素出現\n", "        WebDriverWait(driver, 10).until(\n", "            EC.presence_of_element_located(\n", "                (By.CSS_SELECTOR, 'input[type=\"text\"][name=\"userid\"]')\n", "            )\n", "        )\n", "        \n", "        # 讀取登入資訊\n", "        load_dotenv()\n", "        EMAIL = os.getenv(\"EMAIL\")\n", "        PWD = os.getenv(\"PWD\")\n", "\n", "        # 輸入 e-mail\n", "        driver.find_element(By.CSS_SELECTOR, 'input[type=\"text\"][name=\"userid\"]').send_keys(EMAIL)\n", "        \n", "        # 輸入 password\n", "        driver.find_element(By.CSS_SELECTOR, 'input[type=\"password\"][name=\"passwd\"]').send_keys(EMAIL)\n", "\n", "        \n", "    except TimeoutException as e:\n", "        print('等待逾時: visit')\n", "        \n", "# 下載驗證碼圖片\n", "def download():\n", "    global driver\n", "    \n", "    try:\n", "        # 等待目標元素出現\n", "        WebDriverWait(driver, 10).until(\n", "            EC.presence_of_element_located(\n", "                (By.CSS_SELECTOR, 'div.dispcheckimg_div img[src]')\n", "            )\n", "        )\n", "        \n", "        # 過濾重複的圖片\n", "        mySet = set()\n", "        \n", "        # 要抓幾張\n", "        count = 0\n", "        limit = 1000\n", "    \n", "        # 下載圖片\n", "        while count < limit:\n", "            # 等待目標元素出現\n", "            WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located(\n", "                    (By.CSS_SELECTOR, 'div.dispcheckimg_div img[src]')\n", "                )\n", "            )\n", "            \n", "            # 取得圖片元素\n", "            img_captcha = driver.find_element(By.CSS_SELECTOR, 'div.dispcheckimg_div img[src]')\n", "            \n", "            # 確認圖片是否完整讀取\n", "            if img_captcha.size['width'] == 0:\n", "                continue\n", "            \n", "            # 取得圖片 base64，轉成 md5\n", "            str_md5 = md5(img_captcha.screenshot_as_base64)\n", "            \n", "            # 若是該圖片先前沒有出現過，才進行下載\n", "            if str_md5 not in mySet:\n", "                # 儲存圖片\n", "                img_captcha.screenshot(f'{fullDownloadPath}/{count}.png')\n", "                \n", "                # 將 md5 加到 set 中\n", "                mySet.add(str_md5)\n", "                \n", "                # 計數\n", "                count += 1\n", "\n", "            # 更換圖片\n", "            driver.find_element(By.CSS_SELECTOR, 'div.dispcheckimg_div a[href][title=\"更換驗證碼\"]').click()  \n", "            \n", "    except TimeoutException as e:\n", "        print('等待逾時: download()')\n", "\n", "        \n", "# 關閉瀏覽器\n", "def close():\n", "    global driver\n", "    driver.quit()\n"]}, {"cell_type": "code", "execution_count": null, "id": "850c4014", "metadata": {}, "outputs": [], "source": ["init()"]}, {"cell_type": "code", "execution_count": null, "id": "ad2ce93b", "metadata": {}, "outputs": [], "source": ["visit()"]}, {"cell_type": "code", "execution_count": null, "id": "feb75c51", "metadata": {}, "outputs": [], "source": ["time_begin = time.time()\n", "download()\n", "time_end = time.time()\n", "print(f\"總共執行了 { time_end - time_begin } 秒\")"]}, {"cell_type": "code", "execution_count": null, "id": "bc0af687", "metadata": {}, "outputs": [], "source": ["close()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 5}