{"cells": [{"cell_type": "code", "execution_count": null, "id": "ee8d4a35", "metadata": {}, "outputs": [], "source": ["!pip install -U openpyxl selenium beautifulsoup4 lxml requests tika"]}, {"cell_type": "code", "execution_count": null, "id": "d9d73e86", "metadata": {}, "outputs": [], "source": ["'''\n", "注意事項:\n", "下載對應的 ChromeDriver (web driver) 到程式檔案同一個目錄下後解壓縮，下載前記得對應版本編號。\n", "連結: https://chromedriver.chromium.org/downloads\n", "\n", "參考網頁:\n", "[1] 成大歷史學報\n", "https://his.ncku.edu.tw/p/412-1190-26290.php?Lang=zh-tw\n", "[2] sqlite3 --- SQLite 数据库 DB-API 2.0 接口模块\n", "https://docs.python.org/zh-tw/3/library/sqlite3.html\n", "'''\n", "\n", "\n", "'''\n", "匯入套件\n", "'''\n", "# HTML parser\n", "from bs4 import BeautifulSoup as bs\n", "\n", "# 操作 browser 的 API\n", "from selenium import webdriver\n", "\n", "# 處理逾時例外的工具\n", "from selenium.common.exceptions import TimeoutException\n", "\n", "# 面對動態網頁，等待某個元素出現的工具，通常與 exptected_conditions 搭配\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "\n", "# 搭配 WebDriverWait 使用，對元素狀態的一種期待條件，若條件發生，則等待結束，往下一行執行\n", "from selenium.webdriver.support import expected_conditions as EC\n", "\n", "# 期待元素出現要透過什麼方式指定，通常與 EC、WebDriverWait 一起使用\n", "from selenium.webdriver.common.by import By\n", "\n", "# 處理下拉式選單的工具\n", "from selenium.webdriver.support.ui import Select\n", "\n", "# 強制等待 (執行期間休息一下)\n", "from time import sleep\n", "\n", "# pretty-print\n", "from pprint import pprint\n", "\n", "# 隨機\n", "from random import randint\n", "\n", "# 計時\n", "import time\n", "\n", "# 整理 json 使用的工具\n", "import json\n", "\n", "# 執行 shell command 的時候用的\n", "import os\n", "\n", "# 取得錯誤訊息\n", "import sys, traceback\n", "\n", "# 子處理程序，用來取代 os.system 的功能\n", "import subprocess\n", "\n", "# 正規表達式\n", "import re\n", "\n", "# 編碼\n", "from urllib.parse import quote\n", "\n", "# SQLite 資料庫\n", "import sqlite3\n", "\n", "# 存取 Excel 的工具\n", "from openpyxl import load_workbook\n", "from openpyxl import Workbook\n", "\n", "# 取得系統時間的工具\n", "from datetime import datetime\n", "\n", "# 引入 hashlib 模組\n", "import hashlib\n", "\n", "# 高階文件操作工具\n", "import shutil\n", "\n", "# 檔案剖析工具\n", "from tika import parser"]}, {"cell_type": "code", "execution_count": null, "id": "7592ab4c", "metadata": {}, "outputs": [], "source": ["# 啟動瀏覽器工具的選項\n", "my_options = webdriver.ChromeOptions()\n", "# my_options.add_argument(\"--headless\")             #不開啟實體瀏覽器背景執行\n", "my_options.add_argument(\"--start-maximized\")        #最大化視窗\n", "my_options.add_argument(\"--incognito\")              #開啟無痕模式\n", "my_options.add_argument(\"--disable-popup-blocking\") #禁用彈出攔截\n", "my_options.add_argument(\"--disable-notifications\")  #取消通知\n", "my_options.add_argument(\"--lang=zh-TW\")  #設定為正體中文\n", "my_options.add_argument('--disable-gpu')\n", "my_options.add_argument('--disable-software-rasterizer')\n", "my_options.add_argument('--user-agent=\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Safari/537.36\"')\n", "\n", "# 指定 chromedriver 檔案的路徑\n", "driver_exec_path = './chromedriver.exe'\n", "\n", "# 給 web driver 用的變數\n", "driver = None\n", "\n", "# 來源首頁\n", "prefix_url = 'https://his.ncku.edu.tw'\n", "url = prefix_url + '/p/412-1190-26290.php?Lang=zh-tw'\n", "\n", "# 指定 sheet name\n", "folderName = sheetName = 'his_ncku_edu_tw'\n", "\n", "# 指定 json 檔名\n", "jsonFileName = f'{folderName}.json'\n", "\n", "# 建立儲存檔案用的資料夾\n", "folderPath = f'./{folderName}'\n", "if not os.path.exists(folderPath):\n", "    os.makedirs(folderPath)\n", "    \n", "# 設定 Chrome 下載路徑 (需要絕對路徑)\n", "fullDownloadPath = os.getcwd() + '\\\\' + folderName\n", "\n", "#預設下載路徑\n", "my_options.add_experimental_option(\"prefs\", {\n", "    \"download.default_directory\": fullDownloadPath,\n", "    \"download.prompt_for_download\": <PERSON>als<PERSON>,\n", "    \"download.directory_upgrade\": True,\n", "    \"safebrowsing_for_trusted_sources_enabled\": False,\n", "    \"safebrowsing.enabled\": <PERSON><PERSON><PERSON>,\n", "    \"plugins.always_open_pdf_externally\": True\n", "})\n", "\n", "# 放置爬取的資料\n", "listData = []"]}, {"cell_type": "code", "execution_count": null, "id": "08a1943b", "metadata": {}, "outputs": [], "source": ["'''\n", "函式\n", "'''\n", "# md5 (用來為每一筆資料建立唯一代號)\n", "def md5(string):\n", "    m = hashlib.md5()\n", "    m.update(string.encode(\"utf-8\"))\n", "    return m.hexdigest()\n", "\n", "# 初始化 Web Driver\n", "def init():\n", "    global driver, listData\n", "    \n", "    # 放置資料的變數初始化\n", "    listData = []\n", "    \n", "    # 使用 Chrome 的 WebDriver\n", "    driver = webdriver.Chrome( \n", "        options = my_options, \n", "        executable_path = driver_exec_path\n", "    )\n", "    \n", "    \n", "# 走訪頁面\n", "def visit():\n", "    global driver\n", "    \n", "    try:\n", "        # 走訪首頁\n", "        driver.get(url)\n", "\n", "        # 等待目標元素出現\n", "        WebDriverWait(driver, 10).until(\n", "            EC.presence_of_element_located(\n", "                (By.CSS_SELECTOR, 'div#pageptlist')\n", "            )\n", "        )\n", "        \n", "    except TimeoutException as e:\n", "        print('等待逾時: visit')\n", "        \n", "# 滾動頁面\n", "def scroll():\n", "    '''\n", "    innerHeight => 瀏覽器內部的高度\n", "    offset => 當前捲動的量(高度)\n", "    count => 累計無效滾動次數\n", "    limit => 最大無效滾動次數\n", "    '''\n", "    innerHeight = 0\n", "    offset = 0\n", "    count = 0\n", "    limit = 3\n", "    \n", "    try:\n", "        # 在捲動到沒有元素動態產生前，持續捲動\n", "        while count <= limit:\n", "            # 每次移動高度\n", "            offset = driver.execute_script(\n", "                'return window.document.documentElement.scrollHeight;'\n", "            )\n", "\n", "            '''\n", "            或是每次只滾動一點距離，\n", "            以免有些網站會在移動長距離後，\n", "            將先前移動當中的元素隱藏\n", "\n", "            例如將上方的 script 改成:\n", "            offset += 600\n", "            '''\n", "\n", "            # 捲軸往下滑動\n", "            driver.execute_script(f'''\n", "                window.scrollTo({{\n", "                    top: {offset}, \n", "                    behavior: 'smooth' \n", "                }});\n", "            ''')\n", "\n", "            # 強制等待，此時若有新元素生成，瀏覽器內部高度會自動增加\n", "            sleep(1)\n", "\n", "            # 透過執行 js 語法來取得捲動後的當前總高度\n", "            innerHeight = driver.execute_script(\n", "                'return window.document.documentElement.scrollHeight;'\n", "            );\n", "\n", "            # 經過計算，如果滾動距離(offset)大於等於視窗內部總高度(innerHeight)，代表已經到底了\n", "            if offset == innerHeight:\n", "                count += 1\n", "            else:\n", "                count = 0\n", "    except SyntaxError:\n", "        print('語法錯誤: scroll')\n", "        \n", "# 剖析元素資料\n", "def parse():\n", "    global driver, listData\n", "    try:\n", "        # 等待目標元素出現\n", "        WebDriverWait(driver, 10).until(\n", "            EC.presence_of_element_located(\n", "                (By.CSS_SELECTOR, 'div#pageptlist div.listBS div.mtitle a')\n", "            )\n", "        )\n", "        \n", "        # 取得各期文章 \n", "        for a in driver.find_elements(By.CSS_SELECTOR, 'div#pageptlist div.listBS div.mtitle a'):\n", "            # 刊號\n", "            publish_num = a.get_attribute('innerText').strip()\n", "            \n", "            # 內頁連結\n", "            link = a.get_attribute('href')\n", "            \n", "            # 整理資料\n", "            listData.append({\n", "                'journal_title': '成大歷史學報',\n", "                'publish_num': publish_num,\n", "                'link': link\n", "            })\n", "            \n", "        # 取得內頁中所有 pdf 資訊\n", "        for index, myDict in enumerate(listData):\n", "            # 如果沒有內頁資料，則另外新增\n", "            if not 'sub' in listData[index]:\n", "                listData[index]['sub'] = []\n", "            \n", "            # 走訪內頁\n", "            driver.get(myDict['link'])\n", "            \n", "            # 等待目標元素出現\n", "            WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located(\n", "                    (By.CSS_SELECTOR, 'ul.mptattach li a')\n", "                )\n", "            )\n", "            \n", "            # 強制停止\n", "            sleep(1)\n", "            \n", "            # 取得 pdf 資訊\n", "            for a in driver.find_elements(By.CSS_SELECTOR, 'ul.mptattach li a'):\n", "                # 篇名\n", "                journal_sub_title = a.get_attribute('innerText').strip()\n", "                \n", "                # pdf 連結\n", "                pdf_link = a.get_attribute('href')\n", "                \n", "                # 整理資料\n", "                listData[index]['sub'].append({\n", "                    'id': md5(pdf_link),\n", "                    'journal_sub_title': journal_sub_title,\n", "                    'pdf_link': pdf_link\n", "                })\n", "\n", "    except TimeoutException as e:\n", "        print('等待逾時: parse')\n", "        \n", "# 取得出版日期\n", "def getPublishDate():\n", "    global listData\n", "    \n", "    # 過濾出版日期\n", "    regexPublishDate = r'\\d{4}\\s?年\\s?\\d{1,2}\\s?月'\n", "\n", "    # 嘗試取得出版日期\n", "    for index, myDict in enumerate(listData):\n", "        for idx, d in enumerate(listData[index]['sub']):\n", "            # 出版日期\n", "            listData[index]['sub'][idx]['publish_date'] = ''\n", "\n", "            # 開啟 pdf 檔案，取得出版日期\n", "            content = parser.from_file(d[\"pdf_link\"])['content']\n", "            if content != None:\n", "                matchPublishDate = re.search(regexPublishDate, content)\n", "                if matchPublishDate != None:\n", "                    # 整理資料\n", "                    listData[index]['sub'][idx]['publish_date'] = matchPublishDate[0]\n", "\n", "# 關閉瀏覽器\n", "def close():\n", "    global driver\n", "    driver.quit()\n", "        \n", "# 儲存成 json\n", "def save<PERSON><PERSON>():\n", "    global listData\n", "    with open(f\"{folderPath}/{jsonFileName}\", \"w\", encoding=\"utf-8\") as file:\n", "        file.write( json.dumps( listData, ensure_ascii=False, indent=4 ) )\n", "    \n", "# 儲存 .db\n", "def saveDB():\n", "    with open(f\"{folderPath}/{jsonFileName}\", \"r\", encoding=\"utf-8\") as file:      \n", "        # 取得 json 內容\n", "        strJson = file.read()\n", "        \n", "        # 將 json 轉成 list\n", "        listJson = json.loads(strJson)\n", "        \n", "    # 寫入對話記錄\n", "    conn = sqlite3.connect(f\"{folderPath}/{folderName}.db\")\n", "    \n", "    # 建立 cursor 物件\n", "    cursor = conn.cursor()\n", "\n", "    # 執行 SQL 語法\n", "    try:\n", "        # 查詢特定資料，看看是否已經存在於資料表當中\n", "        sql_query = f'''\n", "        SELECT 1\n", "        FROM journals\n", "        WHERE id = ?\n", "        '''\n", "        \n", "        # 寫入資料\n", "        sql_insert = f'''\n", "        INSERT INTO journals (\n", "            id, journal_title, publish_num, publish_date, journal_sub_title, \n", "            link, pdf_link, is_downloaded, created_at, updated_at\n", "        ) VALUES ( \n", "            ?,?,?,?,?,\n", "            ?,?,?,?,?\n", "        )\n", "        '''\n", "        \n", "        # 放置準備寫入的資料\n", "        list_insert = []\n", "        \n", "        # 將 json 資料一筆一筆找出來\n", "        for myDict in list<PERSON><PERSON>:\n", "            # 每一筆資料裡面有 sub 屬性，裡面有內頁的 pdf 連結等資訊，所以也要找出來\n", "            for d in myDict['sub']:\n", "                # 如果資料庫沒有這筆資料(透過 id)，則暫時將資料以 tuple 格式放到 list 當中\n", "                if cursor.execute(sql_query, (d[\"id\"],)).fetchone() == None:\n", "                    list_insert.append((\n", "                        d['id'],\n", "                        myDict['journal_title'],\n", "                        myDict['publish_num'],\n", "                        d['publish_date'],\n", "                        d['journal_sub_title'],\n", "                        myDict['link'],\n", "                        d['pdf_link'],   \n", "                        0,\n", "                        datetime.today().strftime(\"%Y-%m-%d %H-%M-%S\"),\n", "                        datetime.today().strftime(\"%Y-%m-%d %H-%M-%S\")\n", "                    ))\n", "        \n", "        # 新增資料到資料庫當中\n", "        cursor.executemany(sql_insert, list_insert)\n", "        \n", "        # 執行 SQL 語法\n", "        conn.commit()\n", "    except sqlite3.<PERSON>rror as err: \n", "        # 回滾\n", "        conn.rollback()\n", "\n", "        # SQLite3 例外處理\n", "        exc_type, exc_value, exc_tb = sys.exc_info()\n", "        strErrorMsg = f'''SQLite error: {' '.join(err.args)}\\n\\n\n", "        SQLite traceback: {traceback.format_exception(exc_type, exc_value, exc_tb)}\n", "        '''\n", "        print(strErrorMsg)\n", "    finally:\n", "        # 關閉 sqlite\n", "        conn.close()\n", "    \n", "# 下載\n", "def download():\n", "    # 寫入對話記錄\n", "    conn = sqlite3.connect(f\"{folderPath}/{folderName}.db\")\n", "    \n", "    # 將查詢出來的結果 (tuple)，變成 key-value 型式 (dict)\n", "    conn.row_factory = sqlite3.Row\n", "    \n", "    # 建立 cursor 物件\n", "    cursor = conn.cursor()\n", "\n", "    # 執行 SQL 語法\n", "    try:\n", "        # 查詢尚未下載的資料\n", "        sql_query = f'''\n", "        SELECT sn, id, pdf_link\n", "        FROM journals\n", "        WHERE `is_downloaded` = 0\n", "        '''\n", "        \n", "        # 更新資料的欄位(是否下載過)\n", "        sql_update = f'''\n", "        UPDATE `journals` \n", "        SET \n", "            `is_downloaded` = 1 ,\n", "            `updated_at` = ?\n", "        WHERE `id` = ?\n", "        '''\n", "            \n", "        # 取得所有未下載的資料\n", "        for myDict in cursor.execute(sql_query).fetchall():\n", "            # 等待\n", "            sleep(randint(1,2))\n", "\n", "            # 下載 pdf\n", "            cmd = [\n", "                'curl', \n", "                '-H', 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Safari/537.36',\n", "                '-L', myDict[\"pdf_link\"], \n", "                '-o', f'{folderPath}/{myDict[\"sn\"]}_{myDict[\"id\"]}.pdf'\n", "            ]\n", "            result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT)\n", "            #output = result.stdout\n", "            #pprint(output)\n", "            print(f'{folderPath}/{myDict[\"sn\"]}_{myDict[\"id\"]}.pdf')\n", "\n", "            # 將 is_downloaded 改成 1，代表已下載過\n", "            cursor.execute(sql_update, (datetime.today().strftime(\"%Y-%m-%d %H-%M-%S\"), myDict[\"id\"],))\n", "                \n", "        conn.commit()\n", "    except sqlite3.<PERSON>rror as err: \n", "        # 回滾\n", "        conn.rollback()\n", "\n", "        # SQLite3 例外處理\n", "        exc_type, exc_value, exc_tb = sys.exc_info()\n", "        strErrorMsg = f'''SQLite error: {' '.join(err.args)}\\n\\n\n", "        SQLite traceback: {traceback.format_exception(exc_type, exc_value, exc_tb)}\n", "        '''\n", "        print(strErrorMsg)\n", "    finally:\n", "        # 關閉 sqlite\n", "        conn.close()"]}, {"cell_type": "code", "execution_count": null, "id": "2ac87279", "metadata": {}, "outputs": [], "source": ["init()"]}, {"cell_type": "code", "execution_count": null, "id": "113777d0", "metadata": {}, "outputs": [], "source": ["visit()"]}, {"cell_type": "code", "execution_count": null, "id": "683896de", "metadata": {}, "outputs": [], "source": ["scroll()"]}, {"cell_type": "code", "execution_count": null, "id": "4700e0b9", "metadata": {}, "outputs": [], "source": ["parse()"]}, {"cell_type": "code", "execution_count": null, "id": "a7821590", "metadata": {"scrolled": true}, "outputs": [], "source": ["time_begin = time.time()\n", "getPublishDate()\n", "time_end = time.time()\n", "print(f\"總共執行了 { time_end - time_begin } 秒\")\n", "# 總共執行了 533.356217622757 秒"]}, {"cell_type": "code", "execution_count": null, "id": "d624d485", "metadata": {}, "outputs": [], "source": ["close()"]}, {"cell_type": "code", "execution_count": null, "id": "4b245e2c", "metadata": {}, "outputs": [], "source": ["<PERSON><PERSON><PERSON>()"]}, {"cell_type": "code", "execution_count": null, "id": "35283ffb", "metadata": {}, "outputs": [], "source": ["saveDB()"]}, {"cell_type": "code", "execution_count": null, "id": "e5086396", "metadata": {}, "outputs": [], "source": ["time_begin = time.time()\n", "download()\n", "time_end = time.time()\n", "print(f\"總共執行了 { time_end - time_begin } 秒\")\n", "# 總共執行了 1032.4337289333344 秒"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}}, "nbformat": 4, "nbformat_minor": 5}