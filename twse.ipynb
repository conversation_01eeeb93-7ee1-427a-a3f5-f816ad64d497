{"cells": [{"cell_type": "markdown", "id": "bbef53ae", "metadata": {}, "source": ["# 下拉式選單\n", "- 例如 sel_element = Select(driver.find_element(By.CSS_SELECTOR, '選擇器字串'))\n", "  - 透過 option 的內文來選擇\n", "    - sel_element.select_by_visible_text('民國 100 年')\n", "  - 透過 option 的 value 屬性所設定值來選擇\n", "    - sel_element.select_by_value('2')\n", "  - 透過 option 的順序索引 (從 0 開始，類似陣列的索引概念) 來選擇\n", "    - sel_element.select_by_index(8)\n", "\n", "\n", "# 擷圖\n", "- driver.save_screenshot('/path/圖片存放路徑.png')"]}, {"cell_type": "code", "execution_count": null, "id": "47b612bd", "metadata": {}, "outputs": [], "source": ["'''\n", "臺灣證券交易所\n", "外資及陸資買賣超彙總表\n", "https://www.twse.com.tw/zh/page/trading/fund/TWT38U.html\n", "\n", "目標:\n", "整合下拉式選單與元素的定位與操控，來下載交易資料，並擷圖\n", "'''\n", "\n", "'''\n", "匯入套件\n", "'''\n", "# 操作 browser 的 驅動程式\n", "from selenium import webdriver\n", "\n", "# 期待元素出現要透過什麼方式指定，經常與 EC、WebDriverWait 一起使用\n", "from selenium.webdriver.common.by import By\n", "\n", "# 強制停止/強制等待 (程式執行期間休息一下)\n", "from time import sleep\n", "\n", "# 建立資料夾與執行檔案相關操作\n", "import os\n", "\n", "# 處理下拉式選單的工具\n", "from selenium.webdriver.support.ui import Select\n", "\n", "'''\n", "[1] Selenium with Python 中文翻譯文檔\n", "參考網頁：https://selenium-python-zh.readthedocs.io/en/latest/index.html\n", "[2] Selenium with Python\n", "https://selenium-python.readthedocs.io/\n", "[3] selenium 啓動 Chrome 的進階配置參數\n", "參考網址：https://stackoverflow.max-everyday.com/2019/12/selenium-chrome-options/\n", "[4] How to select a drop-down menu value with Selenium using Python?\n", "參考網址：https://stackoverflow.com/questions/7867537/how-to-select-a-drop-down-menu-value-with-selenium-using-python\n", "'''\n", "\n", "# 啟動瀏覽器工具的選項\n", "my_options = webdriver.ChromeOptions()\n", "# my_options.add_argument(\"--headless\")                # 不開啟實體瀏覽器背景執行\n", "my_options.add_argument(\"--start-maximized\")         # 最大化視窗\n", "my_options.add_argument(\"--incognito\")               # 開啟無痕模式\n", "my_options.add_argument(\"--disable-popup-blocking\")  # 禁用彈出攔截\n", "my_options.add_argument(\"--disable-notifications\")   # 取消通知\n", "\n", "# 建立下載路徑/資料夾，不存在就新增 (os.getcwd() 會取得當前的程式工作目錄)\n", "folderPath = os.path.join(os.getcwd(), 'files')\n", "if not os.path.exists(folderPath):\n", "    os.makedirs(folderPath)\n", "    \n", "# 自訂下載路徑 (不會詢問下載位置)\n", "my_options.add_experimental_option(\"prefs\", {\n", "    \"download.default_directory\": folderPath,\n", "    \"profile.default_content_settings.popups\": 0,\n", "    \"download.prompt_for_download\": <PERSON>als<PERSON>,\n", "    # \"download.directory_upgrade\": True,\n", "    # \"safebrowsing_for_trusted_sources_enabled\": False,\n", "    # \"safebrowsing.enabled\": <PERSON><PERSON><PERSON>,\n", "    # \"plugins.always_open_pdf_externally\": True\n", "})\n", "\n", "# 使用 Chrome 的 WebDriver\n", "driver = webdriver.Chrome(\n", "    options = my_options\n", ")\n", "\n", "# 走訪網址\n", "url = 'https://www.twse.com.tw/zh/page/trading/fund/TWT38U.html'"]}, {"cell_type": "code", "execution_count": 2, "id": "4c81f7bd", "metadata": {}, "outputs": [], "source": ["# 走訪頁面\n", "def visit():\n", "    driver.get(url)\n", "\n", "# 選取下拉式選單的項目\n", "def setDropDownMenu(year, value, index):\n", "    # 強制等待\n", "    sleep(1)\n", "\n", "    # 選擇 select[name=\"yy\"] 元素，並依 option 的 innerText 來進行選取\n", "    yy = Select(driver.find_element(By.CSS_SELECTOR, 'select[name=yy]'))\n", "    yy.select_by_visible_text(f'民國 {year} 年')\n", "\n", "    # 選擇 select[name=\"mm\"] 元素，並依 option 的 value 來進行選取\n", "    mm = Select(driver.find_element(By.CSS_SELECTOR, 'select[name=mm]'))\n", "    mm.select_by_value(str(value))\n", "\n", "    # 選擇 select[name=\"dd\"] 元素，並依 option 的 index 來進行選取\n", "    dd = Select(driver.find_element(By.CSS_SELECTOR, 'select[name=dd]'))\n", "    dd.select_by_index(index)\n", "\n", "    # 按下查詢\n", "    driver.find_element(\n", "        By.CSS_SELECTOR, \n", "        'div.submit'\n", "    ).click()\n", "    \n", "# 下載檔案\n", "def download(year, value, index):\n", "    # 下載 csv\n", "    year = 1911 + year\n", "    value = '0' + str(value) if value < 10 else str(value)\n", "    index = '0' + str(index + 1) if (index + 1) < 10 else str(index + 1)\n", "    date = f'{year}{value}{index}'\n", "    os.system(f'curl \"https://www.twse.com.tw/rwd/zh/fund/TWT38U?date={date}&response=csv\" -o {folderPath}/{date}.csv')\n", "    \n", "    # 擷圖\n", "    driver.save_screenshot(f\"{folderPath}/{date}.png\")\n", "\n", "# 關閉瀏覽器\n", "def close():\n", "    driver.quit()"]}, {"cell_type": "code", "execution_count": null, "id": "c1e0acec", "metadata": {}, "outputs": [], "source": ["# 走訪頁面\n", "visit()\n", "\n", "# 指定年、月、日，檢視查詢結果，並下載 csv\n", "year = 100\n", "value = 2\n", "index = 8\n", "setDropDownMenu(year, value, index)\n", "download(year, value, index)"]}, {"cell_type": "code", "execution_count": 37, "id": "3679d4a5", "metadata": {}, "outputs": [], "source": ["# 關閉瀏覽器\n", "close()"]}], "metadata": {"kernelspec": {"display_name": "test", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}