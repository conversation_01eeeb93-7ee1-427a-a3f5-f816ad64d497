{"cells": [{"cell_type": "code", "execution_count": null, "id": "6f9c10d9", "metadata": {}, "outputs": [], "source": ["!pip install pymysql"]}, {"cell_type": "code", "execution_count": null, "id": "ddca9715", "metadata": {}, "outputs": [], "source": ["'''\n", "在資料庫的交易中，為確保交易(Transaction)是正確可靠的，所以必須具備四個特性：\n", "Atomicity   原子性 - 在資料庫的每一筆交易中只有兩種可能發生，第一種是全部完全(commit)，第二種是全部不完成(rollback)，\n", "                    不會因為某個環節出錯，而終止在那個環節，在出錯之後會恢復至交易之前的狀態，如同還沒執行此筆交易。\n", "Consistency 一致性 - 在交易中會產生資料或者驗證狀態，然而當錯誤發生，所有已更改的資料或狀態將會恢復至交易之前。\n", "Isolation   隔離性 - 資料庫允許多筆交易同時進行，交易進行時未完成的交易資料並不會被其他交易使用，直到此筆交易完成。\n", "Durability  永續性 - 交易完成後對資料的修改是永久性的，資料不會因為系統重啟或錯誤而改變。\n", "'''"]}, {"cell_type": "markdown", "id": "e18723b0", "metadata": {}, "source": ["![交易過程](https://i.imgur.com/r29XFgO.png \"交易過程\")"]}, {"cell_type": "code", "execution_count": null, "id": "82561ad3", "metadata": {}, "outputs": [], "source": ["'''\n", "參考頁面:\n", "[1] PyMySQL Examples\n", "https://pymysql.readthedocs.io/en/latest/user/examples.html\n", "[2] Python+MySQL資料庫操作（PyMySQL）\n", "https://www.tw511.com/3/39/1388.html\n", "[3] Python資料庫學習筆記(四)：使用PyMySQL模組\n", "https://reurl.cc/Q78eD2\n", "'''\n", "\n", "import pymysql\n", "\n", "# 資料庫連線\n", "connection = pymysql.connect(\n", "    host = 'localhost',\n", "    user = 'root',\n", "    password = '',\n", "    database = 'my_db',\n", "    charset = 'utf8mb4',\n", "    cursorclass=pymysql.cursors.DictCursor\n", ")\n", "\n", "# 取得 cursor 物件，進行 CRUD\n", "cursor = connection.cursor()\n", "\n", "try:\n", "    # 寫入資料\n", "    # sql = \"INSERT INTO `users` (`email`, `password`) VALUES (%s, %s)\"\n", "    # cursor.execute(sql, ('<EMAIL>', 'very-secret'))\n", "\n", "    # 查詢資料\n", "    sql = \"SELECT * FROM `users`\"\n", "    cursor.execute(sql)\n", "\n", "    # 查詢結果列數大於0 ，代表有資料\n", "    if cursor.rowcount > 0:\n", "        # 將查詢結果轉成 list 型態 (裡頭元素都是 dict)\n", "        results = cursor.fetchall() # 如果 sql 語法明顯只取得一筆，則使用 fetchone()\n", "        # 迭代取得資料 (dict 型態)\n", "        for result in results:\n", "            print(result)\n", "    else:\n", "        print(\"rowcount: 0\")\n", "\n", "    # 提交 SQL 執行結果\n", "    connection.commit()\n", "except Exception as e:\n", "    # 回滾\n", "    connection.rollback()\n", "    print(\"SQL 執行失敗\")\n", "    print(e)\n", "\n", "# 釋放 cursor\n", "cursor.close()\n", "\n", "# 關閉資料庫連線\n", "connection.close()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}}, "nbformat": 4, "nbformat_minor": 5}