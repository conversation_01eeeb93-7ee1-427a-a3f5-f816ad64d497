{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "# 先建立 dict 格式的 account.json 檔，並取得其中的帳密資料\n", "with open(\"./account.json\", \"r\", encoding=\"utf-8\") as file:\n", "    strJson = file.read()\n", "    dictJson = json.loads(strJson)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["'''\n", "匯入套件\n", "'''\n", "# 操作 browser 的 API\n", "from selenium import webdriver\n", "from selenium.webdriver.chrome.service import Service\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "# 處理逾時例外的工具\n", "from selenium.common.exceptions import TimeoutException\n", "\n", "# 面對動態網頁，等待某個元素出現的工具，通常與 exptected_conditions 搭配\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "\n", "# 搭配 WebDriverWait 使用，對元素狀態的一種期待條件，若條件發生，則等待結束，往下一行執行\n", "from selenium.webdriver.support import expected_conditions as EC\n", "\n", "# 期待元素出現要透過什麼方式指定，通常與 EC、WebDriverWait 一起使用\n", "from selenium.webdriver.common.by import By\n", "\n", "# 強制等待 (執行期間休息一下)\n", "from time import sleep\n", "\n", "# 整理 json 使用的工具\n", "import json\n", "\n", "# 執行 command 的時候用的\n", "import os\n", "\n", "# 引入 regular expression 工具\n", "import re\n", "\n", "# 子處理程序，用來取代 os.system 的功能\n", "import subprocess\n", "\n", "\n", "'''\n", "Selenium with Python 中文翻譯文檔\n", "參考網頁：https://selenium-python-zh.readthedocs.io/en/latest/index.html\n", "selenium 啓動 Chrome 的進階配置參數\n", "參考網址：https://stackoverflow.max-everyday.com/2019/12/selenium-chrome-options/\n", "Mouse Hover Action in Selenium\n", "參考網址：https://www.toolsqa.com/selenium-webdriver/mouse-hover-action/\n", "yt-dlp 下載影音的好工具\n", "參考網址：https://github.com/yt-dlp/yt-dlp\n", "'''\n", "# 啟動瀏覽器工具的選項\n", "my_options = webdriver.ChromeOptions()\n", "# my_options.add_argument(\"--headless\")                #不開啟實體瀏覽器背景執行\n", "my_options.add_argument(\"--start-maximized\")         #最大化視窗\n", "my_options.add_argument(\"--incognito\")               #開啟無痕模式\n", "my_options.add_argument(\"--disable-popup-blocking\") #禁用彈出攔截\n", "my_options.add_argument(\"--disable-notifications\")  #取消通知\n", "\n", "\n", "# 使用 Chrome 的 WebDriver\n", "driver = webdriver.Chrome(\n", "    options = my_options,\n", "    service = Service(ChromeDriverManager().install())\n", ")\n", "\n", "\n", "'''\n", "自訂變數\n", "'''\n", "# 想爬取資料的來源網址\n", "url = 'https://www.instagram.com/'\n", "ig_account = 'english.ig_'\n", "\n", "# 放置爬取的資料\n", "listData = []\n", "\n", "# 放置 ig 每個格子裡面的超連結\n", "listLink = []\n", "\n", "# 設定 set 物件，放置影片、圖片連結，協助過濾重複的元素\n", "setTmp = set()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [], "source": ["'''\n", "以 function 名稱，作為爬蟲流程\n", "'''\n", "def login():\n", "    try:\n", "        # 前往首頁\n", "        driver.get(url);\n", "\n", "        # 等待互動元素出現 (這裡用帳號文字欄位)\n", "        WebDriverWait(driver, 10).until(\n", "            EC.presence_of_element_located( \n", "                (By.CSS_SELECTOR, 'input[name=\"username\"]') \n", "            )\n", "        )\n", "\n", "        # 輸入帳號\n", "        driver.find_element(\n", "            By.CSS_SELECTOR, \n", "            'input[name=\"username\"]'\n", "        ).send_keys(dict<PERSON>son['username'])\n", "\n", "        # 輸入密碼\n", "        driver.find_element(\n", "            By.CSS_SELECTOR, \n", "            'input[name=\"password\"]'\n", "        ).send_keys(dict<PERSON>son['password'])\n", "\n", "        # 強制等待\n", "        sleep(3)\n", "\n", "        # 按下登入\n", "        driver.find_element(\n", "            By.CSS_SELECTOR, \n", "            'button[type=\"submit\"].sqdOP.L3NKy.y3zKF'\n", "        ).click()\n", "        \n", "        # 強制等待\n", "        sleep(4)\n", "    except TimeoutException:\n", "        print(\"等待逾時，即將關閉瀏覽器…\")\n", "        driver.quit()\n", "\n", "# 走訪頁面\n", "def visit():\n", "    # 前往指定連結\n", "    driver.get(url + ig_account);\n", "\n", "# 取得每個項目的 url\n", "def getUrl():\n", "    # 逐一檢視元素\n", "    for index, a in enumerate(driver.find_elements(By.CSS_SELECTOR, 'div.Nnq7C.weEfm div.v1Nh3.kIKUG._bz0w a')):\n", "        # 測試功能，所以只選三個連結\n", "        #if index == 3:\n", "        #    break\n", "            \n", "        print(f\"取得網址: {a.get_attribute('href')}\")\n", "        \n", "        # 放資料到 list 中\n", "        listLink.append(a.get_attribute('href'))\n", "\n", "# 逐個網頁連結內容進行分析\n", "def parse():\n", "    for aLink in listLink:\n", "        # 清空暫存圖片、影片連結的 set\n", "        setTmp.clear()\n", "        \n", "        # 走訪頁面\n", "        driver.get(aLink)\n", "        \n", "        '''\n", "        取得頁面的 id：\n", "            full_match = re.search(regex, aLink)[0]\n", "            group_1 = re.search(regex, aLink)[1]\n", "        \n", "        範例：\n", "            matchObj[0]  : /p/CGpaFHrHV2j/\n", "            matchObj[1]  : CGpaFHrHV2j \n", "        '''\n", "        # 取得 ig 連結的 id\n", "        pageId = re.search(r'\\/p\\/([a-zA-Z0-9-_]+)\\/', aLink)[1]\n", "        \n", "        print(f\"網頁 ID: {pageId}\")\n", "        \n", "        # 強制等待\n", "        sleep(2)\n", "        \n", "        '''\n", "        判斷是否有「向右」按鈕：\n", "            若有，則代表會有多個 li；\n", "            若無，則代表只有一個 li\n", "\n", "        備註：\n", "            由於 find_elements 回傳 list 格式，\n", "            所以可以用 len() 來取得元素長度，\n", "            判斷元素是否存在\n", "        '''\n", "        # 當有右箭頭可以按的時候，代表頁面中同時有多張圖片或影片，取得多元素資訊\n", "        while len(driver.find_elements(By.CSS_SELECTOR, \"button._6CZji\")) > 0:\n", "            # 按下向右按鈕\n", "            driver.find_element(By.CSS_SELECTOR, \"button._6CZji\").click()\n", "\n", "            # 檢視當前現有的 li (解決滑動時，前面一部分或後面一部分 li.Ckrof 會消失的問題)\n", "            for index, li in enumerate(driver.find_elements(By.CSS_SELECTOR, \"article li.Ckrof\")):\n", "                # 判斷 li 底下是否有 img\n", "                if len(li.find_elements(By.CSS_SELECTOR, \"img.FFVAD\")) > 0:\n", "                    # 將 img 裡面的屬性值(例如 src)，視為 value 加入 set 當中\n", "                    setTmp.add(li.find_element(By.CSS_SELECTOR, \"img.FFVAD\").get_attribute('src'))\n", "                else:\n", "                    # 若非 img，便是 video，將頁面連結加入 set 當中\n", "                    setTmp.add(aLink)\n", "\n", "            # 強制等待\n", "            sleep(1)\n", "        else:\n", "            # 當沒有右箭頭可以按的時候，代表是獨立的單張圖片或影片\n", "            if len( driver.find_elements(By.CSS_SELECTOR, \"article img.FFVAD\") ) > 0:\n", "                # 將 img 的 src 值加入 set 當中\n", "                setTmp.add(driver.find_element(By.CSS_SELECTOR, \"article img.FFVAD\").get_attribute('src'))\n", "            else:\n", "                # 若非 img，便是 video，將頁面連結加入 set 當中\n", "                setTmp.add(aLink)\n", "                \n", "\n", "        # 整合此次網頁連結的元素資料\n", "        listData.append({\n", "            \"id\": pageId,\n", "            \"url\": aLink,\n", "            \"listDL\": list(setTmp)\n", "        })\n", "        \n", "# 將 list 存成 json\n", "def save<PERSON><PERSON>():\n", "    with open(\"./ig.json\", \"w\", encoding='utf-8') as fp:\n", "        fp.write( json.dumps(listData, ensure_ascii=False, indent=4) )\n", "    \n", "# 關閉瀏覽器\n", "def close():\n", "    driver.quit()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 流程開始前，先行登入，減少被 IG 停止帳號的風險 (雖然修改密碼還是救得回來…)\n", "login()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["'''主程式'''\n", "if __name__ == '__main__':\n", "    visit()\n", "    getUrl()\n", "    parse()\n", "    <PERSON><PERSON><PERSON>()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 關閉瀏覽器\n", "close()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 下載檔案\n", "def download():\n", "    # 開啟 json 檔案\n", "    with open(\"./ig.json\", \"r\", encoding='utf-8') as fp:\n", "        #取得 json 字串\n", "        strJson = fp.read()\n", "    \n", "    # 建立儲存圖片、影片的資料夾\n", "    folderPath = 'ig'\n", "    if not os.path.exists(folderPath):\n", "        os.makedirs(folderPath)\n", "    \n", "    # 將 json 轉成 list (裡面是 dict 集合)\n", "    listResult = json.loads(strJson)\n", "    \n", "    # 將 listData 底下的 dict 找出來\n", "    for d in listResult:\n", "        # dictObj 底下有個 listDL 屬性，它對應的值是個 list，將 list裡面所有 url 找出來\n", "        for index, url in enumerate(d['listDL']):\n", "            print(\"=\" * 50)\n", "            print(f\"正在下載連結: {url}\")\n", "            \n", "            match = re.search(r\"https:\\/\\/\\S+\\/(\\S+.jpe?g)\", url)\n", "            if match != None:\n", "                os.system(f'curl \"{url}\" -o ./{folderPath}/{d[\"id\"]}_{match[1]}')\n", "                print(\"下載完成\")\n", "            else:\n", "                # 強制等待\n", "                sleep(2)\n", "                \n", "                # 定義指令\n", "                cmd = [\n", "                    './yt-dlp.exe', \n", "                    url, \n", "                    '-f', 'b[ext=mp4]', \n", "                    '-o', f'./{folderPath}/%(id)s_{index}.%(ext)s', \n", "                    '-u', dict<PERSON><PERSON>['username'],\n", "                    '-p', dict<PERSON><PERSON>['password']\n", "                ]\n", "                \n", "                # 執行指令，並取得回傳結果\n", "                result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT)\n", "\n", "                # 將回傳結果進行解碼，顯示實際執行過程的文字輸出\n", "                output = result.stdout.decode('utf-8')\n", "                print(\"下載完成，訊息如下:\")\n", "                print(output)\n", "\n", "'''另行下載'''\n", "download()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.12"}}, "nbformat": 4, "nbformat_minor": 4}