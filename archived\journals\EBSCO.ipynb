{"cells": [{"cell_type": "markdown", "id": "f214c2be", "metadata": {}, "source": ["# 套件安裝"]}, {"cell_type": "code", "execution_count": null, "id": "b079da8e", "metadata": {"scrolled": true}, "outputs": [], "source": ["# 安裝所需套件\n", "!pip install -U openpyxl selenium beautifulsoup4 lxml requests"]}, {"cell_type": "markdown", "id": "276ca8e6", "metadata": {}, "source": ["# 設定初始化"]}, {"cell_type": "code", "execution_count": null, "id": "011f7b25", "metadata": {"scrolled": true}, "outputs": [], "source": ["'''\n", "注意事項:\n", "1. 程式執行前，需先連上臺師大 VPN。\n", "2. 下載對應的 ChromeDriver (web driver) 到程式檔案同一個目錄下後解壓縮，下載前記得對應版本編號。\n", "連結: https://chromedriver.chromium.org/downloads\n", "\n", "參考網頁:\n", "[1] 臺師大 圖書館 電子資料庫總覽\n", "https://www.lib.ntnu.edu.tw/database/database_english.jsp?flag=0&choice_id=E\n", "[2] EBSCO Select Resource\n", "https://search.ebscohost.com/Community.aspx?community=y&authtype=ip\n", "[3] python操作Excel檔案之openpyxl\n", "https://www.itread01.com/content/1544850004.html\n", "'''\n", "\n", "\n", "'''\n", "匯入套件\n", "'''\n", "# 操作 browser 的 API\n", "from selenium import webdriver\n", "\n", "# 處理逾時例外的工具\n", "from selenium.common.exceptions import TimeoutException\n", "\n", "# 面對動態網頁，等待某個元素出現的工具，通常與 exptected_conditions 搭配\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "\n", "# 搭配 WebDriverWait 使用，對元素狀態的一種期待條件，若條件發生，則等待結束，往下一行執行\n", "from selenium.webdriver.support import expected_conditions as EC\n", "\n", "# 期待元素出現要透過什麼方式指定，通常與 EC、WebDriverWait 一起使用\n", "from selenium.webdriver.common.by import By\n", "\n", "# 處理下拉式選單的工具\n", "from selenium.webdriver.support.ui import Select\n", "\n", "# 強制等待 (執行期間休息一下)\n", "from time import sleep\n", "\n", "# pretty-print\n", "from pprint import pprint\n", "\n", "# 隨機\n", "from random import randint\n", "\n", "# 計時\n", "import time\n", "\n", "# 整理 json 使用的工具\n", "import json\n", "\n", "# 執行 shell command 的時候用的\n", "import os\n", "\n", "# 子處理程序，用來取代 os.system 的功能\n", "import subprocess\n", "\n", "# 正規表達式\n", "import re\n", "\n", "# 存取 Excel 的工具\n", "from openpyxl import load_workbook\n", "from openpyxl import Workbook\n", "\n", "# 啟動瀏覽器工具的選項\n", "my_options = webdriver.ChromeOptions()\n", "# my_options.add_argument(\"--headless\")             #不開啟實體瀏覽器背景執行\n", "my_options.add_argument(\"--start-maximized\")        #最大化視窗\n", "my_options.add_argument(\"--incognito\")              #開啟無痕模式\n", "my_options.add_argument(\"--disable-popup-blocking\") #禁用彈出攔截\n", "my_options.add_argument(\"--disable-notifications\")  #取消通知\n", "my_options.add_argument(\"--lang=zh-TW\")  #設定為正體中文\n", "\n", "# 指定 chromedriver 檔案的路徑\n", "driver_exec_path = './chromedriver.exe'\n", "\n", "# 給 web driver 用的變數\n", "driver = None\n", "\n", "# 來源首頁\n", "url = 'https://search.ebscohost.com/Community.aspx?community=y&authtype=ip'\n", "\n", "# 檢索文字 (暫時以 list 型態定義檢索文字，未來有機會可以擴充使用)\n", "listKeywords = [\n", "    [\n", "        'dissertation or thesis or \"journal article\" or \"research paper\"', \n", "        'plagiarism or cheating or \"academic integrity\"'\n", "    ]\n", "] \n", "fromYear = 1986\n", "toYear = 2022\n", "\n", "# 指定 sheet name\n", "sheetName = '875'\n", "\n", "# 指定 excel 檔名\n", "excelFileName = 'EBSCO.xlsx'\n", "\n", "# 指定 json 檔名\n", "jsonFileName = f'{sheetName}.json'\n", "\n", "# 建立儲存圖片、影片的資料夾\n", "folderPath = f'./EBSCO_{sheetName}'\n", "if not os.path.exists(folderPath):\n", "    os.makedirs(folderPath)\n", "\n", "# 判斷 excel 檔案是否存在，不存在就新增\n", "filePath = folderPath + '/' + excelFileName\n", "if not os.path.exists(filePath):\n", "    workbook = Workbook() # 動態新增檔案\n", "    worksheet = workbook.create_sheet(sheetName, 0) # 建立並取得 active sheet\n", "else:\n", "    workbook = load_workbook(filename = filePath)\n", "    worksheet = workbook[sheetName] # 取得 active sheet\n", "\n", "# 日期、來源、篇名、摘要、內文等標題\n", "worksheet['A1'] = \"檢索流水號\"\n", "worksheet['B1'] = \"日期\"\n", "worksheet['C1'] = \"來源\"\n", "worksheet['D1'] = \"篇名\"\n", "worksheet['E1'] = \"全文類型\"\n", "worksheet['F1'] = \"固定連結\"\n", "worksheet['G1'] = \"摘要\"\n", "worksheet['H1'] = \"內文\"\n", "\n", "# 放置爬取的資料\n", "listData = []"]}, {"cell_type": "markdown", "id": "ad2c9fb3", "metadata": {}, "source": ["# 自訂函式 (網路爬蟲執行流程)"]}, {"cell_type": "code", "execution_count": null, "id": "aeac6c7f", "metadata": {"scrolled": true}, "outputs": [], "source": ["'''\n", "函式\n", "'''\n", "# 初始化 Web Driver\n", "def init():\n", "    global driver\n", "    # 使用 Chrome 的 WebDriver\n", "    driver = webdriver.Chrome( \n", "        options = my_options, \n", "        executable_path = driver_exec_path\n", "    )\n", "\n", "# 走訪來源網頁\n", "def visit():\n", "    global driver\n", "    driver.get(url) #進入來源網頁\n", "    try:\n", "        # 等待目標元素出現\n", "        WebDriverWait(driver, 10).until(\n", "            EC.presence_of_element_located(\n", "                (By.LINK_TEXT, 'EBSCOhost Web')\n", "            )\n", "        )\n", "        \n", "        # 按下超連結\n", "        driver.find_element(By.LINK_TEXT, 'EBSCOhost Web').click()\n", "    except TimeoutException as e:\n", "        print('等待逾時: visit')\n", "\n", "# 檢索\n", "def search():\n", "    global driver\n", "    try:\n", "        # 強制等待\n", "        sleep( randint(1,2) )\n", "        \n", "        # 等待 選擇資料庫 文字出現\n", "        WebDriverWait(driver, 10).until(\n", "            EC.presence_of_element_located(\n", "                (By.CSS_SELECTOR, 'a#selectDBLink')\n", "            )\n", "        )\n", "        \n", "        # 按下 選擇資料庫\n", "        driver.find_element(By.CSS_SELECTOR, 'a#selectDBLink').click()\n", "        \n", "        # 等待 選取/取消選取全部 checkbox 元素出現\n", "        WebDriverWait(driver, 10).until(\n", "            EC.presence_of_element_located(\n", "                (By.CSS_SELECTOR, 'input#selectAll')\n", "            )\n", "        )\n", "        \n", "        # 取得 選取/取消選取全部 checkbox 元素\n", "        checkboxAllElement = driver.find_element(By.CSS_SELECTOR, 'input#selectAll')\n", "        \n", "        # 判斷 選取/取消選取全部 checkbox 元素是否勾選，勾選就取消\n", "        if checkboxAllElement.is_selected():\n", "            checkboxAllElement.click()\n", "            \n", "        # 強制等待\n", "        sleep( randint(1,2) )\n", "        \n", "        # 取得指定的 checkbox 元素 (本例為 Newspaper Source)\n", "        checkboxSpecificElement = driver.find_element(By.CSS_SELECTOR, 'input#ctrlSelectDb_dbList_ctl21_itemCheck')\n", "        \n", "        # 判斷 指定的 checkbox 元素是否勾選，沒勾選就直接勾選\n", "        if not checkboxAllElement.is_selected():\n", "            checkboxSpecificElement.click()\n", "            \n", "        # 強制等待\n", "        sleep( randint(1,2) )\n", "            \n", "        # 按下 OK\n", "        driver.find_element(By.CSS_SELECTOR, 'input#btnOK').click()\n", "        \n", "        # 等待目標元素出現\n", "        WebDriverWait(driver, 10).until(\n", "            EC.presence_of_element_located(\n", "                (By.CSS_SELECTOR, 'input#Searchbox1')\n", "            )\n", "        )\n", "        \n", "        # 點選(按鈕)選單元素 (本例為 TX)\n", "        buttonDropDownElements = driver.find_elements(By.CSS_SELECTOR, 'button.dd-active')\n", "        \n", "        # 取得所有選單元素底下的列表\n", "        ulDropDownElements = driver.find_elements(By.CSS_SELECTOR, 'ul.dd-list')\n", "        \n", "        # 輸入檢索文字，並選擇欄位\n", "        inputSearchbox1 = driver.find_element(By.CSS_SELECTOR, 'input#Searchbox1')\n", "        inputSearchbox1.send_keys(listKeywords[0][0])\n", "        buttonDropDownElements[0].click()\n", "        ulDropDownElements[0].find_elements(By.CSS_SELECTOR, 'li.dd-list-item')[1].click()\n", "        \n", "        # 輸入檢索文字，並選擇欄位\n", "        inputSearchbox2 = driver.find_element(By.CSS_SELECTOR, 'input#Searchbox2')\n", "        inputSearchbox2.send_keys(listKeywords[0][1])\n", "        buttonDropDownElements[2].click()\n", "        ulDropDownElements[2].find_elements(By.CSS_SELECTOR, 'li.dd-list-item')[1].click()\n", "        \n", "        # 取得 全文 checkbox 元素\n", "        checkboxFullText = driver.find_element(By.CSS_SELECTOR, 'input#common_FT')\n", "        \n", "        # 判斷 全文 checkbox 元素是否勾選，沒勾選就直接勾選\n", "        if not checkboxFullText.is_selected():\n", "            checkboxFullText.click()\n", "            \n", "        # 輸入開始年份\n", "        driver.find_element(By.CSS_SELECTOR, 'input#common_DT1_FromYear').send_keys(fromYear)\n", "        \n", "        # 輸入結束年份\n", "        driver.find_element(By.CSS_SELECTOR, 'input#common_DT1_ToYear').send_keys(toYear)\n", "        \n", "        # 按下檢索\n", "        driver.find_element(By.CSS_SELECTOR, 'input#ctl00_ctl00_MainContentArea_MainContentArea_ctrlLimiters_btnSearch').click()\n", "        \n", "    except TimeoutException as e:\n", "        print('等待逾時: search')\n", "        \n", "# 取得主要連結與資訊\n", "def getData():\n", "    try:\n", "        # 等待目標元素出現\n", "        WebDriverWait(driver, 10).until(\n", "            EC.presence_of_element_located(\n", "                (By.CSS_SELECTOR, \"a.arrow-link.legacy-link.next\")\n", "            )\n", "        )\n", "        \n", "        # 判斷是否有下一頁，若有，則先蒐集資料，再按下一頁的連結\n", "        while True:\n", "            # 取得檢索結果(列表)\n", "            li_elms = driver.find_elements(By.CSS_SELECTOR, \"div#resultListControl.content-wrapper ul.result-list.has-icons > li.result-list-li\")\n", "            \n", "            # 逐筆資料剖析\n", "            for index, li in enumerate(li_elms):\n", "                # 檢索流水號\n", "                result_index = li.find_element(By.CSS_SELECTOR, 'span.record-index').get_attribute('innerText')\n", "                result_index = result_index.replace(\".\", \"\")\n", "                \n", "                # 篇名 與 超連結\n", "                a_elm = li.find_element(By.CSS_SELECTOR, 'div.result-list-record h3.title-link-wrapper a')\n", "                str_title = a_elm.get_attribute('innerText')\n", "                str_title_link = a_elm.get_attribute('href')\n", "                \n", "                # 來源 與 日期\n", "                str_info = li.find_element(By.CSS_SELECTOR, 'div.result-list-record div.display-info').get_attribute('innerText')\n", "                list_split_info = str_info.split(\"\\n\")\n", "                str_info_source = list_split_info[2]\n", "                regex_date = r'\\d{1,2}\\s*?\\/\\s*?\\d{1,2}\\s*?\\/\\s*?\\d{2,4}'\n", "                match_date = re.search(regex_date, str_info)\n", "                str_date = match_date[0]\n", "                \n", "                # 全文類型\n", "                a_elm = li.find_element(By.CSS_SELECTOR, 'div.result-list-record div.display-info span.record-formats a')\n", "                str_fulltext_type = a_elm.get_attribute('innerText')\n", "                regexType = r'[A-Z]+'\n", "                matchType = re.search(regexType, str_fulltext_type)\n", "                str_fulltext_type = matchType[0]\n", "                \n", "                # 開啟新分頁\n", "                driver.execute_script(f'window.open(\"{str_title_link}\", \"_blank\");')\n", "\n", "                # 切換到分頁\n", "                driver.switch_to.window(driver.window_handles[1])\n", "                \n", "                # 等描述資料相關元素出現\n", "                WebDriverWait(driver, 10).until(\n", "                    EC.presence_of_element_located(\n", "                        (By.CSS_SELECTOR, \"dl#citationFields\")\n", "                    )\n", "                )\n", "                \n", "                # 取得描述資料區塊的元素\n", "                dt_elms = driver.find_elements(By.CSS_SELECTOR, 'dl#citationFields dt[data-auto=\"citation_field_label\"]')\n", "                dd_elms = driver.find_elements(By.CSS_SELECTOR, 'dl#citationFields dd[data-auto=\"citation_field_value\"]')\n", "                \n", "                # 摘要、內文、全文連結、pdf 下載連結 等預設值\n", "                str_abstract = str_inner_text = str_permalink = str_pdf_link = \"\"\n", "                \n", "                if str_fulltext_type == 'HTML': # HTML 取得方式\n", "                    # 摘要\n", "                    for index, dt in enumerate(dt_elms):\n", "                        if \"摘要\" in dt.get_attribute('innerText'):\n", "                            str_abstract = dd_elms[index].get_attribute('innerText')\n", "                            break\n", "\n", "                    # 內文\n", "                    children_in_section_elms = driver.find_elements(By.CSS_SELECTOR, 'section#TextToSpeech > *')\n", "                    for index, elm in enumerate(children_in_section_elms):\n", "                        if elm.get_attribute('data-auto') == 'copyright_info': continue\n", "                        if elm.get_attribute('id') == 'textToSpeechPlaceholder': continue\n", "                        if elm.get_attribute('data-auto') == 'fulltext_title_hidden': continue\n", "                        str_inner_text += elm.get_attribute('innerText') + \" \\n\"\n", "                    \n", "                elif str_fulltext_type == 'PDF': # PDF 取得方式\n", "                    # 判斷頁面是否有 pdf 全文連結，如果找不到，則刷新頁面試試\n", "                    for i in range(10):\n", "                        if len( driver.find_elements(By.CSS_SELECTOR, 'a.record-type.pdf-ft') ) > 0 and driver.find_element(By.CSS_SELECTOR, 'a.record-type.pdf-ft').is_displayed():\n", "                            break\n", "                        else:\n", "                            driver.refresh()\n", "                            sleep( randint(1,2) )\n", "                    \n", "                    # 進入 pdf viewer 頁面\n", "                    driver.find_element(By.CSS_SELECTOR, 'a.record-type.pdf-ft').click()\n", "                    \n", "                    # 等待 pdf viewer 元素出現\n", "                    WebDriverWait(driver, 10).until(\n", "                        EC.presence_of_element_located(\n", "                            (By.CSS_SELECTOR, 'input#pdfUrl')\n", "                        )\n", "                    )\n", "                    \n", "                    # 取得 pdf 連結\n", "                    str_pdf_link = driver.find_element(By.CSS_SELECTOR, 'input#pdfUrl').get_attribute('value')\n", "                    \n", "                    # 下載 pdf\n", "                    cmd = ['curl', str_pdf_link, '-o', f'{folderPath}/{sheetName}-{result_index}.pdf']\n", "                    result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT)\n", "                    output = result.stdout\n", "                    print(\"=\" * 20)\n", "                    print(f\"{sheetName}-{result_index}.pdf 下載完成\")\n", "                    print(\"=\" * 20)\n", "                \n", "                # 等待\n", "                sleep(1)\n", "                \n", "                # 如果找不到 固定連結，則刷新頁面試試\n", "                for i in range(10):\n", "                    if len( driver.find_elements(By.CSS_SELECTOR, 'ul.article-tools.delivery-control > li.article-tool > a.permalink-link') ) > 0 and driver.find_element(By.CSS_SELECTOR, 'ul.article-tools.delivery-control > li.article-tool > a.permalink-link').is_displayed():\n", "                        break\n", "                    else:\n", "                        driver.refresh()\n", "                        sleep( randint(1,2) )\n", "\n", "                # 如果還是找不到 固定連結，則以當前連結為 固定連結\n", "                if driver.find_element(By.CSS_SELECTOR, 'ul.article-tools.delivery-control > li.article-tool > a.permalink-link').is_displayed():\n", "                    # 按下 固定連結 圖示\n", "                    driver.find_element(By.CSS_SELECTOR, 'ul.article-tools.delivery-control > li.article-tool > a.permalink-link').click()\n", "\n", "                    # 等待 固定連結 元素出現\n", "                    WebDriverWait(driver, 10).until(\n", "                        EC.presence_of_element_located(\n", "                            (By.CSS_SELECTOR, 'input#pLinkDetail')\n", "                        )\n", "                    )\n", "                    \n", "                    # 強制等待\n", "                    sleep( randint(1,2) )\n", "\n", "                    # 取得 固定連結\n", "                    str_permalink = driver.find_element(By.CSS_SELECTOR, 'input#pLinkDetail').get_attribute('value')        \n", "                else:\n", "                    # 依然找不到，則取得當前連結，作為 固定連結\n", "                    str_permalink = driver.current_url\n", "                        \n", "                # 關閉當前分頁\n", "                driver.close()\n", "                \n", "                # 切換到初始分頁\n", "                driver.switch_to.window(driver.window_handles[0])\n", "                \n", "                # 整理資料\n", "                listData.append({\n", "                    \"檢索流水號\": result_index,\n", "                    \"日期\": str_date,\n", "                    \"來源\": str_info_source,\n", "                    \"篇名\": str_title,\n", "                    \"全文類型\": str_fulltext_type,\n", "                    \"固定連結\": str_permalink,\n", "                    \"摘要\": str_abstract,\n", "                    \"內文\": str_inner_text\n", "                })\n", "                \n", "                print(f\"已走訪編號: {result_index}\")\n", "                \n", "            # 按 下一頁(個)，如果沒有下一頁，則跳出迴圈\n", "            if len( driver.find_elements(By.CSS_SELECTOR, \"a.arrow-link.legacy-link.next\") ) > 0:\n", "                driver.find_element(By.CSS_SELECTOR, \"a.arrow-link.legacy-link.next\").click()\n", "            else:\n", "                break\n", "            \n", "    except TimeoutException as e:\n", "        print(\"等待逾時: getMainData\")\n", "\n", "# 關閉瀏覽器\n", "def close():\n", "    driver.quit()\n", "        \n", "# 儲存成 json\n", "def save<PERSON><PERSON>():\n", "    global listData\n", "    with open(f\"{folderPath}/{jsonFileName}\", \"w\", encoding=\"utf-8\") as file:\n", "        file.write( json.dumps( listData, ensure_ascii=False, indent=4 ) )\n", "        \n", "# 儲存成 excel\n", "def saveExcel():\n", "    with open(f\"{folderPath}/{jsonFileName}\", \"r\", encoding=\"utf-8\") as file:\n", "        # 從 excel 列號 2 開始寫入資料\n", "        row_num = 2\n", "        \n", "        # 取得 json 內容\n", "        strJson = file.read()\n", "        \n", "        # 將 json 轉成 list\n", "        listJson = json.loads(strJson)\n", "        \n", "        # 逐列寫入\n", "        for myDict in list<PERSON><PERSON>:\n", "            worksheet['A' + str(row_num)] = myDict['檢索流水號']\n", "            worksheet['B' + str(row_num)] = myDict['日期']\n", "            worksheet['C' + str(row_num)] = myDict['來源']\n", "            worksheet['D' + str(row_num)] = myDict['篇名']\n", "            worksheet['E' + str(row_num)] = myDict['全文類型']\n", "            worksheet['F' + str(row_num)] = myDict['固定連結']\n", "            worksheet['G' + str(row_num)] = myDict['摘要']\n", "            worksheet['H' + str(row_num)] = myDict['內文']\n", "            row_num += 1\n", "    \n", "    # 儲存 workbook\n", "    workbook.save(filePath)\n", "\n", "    # 關閉 workbook\n", "    workbook.close()"]}, {"cell_type": "markdown", "id": "7b791bf3", "metadata": {}, "source": ["# 注意\n", "- 以下的函數必須各別執行。\n", "- 若是中間需要透過瀏覽器讀取網頁後，一定要等瀏覽器讀取完，才能往下一個流程執行。\n", "- 只要瀏覽器沒關閉(close)，中間的檢索(search)與取得主要資訊(getData)都可以修改程式後，重新依序執行。\n", "- 只要建立完 json 檔 (saveJson)，便可建立 excel 檔 (saveExcel)，與網路爬蟲流程是分開的。\n", "- 這個網站麻煩的地方在於，**它提供的超連結，只限此時開啟瀏覽器期間可用，若是關閉瀏覽器重開，網站的 token 一換，舊的超連結就無法使用了**。"]}, {"cell_type": "code", "execution_count": null, "id": "b74f5407", "metadata": {"scrolled": true}, "outputs": [], "source": ["# 開啟 Web Driver\n", "init()"]}, {"cell_type": "code", "execution_count": null, "id": "07cde3bc", "metadata": {"scrolled": true}, "outputs": [], "source": ["# 走訪來源網頁 (有時候瀏覽器會執行比較久，要等它讀完網頁，才能執行下一個流程 search)\n", "visit()"]}, {"cell_type": "code", "execution_count": null, "id": "da2543d4", "metadata": {"scrolled": true}, "outputs": [], "source": ["# 檢索 (有時候瀏覽器會執行比較久，要等它讀完網頁，才能執行下一個流程 getData)\n", "search()"]}, {"cell_type": "code", "execution_count": null, "id": "ac356a2e", "metadata": {"scrolled": false}, "outputs": [], "source": ["# 取得主要連結與資訊 (記得要先等上一步 search 流程的瀏覽器先讀完，不然會有問題)\n", "time_begin = time.time()\n", "getData()\n", "time_end = time.time()\n", "print(f\"總共執行了 { time_end - time_begin } 秒\")"]}, {"cell_type": "code", "execution_count": null, "id": "0eaf0d94", "metadata": {"scrolled": true}, "outputs": [], "source": ["# 手動關閉瀏覽器\n", "close()"]}, {"cell_type": "code", "execution_count": null, "id": "3a31b06e", "metadata": {"scrolled": true}, "outputs": [], "source": ["# 將 listData 儲存成 json\n", "<PERSON><PERSON><PERSON>()"]}, {"cell_type": "code", "execution_count": null, "id": "f736390d", "metadata": {"scrolled": true}, "outputs": [], "source": ["# 將 json 資料儲存至 excel 當中 (要先有 json 檔)\n", "saveExcel()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}}, "nbformat": 4, "nbformat_minor": 5}