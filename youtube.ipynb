'''
匯入套件
'''
# 操作 browser 的 API
from selenium import webdriver
from selenium.webdriver.chrome.service import Service

# ChromeDriver 的下載管理工具
from webdriver_manager.chrome import ChromeDriverManager

# 處理逾時例外的工具
from selenium.common.exceptions import TimeoutException

# 面對動態網頁，等待某個元素出現的工具，通常與 exptected_conditions 搭配
from selenium.webdriver.support.ui import WebDriverWait

# 搭配 WebDriverWait 使用，對元素狀態的一種期待條件，若條件發生，則等待結束，往下一行執行
from selenium.webdriver.support import expected_conditions as EC

# 期待元素出現要透過什麼方式指定，通常與 EC、WebDriverWait 一起使用
from selenium.webdriver.common.by import By

# 強制等待 (執行期間休息一下)
from time import sleep

# 整理 json 使用的工具
import json

# 執行 command 的時候用的
import os

# 子處理程序，用來取代 os.system 的功能
import subprocess

# 下載檔案的工具
import wget


'''
Selenium with Python 中文翻譯文檔
參考網頁：https://selenium-python-zh.readthedocs.io/en/latest/index.html
selenium 啓動 Chrome 的進階配置參數
參考網址：https://stackoverflow.max-everyday.com/2019/12/selenium-chrome-options/
'''

# 啟動瀏覽器工具的選項
my_options = webdriver.ChromeOptions()
# my_options.add_argument("--headless")                #不開啟實體瀏覽器背景執行
my_options.add_argument("--start-maximized")         #最大化視窗
my_options.add_argument("--incognito")               #開啟無痕模式
my_options.add_argument("--disable-popup-blocking") #禁用彈出攔截
my_options.add_argument("--disable-notifications")  #取消 chrome 推播通知
my_options.add_argument("--lang=zh-TW")  #設定為正體中文

# 使用 Chrome 的 WebDriver
driver = webdriver.Chrome(
    options = my_options
)

# 建立儲存圖片、影片的資料夾
folderPath = 'youtube'
if not os.path.exists(folderPath):
    os.makedirs(folderPath)

# 放置爬取的資料
listData = []

'''
以 function 名稱，作為爬蟲流程
'''

# 走訪頁面
def visit():
    driver.get('https://www.youtube.com/');

# 輸入關鍵字
def search():
    # 輸入名稱
    txtInput = driver.find_element(By.CSS_SELECTOR, "input#search")
    txtInput.send_keys("張學友")
    
    # 等待一下
    sleep(1)
    
    # 送出表單資料
    txtInput.submit()
    
    # 等待一下
    sleep(1)
    
# 篩選 (選項)
def filterFunc():
    try:
        # 等待篩選元素出現
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located( 
                (By.CSS_SELECTOR, "ytd-toggle-button-renderer.style-scope.ytd-search-sub-menu-renderer") 
            )
        )

        #按下篩選元素，使項目浮現
        driver.find_element(
            By.CSS_SELECTOR, 
            "ytd-toggle-button-renderer.style-scope.ytd-search-sub-menu-renderer"
        ).click()

        # 等待一下
        sleep(2)

        # 按下選擇的項目
        driver.find_elements(
            By.CSS_SELECTOR, 
            "a#endpoint.yt-simple-endpoint.style-scope.ytd-search-filter-renderer"
        )[9].click()
        
        # 等待一下
        sleep(2)
        
    except TimeoutException:
        print("等待逾時")
        
# 滾動頁面
def scroll():
    '''
    innerHeight => 瀏覽器內部的高度
    offset => 當前捲動的量(高度)
    count => 累計無效滾動次數
    limit => 最大無效滾動次數
    '''
    innerHeight = 0
    offset = 0
    count = 0
    limit = 3
    
    # 在捲動到沒有元素動態產生前，持續捲動
    while count <= limit:
        # 每次移動高度
        offset = driver.execute_script(
            'return document.documentElement.scrollHeight;'
        )

        '''
        或是每次只滾動一點距離，
        以免有些網站會在移動長距離後，
        將先前移動當中的元素隱藏

        例如將上方的 script 改成:
        offset += 600
        '''

        # 捲軸往下滑動
        driver.execute_script(f'''
            window.scrollTo({{
                top: {offset}, 
                behavior: 'smooth' 
            }});
        ''')
        
        '''
        [補充]
        如果要滾動的是 div 裡面的捲軸，可以使用以下的方法
        document.querySelector('div').scrollTo({...})
        '''
        
        # (重要)強制等待，此時若有新元素生成，瀏覽器內部高度會自動增加
        sleep(3)
        
        # 透過執行 js 語法來取得捲動後的當前總高度
        innerHeight = driver.execute_script(
            'return document.documentElement.scrollHeight;'
        )
        
        # 經過計算，如果滾動距離(offset)大於等於視窗內部總高度(innerHeight)，代表已經到底了
        if offset == innerHeight:
            count += 1
            
        # 為了實驗功能，捲動超過一定的距離，就結束程式
        if offset >= 600:
            break

# 分析頁面元素資訊
def parse():
    # 使用全域變數
    global listData
    
    # 清空存放資料的變數
    listData.clear()
    
    # 取得主要元素的集合
    elements = driver.find_elements(
        By.CSS_SELECTOR, 
        'ytd-video-renderer.style-scope.ytd-item-section-renderer'
    )
    
    # 逐一檢視元素
    for elm in elements:
        # 印出分隔文字
        print("=" * 30)
        
        # 取得圖片連結
        img = elm.find_element(
            By.CSS_SELECTOR, 
            "a#thumbnail img"
        )
        imgSrc = img.get_attribute('src')
        print(imgSrc)
        
        # 取得資料名稱
        a = elm.find_element(By.CSS_SELECTOR, "a#video-title")
        aTitle = a.get_attribute('innerText')
        print(aTitle)
        
        # 取得 YouTube 連結
        aLink = a.get_attribute('href')
        print(aLink)
        
        # 取得 影音 ID
        strDelimiter = ''
        if 'shorts' in aLink:
            strDelimiter = '/shorts/'
        else:
            strDelimiter = 'v='  
        youtube_id = aLink.split(strDelimiter)[1]
        print(youtube_id)
        
        # 放資料到 list 中
        listData.append({
            "id": youtube_id,
            "title": aTitle,
            "link": aLink,
            "img": imgSrc
        })

# 將 list 存成 json
def saveJson():
    with open(f"{folderPath}/youtube.json", "w", encoding='utf-8') as file:
        file.write( json.dumps(listData, ensure_ascii=False, indent=4) )
    
# 關閉瀏覽器
def close():
    driver.quit()

# 主程式
if __name__ == '__main__':
    visit()
    search()
    filterFunc()
    scroll()
    parse()
    saveJson()

# 等確定擷取流程結束後，再手動關閉瀏覽器，以便 debug，減少瀏覽器開開關關
close()

'''
yt-dlp 專案
https://github.com/yt-dlp/yt-dlp

下載 yt-dlp (Linux 和 MacOS 版本，要設定「chmod +x yt-dlp」，MacOS 也要改成 yt-dlp)
- Windows: https://github.com/yt-dlp/yt-dlp/releases/latest/download/yt-dlp.exe
- Linux: https://github.com/yt-dlp/yt-dlp/releases/latest/download/yt-dlp
- MacOS: https://github.com/yt-dlp/yt-dlp/releases/latest/download/yt-dlp_macos

輸出檔案名稱範例
https://github.com/yt-dlp/yt-dlp#output-template-examples
'''

# 下載
def download():
    # 確認 yt-dlp 是否存在
    if not os.path.exists('./yt-dlp.exe'):
        print('[下載 yt-dlp]')
        wget.download('https://github.com/yt-dlp/yt-dlp/releases/latest/download/yt-dlp.exe', './yt-dlp.exe')
    
    # 開啟 json 檔案
    with open(f"{folderPath}/youtube.json", "r", encoding='utf-8') as file:
        #取得 json 字串
        strJson = file.read()
    
    # 將 json 轉成 list (裡面是 dict 集合)
    listResult = json.loads(strJson)
    
    # 下載所有檔案
    for index, obj in enumerate(listResult):
        if index == 3:
            break
        
        print(f"正在下載: {obj['link']}")
        
        # 定義指令
        cmd = [
            './yt-dlp.exe', 
            obj['link'], 
            '-f', 'b[ext=mp4]', 
            '-o', f'{folderPath}/%(id)s.%(ext)s'
        ]

        # 執行指令，並取得回傳結果 (subprocess 物件)
        obj_sp = subprocess.run(cmd)
        
        '''
        obj_sp 物件的內容:
        
        CompletedProcess(
            args=[
                './yt-dlp.exe', 
                'https://www.youtube.com/watch?v=XHCBKSI1ppM&pp=ygUJ5by15a245Y-L', 
                '-f', 
                'b[ext=mp4]', 
                '-o',
                'youtube/%(id)s.%(ext)s'
            ], 
            returncode=0
        )
        '''

        # 判斷指令行是否正常 (returncode == 0 代表正常)
        if obj_sp.returncode == 0:
            print('下載成功!!')
        else:
            print('下載失敗…')

            

'''另行下載'''
download()