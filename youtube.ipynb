{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [], "source": ["'''\n", "匯入套件\n", "'''\n", "# 操作 browser 的 API\n", "from selenium import webdriver\n", "from selenium.webdriver.chrome.service import Service\n", "\n", "# ChromeDriver 的下載管理工具\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "# 處理逾時例外的工具\n", "from selenium.common.exceptions import TimeoutException\n", "\n", "# 面對動態網頁，等待某個元素出現的工具，通常與 exptected_conditions 搭配\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "\n", "# 搭配 WebDriverWait 使用，對元素狀態的一種期待條件，若條件發生，則等待結束，往下一行執行\n", "from selenium.webdriver.support import expected_conditions as EC\n", "\n", "# 期待元素出現要透過什麼方式指定，通常與 EC、WebDriverWait 一起使用\n", "from selenium.webdriver.common.by import By\n", "\n", "# 強制等待 (執行期間休息一下)\n", "from time import sleep\n", "\n", "# 整理 json 使用的工具\n", "import json\n", "\n", "# 執行 command 的時候用的\n", "import os\n", "\n", "# 子處理程序，用來取代 os.system 的功能\n", "import subprocess\n", "\n", "# 下載檔案的工具\n", "import wget\n", "\n", "\n", "'''\n", "Selenium with Python 中文翻譯文檔\n", "參考網頁：https://selenium-python-zh.readthedocs.io/en/latest/index.html\n", "selenium 啓動 Chrome 的進階配置參數\n", "參考網址：https://stackoverflow.max-everyday.com/2019/12/selenium-chrome-options/\n", "'''\n", "\n", "# 啟動瀏覽器工具的選項\n", "my_options = webdriver.ChromeOptions()\n", "# my_options.add_argument(\"--headless\")                #不開啟實體瀏覽器背景執行\n", "my_options.add_argument(\"--start-maximized\")         #最大化視窗\n", "my_options.add_argument(\"--incognito\")               #開啟無痕模式\n", "my_options.add_argument(\"--disable-popup-blocking\") #禁用彈出攔截\n", "my_options.add_argument(\"--disable-notifications\")  #取消 chrome 推播通知\n", "my_options.add_argument(\"--lang=zh-TW\")  #設定為正體中文\n", "\n", "# 使用 Chrome 的 WebDriver\n", "driver = webdriver.Chrome(\n", "    options = my_options\n", ")\n", "\n", "# 建立儲存圖片、影片的資料夾\n", "folderPath = 'youtube'\n", "if not os.path.exists(folderPath):\n", "    os.makedirs(folderPath)\n", "\n", "# 放置爬取的資料\n", "listData = []"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [], "source": ["'''\n", "以 function 名稱，作為爬蟲流程\n", "'''\n", "\n", "# 走訪頁面\n", "def visit():\n", "    driver.get('https://www.youtube.com/');\n", "\n", "# 輸入關鍵字\n", "def search():\n", "    # 輸入名稱\n", "    txtInput = driver.find_element(By.CSS_SELECTOR, \"input#search\")\n", "    txtInput.send_keys(\"張學友\")\n", "    \n", "    # 等待一下\n", "    sleep(1)\n", "    \n", "    # 送出表單資料\n", "    txtInput.submit()\n", "    \n", "    # 等待一下\n", "    sleep(1)\n", "    \n", "# 篩選 (選項)\n", "def filterFunc():\n", "    try:\n", "        # 等待篩選元素出現\n", "        WebDriverWait(driver, 10).until(\n", "            EC.presence_of_element_located( \n", "                (By.CSS_SELECTOR, \"ytd-toggle-button-renderer.style-scope.ytd-search-sub-menu-renderer\") \n", "            )\n", "        )\n", "\n", "        #按下篩選元素，使項目浮現\n", "        driver.find_element(\n", "            By.CSS_SELECTOR, \n", "            \"ytd-toggle-button-renderer.style-scope.ytd-search-sub-menu-renderer\"\n", "        ).click()\n", "\n", "        # 等待一下\n", "        sleep(2)\n", "\n", "        # 按下選擇的項目\n", "        driver.find_elements(\n", "            By.CSS_SELECTOR, \n", "            \"a#endpoint.yt-simple-endpoint.style-scope.ytd-search-filter-renderer\"\n", "        )[9].click()\n", "        \n", "        # 等待一下\n", "        sleep(2)\n", "        \n", "    except TimeoutException:\n", "        print(\"等待逾時\")\n", "        \n", "# 滾動頁面\n", "def scroll():\n", "    '''\n", "    innerHeight => 瀏覽器內部的高度\n", "    offset => 當前捲動的量(高度)\n", "    count => 累計無效滾動次數\n", "    limit => 最大無效滾動次數\n", "    '''\n", "    innerHeight = 0\n", "    offset = 0\n", "    count = 0\n", "    limit = 3\n", "    \n", "    # 在捲動到沒有元素動態產生前，持續捲動\n", "    while count <= limit:\n", "        # 每次移動高度\n", "        offset = driver.execute_script(\n", "            'return document.documentElement.scrollHeight;'\n", "        )\n", "\n", "        '''\n", "        或是每次只滾動一點距離，\n", "        以免有些網站會在移動長距離後，\n", "        將先前移動當中的元素隱藏\n", "\n", "        例如將上方的 script 改成:\n", "        offset += 600\n", "        '''\n", "\n", "        # 捲軸往下滑動\n", "        driver.execute_script(f'''\n", "            window.scrollTo({{\n", "                top: {offset}, \n", "                behavior: 'smooth' \n", "            }});\n", "        ''')\n", "        \n", "        '''\n", "        [補充]\n", "        如果要滾動的是 div 裡面的捲軸，可以使用以下的方法\n", "        document.querySelector('div').scrollTo({...})\n", "        '''\n", "        \n", "        # (重要)強制等待，此時若有新元素生成，瀏覽器內部高度會自動增加\n", "        sleep(3)\n", "        \n", "        # 透過執行 js 語法來取得捲動後的當前總高度\n", "        innerHeight = driver.execute_script(\n", "            'return document.documentElement.scrollHeight;'\n", "        )\n", "        \n", "        # 經過計算，如果滾動距離(offset)大於等於視窗內部總高度(innerHeight)，代表已經到底了\n", "        if offset == innerHeight:\n", "            count += 1\n", "            \n", "        # 為了實驗功能，捲動超過一定的距離，就結束程式\n", "        if offset >= 600:\n", "            break\n", "\n", "# 分析頁面元素資訊\n", "def parse():\n", "    # 使用全域變數\n", "    global listData\n", "    \n", "    # 清空存放資料的變數\n", "    listData.clear()\n", "    \n", "    # 取得主要元素的集合\n", "    elements = driver.find_elements(\n", "        By.CSS_SELECTOR, \n", "        'ytd-video-renderer.style-scope.ytd-item-section-renderer'\n", "    )\n", "    \n", "    # 逐一檢視元素\n", "    for elm in elements:\n", "        # 印出分隔文字\n", "        print(\"=\" * 30)\n", "        \n", "        # 取得圖片連結\n", "        img = elm.find_element(\n", "            By.CSS_SELECTOR, \n", "            \"a#thumbnail img\"\n", "        )\n", "        imgSrc = img.get_attribute('src')\n", "        print(imgSrc)\n", "        \n", "        # 取得資料名稱\n", "        a = elm.find_element(By.CSS_SELECTOR, \"a#video-title\")\n", "        aTitle = a.get_attribute('innerText')\n", "        print(aTitle)\n", "        \n", "        # 取得 YouTube 連結\n", "        aLink = a.get_attribute('href')\n", "        print(aLink)\n", "        \n", "        # 取得 影音 ID\n", "        strDelimiter = ''\n", "        if 'shorts' in aLink:\n", "            strDelimiter = '/shorts/'\n", "        else:\n", "            strDelimiter = 'v='  \n", "        youtube_id = aLink.split(strDelimiter)[1]\n", "        print(youtube_id)\n", "        \n", "        # 放資料到 list 中\n", "        listData.append({\n", "            \"id\": youtube_id,\n", "            \"title\": a<PERSON><PERSON><PERSON>,\n", "            \"link\": aLink,\n", "            \"img\": imgSrc\n", "        })\n", "\n", "# 將 list 存成 json\n", "def save<PERSON><PERSON>():\n", "    with open(f\"{folderPath}/youtube.json\", \"w\", encoding='utf-8') as file:\n", "        file.write( json.dumps(listData, ensure_ascii=False, indent=4) )\n", "    \n", "# 關閉瀏覽器\n", "def close():\n", "    driver.quit()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": false}, "outputs": [], "source": ["# 主程式\n", "if __name__ == '__main__':\n", "    visit()\n", "    search()\n", "    filterFunc()\n", "    scroll()\n", "    parse()\n", "    <PERSON><PERSON><PERSON>()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [], "source": ["# 等確定擷取流程結束後，再手動關閉瀏覽器，以便 debug，減少瀏覽器開開關關\n", "close()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [], "source": ["'''\n", "yt-dlp 專案\n", "https://github.com/yt-dlp/yt-dlp\n", "\n", "下載 yt-dlp (Linux 和 MacOS 版本，要設定「chmod +x yt-dlp」，MacOS 也要改成 yt-dlp)\n", "- Windows: https://github.com/yt-dlp/yt-dlp/releases/latest/download/yt-dlp.exe\n", "- Linux: https://github.com/yt-dlp/yt-dlp/releases/latest/download/yt-dlp\n", "- MacOS: https://github.com/yt-dlp/yt-dlp/releases/latest/download/yt-dlp_macos\n", "\n", "輸出檔案名稱範例\n", "https://github.com/yt-dlp/yt-dlp#output-template-examples\n", "'''\n", "\n", "# 下載\n", "def download():\n", "    # 確認 yt-dlp 是否存在\n", "    if not os.path.exists('./yt-dlp.exe'):\n", "        print('[下載 yt-dlp]')\n", "        wget.download('https://github.com/yt-dlp/yt-dlp/releases/latest/download/yt-dlp.exe', './yt-dlp.exe')\n", "    \n", "    # 開啟 json 檔案\n", "    with open(f\"{folderPath}/youtube.json\", \"r\", encoding='utf-8') as file:\n", "        #取得 json 字串\n", "        strJson = file.read()\n", "    \n", "    # 將 json 轉成 list (裡面是 dict 集合)\n", "    listResult = json.loads(strJson)\n", "    \n", "    # 下載所有檔案\n", "    for index, obj in enumerate(listResult):\n", "        if index == 3:\n", "            break\n", "        \n", "        print(f\"正在下載: {obj['link']}\")\n", "        \n", "        # 定義指令\n", "        cmd = [\n", "            './yt-dlp.exe', \n", "            obj['link'], \n", "            '-f', 'b[ext=mp4]', \n", "            '-o', f'{folderPath}/%(id)s.%(ext)s'\n", "        ]\n", "\n", "        # 執行指令，並取得回傳結果 (subprocess 物件)\n", "        obj_sp = subprocess.run(cmd)\n", "        \n", "        '''\n", "        obj_sp 物件的內容:\n", "        \n", "        CompletedProcess(\n", "            args=[\n", "                './yt-dlp.exe', \n", "                'https://www.youtube.com/watch?v=XHCBKSI1ppM&pp=ygUJ5by15a245Y-L', \n", "                '-f', \n", "                'b[ext=mp4]', \n", "                '-o',\n", "                'youtube/%(id)s.%(ext)s'\n", "            ], \n", "            returncode=0\n", "        )\n", "        '''\n", "\n", "        # 判斷指令行是否正常 (returncode == 0 代表正常)\n", "        if obj_sp.returncode == 0:\n", "            print('下載成功!!')\n", "        else:\n", "            print('下載失敗…')\n", "\n", "            \n", "\n", "'''另行下載'''\n", "download()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 4}