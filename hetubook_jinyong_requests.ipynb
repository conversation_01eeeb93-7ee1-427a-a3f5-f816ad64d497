{"cells": [{"cell_type": "code", "execution_count": null, "id": "6814170b", "metadata": {}, "outputs": [], "source": ["'''匯入套件'''\n", "import requests as req\n", "from bs4 import BeautifulSoup as bs\n", "import json, os, time, random, pprint, re\n", "\n", "# 隨機取得 User-Agent\n", "'''\n", "# 從外部資料來取得清單，清單預設儲存路徑: /tmp\n", "ua = UserAgent(use_external_data=True)\n", "# 從外部資料來取得清單，儲存在指定路徑\n", "ua = UserAgent(use_external_data=True, cache_path=/home/<USER>\n", "\n", "更詳細的說明，請見以下網頁:\n", "https://pypi.org/project/fake-useragent/\n", "'''\n", "from fake_useragent import UserAgent\n", "ua = UserAgent(use_external_data=True)\n", "\n", "'''放置 金庸小說 metadata 的資訊'''\n", "listData = []\n", "\n", "'''金庸小說的網址'''\n", "prefix = 'https://hetubook.com'\n", "list_urls = [\n", "    prefix + '/tag/金庸-1.html',\n", "    prefix + '/tag/金庸-2.html'\n", "]\n", "\n", "'''設定標頭'''\n", "my_headers = {\n", "    'user-agent': ua.random,\n", "    'referer': 'https://hetubook.com/book2/56/index.html',\n", "}\n", "\n", "\n", "# 沒有放置 txt 檔的資料夾，就建立起來\n", "folderPath = 'jinyong'\n", "if not os.path.exists(folderPath):\n", "    os.makedirs(folderPath)"]}, {"cell_type": "code", "execution_count": null, "id": "a12857e8", "metadata": {}, "outputs": [], "source": ["'''\n", "自訂函式\n", "'''\n", "# 取得小說的主要連結\n", "def getMainLinks():    \n", "    # 清除 list 內容\n", "    listData.clear()\n", "    \n", "    # 走訪首頁\n", "    for link in list_urls:\n", "        res = req.get(url = link, headers = my_headers, cookies = my_cookies)\n", "        soup = bs(res.text, \"lxml\")\n", "\n", "        # 取得每一本指定的小說連結\n", "        for a in soup.select('dl#body.list dd h4 a[href]'):\n", "            # 取得主要連結相關資訊\n", "            listData.append({\n", "                'title': a.get_text(),\n", "                'link': prefix + a['href'],\n", "                'sub': [] # 之後會放置每一本小說的章回資訊\n", "            })\n", "        \n", "    # 預覽結果\n", "    # pprint.pprint(listData)\n", "    \n", "\n", "# 取得所有小說的獨立連結\n", "def getSubLinks():\n", "    # 取得章回列表\n", "    for index in range( len(listData) ):\n", "        res = req.get(url = listData[index]['link'], headers = my_headers)\n", "        soup = bs(res.text, \"lxml\")\n", "        \n", "        for a in soup.select('dl#dir > dd > a[href][title]'):\n", "            listData[index]['sub'].append({\n", "                'title': a['title'], # 或是 a.get_text() 來取得 title\n", "                'link': prefix + a['href'],\n", "                'content': '' # 預留給小說內文\n", "            })\n", "            \n", "        # 隨機等待\n", "        # time.sleep(random.randint(1, 3))\n", "    \n", "    # 預覽結果\n", "    # pprint.pprint(listData)\n", "    \n", "    \n", "# 將金庸小說所有章回的內容，各自寫到 txt 與 json 中\n", "def writeTxt():\n", "    for index in range( len(listData) ):\n", "        for idx in range( len(listData[index]['sub']) ):\n", "            sess = req.Session()\n", "            res = sess.get(url = listData[index]['sub'][idx]['link'], headers = my_headers)\n", "            for key, value in res.cookies.items():\n", "                print(key + '=' + value)\n", "            \n", "            soup = bs(res.text, \"lxml\")\n", "            \n", "            # 用 len( soup.select('CSS_SELECTOR') ) 來判斷文章區域是否存在，存在則儲存起來\n", "            if len(soup.select('div#content')) > 0:\n", "                # 取得內文所在的元素\n", "                elm = soup.select_one('div#content')\n", "                \n", "                # 刪除不必要的元素\n", "                elm.select_one('h2.h2').decompose()\n", "                \n", "                # 取得小說內文\n", "                content = ''\n", "                for div in elm.select('div'):\n", "                    content += div.get_text()\n", "                \n", "                pprint.pprint(elm)\n", "                \n", "                # 更新 json 的 content 節點\n", "                listData[index]['sub'][idx]['content'] = content\n", "                \n", "                # 將小說內文額外存成 txt 檔\n", "                file_name = f\"{listData[index]['title']}_{listData[index]['sub'][idx]['title']}\"\n", "                with open(f\"{folderPath}/{file_name}.txt\", \"w\", encoding=\"utf-8\") as file:\n", "                    file.write(content)\n", "                    \n", "            break\n", "                    \n", "            # 隨機等待\n", "            # time.sleep(random.randint(1, 3))\n", "            \n", "        break\n", "                \n", "\n", "# 建立金庸小說的 json 檔\n", "def save<PERSON><PERSON>():\n", "    with open(f\"{folderPath}/hetubook_jinyong_requests.json\", \"w\", encoding=\"utf-8\") as file:\n", "        file.write( json.dumps(listData, ensure_ascii=False, indent=4) )"]}, {"cell_type": "code", "execution_count": null, "id": "cf85fada", "metadata": {"scrolled": false}, "outputs": [], "source": ["# 主程式\n", "if __name__ == \"__main__\":\n", "    time_begin = time.time()\n", "    getMainLinks()\n", "    getSubLinks()\n", "    writeTxt()\n", "#     save<PERSON><PERSON>()\n", "    time_end = time.time()\n", "    print(f\"總共執行 {time_end - time_begin} 秒\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}