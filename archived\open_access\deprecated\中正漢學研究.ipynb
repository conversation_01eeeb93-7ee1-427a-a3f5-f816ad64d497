{"cells": [{"cell_type": "code", "execution_count": null, "id": "1bd181e8", "metadata": {"scrolled": false}, "outputs": [], "source": ["'''\n", "中正漢學研究\n", "http://140.123.13.91/journal/journal.html\n", "'''\n", "\n", "'''\n", "匯入套件\n", "'''\n", "# 請求套件\n", "import requests as req\n", "\n", "# 格式化輸出工具\n", "from pprint import pprint as pp\n", "\n", "# HTML Parser\n", "from bs4 import BeautifulSoup as bs\n", "\n", "# 強制等待 (執行期間休息一下)\n", "from time import sleep\n", "\n", "# 整理 json 使用的工具\n", "import json\n", "\n", "# regular expression 工具\n", "import re\n", "\n", "# 子處理程序，用來取代 os.system 的功能\n", "import subprocess\n", "\n", "# 建立隨機數\n", "from random import randint\n", "\n", "# 資料庫 (sqlite3)\n", "import sqlite3\n", "\n", "# Excel 工具\n", "from openpyxl import load_workbook\n", "from openpyxl import Workbook\n", "\n", "# 時間工具\n", "from datetime import datetime\n", "\n", "# 其它\n", "import json, os, sys\n", "\n", "'''設定'''\n", "# 主要首頁\n", "prefix = 'http://140.123.13.91/journal/'\n", "url = prefix + 'journal.html'\n", "\n", "# JSON 存檔路徑\n", "json_path = \"./中正漢學研究.json\"\n", "\n", "# 自訂標頭\n", "my_headers = {\n", "    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36'\n", "}\n", "\n", "# 整理資料用的變數\n", "listData = []\n", "\n", "# 建立儲存檔案用的資料夾，不存在就新增\n", "folderPath = '中正漢學研究'\n", "if not os.path.exists(folderPath):\n", "    os.makedirs(folderPath)\n", "\n", "    \n", "'''程式區域'''\n", "# 取得主要頁面所有連結資訊\n", "def getMainLinks():\n", "    # 建立 parser\n", "    res = req.get(url, headers = my_headers)\n", "    res.encoding = 'utf-8'\n", "    soup = bs(res.text, \"lxml\")\n", "    \n", "    # 取得所有連結\n", "    a_elms = soup.select('table td.tdJournal > a')\n", "    for a in a_elms:        \n", "        listData.append({\n", "            \"title\": a.get_text(),\n", "            \"link\": f\"{prefix}{a['href']}\"\n", "        })\n", "\n", "# 取得內頁連結\n", "def getSubLinks():\n", "    # 走訪所有連結\n", "    for index in range( len(listData) ):\n", "        # 沒有 sub 屬性，則建立，為了放置細節頁的內容\n", "        if \"sub\" not in listData[index]:\n", "            listData[index]['sub'] = []\n", "            \n", "        #建立 parser\n", "        res = req.get(listData[index]['link'], headers = my_headers)\n", "        res.encoding = 'utf-8'\n", "        soup = bs(res.text, \"lxml\")\n", "\n", "        # 取得有 table 元素的所有連結資訊\n", "        tr_elms = soup.select('table tr')\n", "        if len(tr_elms) > 0:\n", "            # 走訪每一個 tr\n", "            for tr in tr_elms:\n", "                # td 底下確實有 a\n", "                if len(tr.select('td a')) > 0:\n", "                    # 資料清理\n", "                    a = tr.select('td')[0].select_one('a')\n", "                    sub_title = a.get_text()\n", "\n", "                    # 作者資訊\n", "                    author = tr.select('td')[1].get_text()\n", "                    \n", "                    # 整理超連結格式\n", "                    if re.search(r\"^https?:\\/\\/\", a['href']):\n", "                        sub_link = f\"{a['href']}\"\n", "                    else:\n", "                        sub_link = f\"{prefix}{a['href']}\"\n", "                    \n", "                    # 整合資料\n", "                    listData[index]['sub'].append({\n", "                        \"sub_title\": re.sub(r\"\\n|\\s\", \"\", sub_title),\n", "                        \"sub_link\": sub_link,\n", "                        \"author\": re.sub(r\"\\n|\\s\", \"\", author)\n", "                    })\n", "        else: \n", "            # 取得 p 元素底下的所有 a 連結資訊\n", "            a_elms = soup.select('p a')\n", "            if len(a_elms) > 0:\n", "                # 走訪所有 a\n", "                for a in a_elms:\n", "                    # 若 a 有 href 屬性\n", "                    if a.has_attr('href'):\n", "                        # 取得超連結\n", "                        href = a['href']\n", "\n", "                        # 判斷超連結是否有 article 字眼，代表可能是 pdf 連結\n", "                        if re.search(r\"article\", href):\n", "                            # 取得超連結內文\n", "                            sub_title = a.get_text()\n", "\n", "                            # 整理超連結格式\n", "                            sub_link = f\"{prefix}{a['href']}\"\n", "\n", "                            # 整合資料\n", "                            listData[index]['sub'].append({\n", "                                \"sub_title\": sub_title,\n", "                                \"sub_link\": sub_link,\n", "                                \"author\": \"\"\n", "                            })      \n", "                \n", "# 儲存 JSON\n", "def save<PERSON><PERSON><PERSON><PERSON>():\n", "    with open(f\"{folderPath}/{json_path}\", \"w\", encoding='utf-8') as fp:\n", "        fp.write( json.dumps(listData, ensure_ascii=False, indent=4) )\n", "        \n", "# 將 JSON 資料寫進資料庫\n", "def saveToDB():\n", "    # 資料庫連線\n", "    conn = sqlite3.connect(\"journals.db\")\n", "    \n", "    try:\n", "        # 預設空字串\n", "        strJson = ''\n", "        with open(json_path, \"r\", encoding='utf-8') as fp:\n", "            # 取得 JSON 字串\n", "            strJson = fp.read()\n", "\n", "        # json 字串轉 dict\n", "        listResult = json.loads(strJson)\n", "        \n", "        # 建立 cursor\n", "        cursor = conn.cursor()\n", "\n", "        # 取得資料總數\n", "        sql_insert = f'''\n", "        INSERT INTO `140_123_13_91_journals` (`title`, `link`, `sub_title`, `sub_link`, `author`, `created_at`)\n", "        VALUES (?,?,?,?,?,?)\n", "        '''\n", "\n", "        # 取得資料\n", "        for index in range( len(listResult) ):\n", "            title = listResult[index]['title']\n", "            link = listResult[index]['link']\n", "            for idx in range( len(listResult[index]['sub']) ):\n", "                sub_title = listResult[index]['sub'][idx]['sub_title']\n", "                sub_link = listResult[index]['sub'][idx]['sub_link']\n", "                author = listResult[index]['sub'][idx]['author']\n", "                created_at = datetime.today().strftime(\"%Y-%m-%d %H-%M-%S\")\n", "                \n", "                # 新增資料\n", "                cursor.execute(sql_insert, (title, link, sub_title, sub_link, author, created_at))\n", "                \n", "        # 執行 SQL\n", "        conn.commit()      \n", "    except:\n", "        # SQL 執行失敗時回滾\n", "        conn.rollback()\n", "        print(\"Unexpected error: \", sys.exc_info())\n", "        \n", "    # 關閉資料庫\n", "    conn.close()\n", "\n", "# 將 JSON 資料儲存到 Excel\n", "def saveToExcel():\n", "    # 動態新增檔案\n", "    workbook = Workbook()\n", "    worksheet = workbook.create_sheet(\"中正漢學研究\", 0)\n", "    \n", "    # 取得主要的 sheet\n", "    worksheet = workbook['中正漢學研究']\n", "\n", "    # 預設空字串\n", "    strJson = ''\n", "    with open(f\"{folderPath}/{json_path}\", \"r\", encoding='utf-8') as fp:\n", "        # 取得 JSON 字串\n", "        strJson = fp.read()\n", "\n", "    # json 字串轉 dict\n", "    listResult = json.loads(strJson)\n", "    \n", "    # 設定欄位名稱\n", "    worksheet['A1'] = 'id'\n", "    worksheet['B1'] = 'title'\n", "    worksheet['C1'] = 'link'\n", "    worksheet['D1'] = 'sub_title'\n", "    worksheet['E1'] = 'sub_link'\n", "    worksheet['F1'] = 'author'\n", "    worksheet['G1'] = 'created_at'\n", "    \n", "    # 列數\n", "    row = 0\n", "    \n", "    # 取得資料\n", "    for index in range( len(listResult) ):\n", "        title = listResult[index]['title']\n", "        link = listResult[index]['link']\n", "        for idx in range( len(listResult[index]['sub']) ):\n", "            sub_title = listResult[index]['sub'][idx]['sub_title']\n", "            sub_link = listResult[index]['sub'][idx]['sub_link']\n", "            author = listResult[index]['sub'][idx]['author']\n", "            created_at = datetime.today().strftime(\"%Y-%m-%d %H-%M-%S\")\n", "            \n", "            # 寫入 excel\n", "            worksheet['A' + str(row + 2)] = str(row + 1)\n", "            worksheet['B' + str(row + 2)] = title\n", "            worksheet['C' + str(row + 2)] = link\n", "            worksheet['D' + str(row + 2)] = sub_title\n", "            worksheet['E' + str(row + 2)] = sub_link\n", "            worksheet['F' + str(row + 2)] = author\n", "            worksheet['G' + str(row + 2)] = created_at\n", "            \n", "            # 遞增數量，以便後續對應列數寫入\n", "            row += 1\n", "            \n", "    # 儲存 workbook\n", "    exportFile = f\"{folderPath}/中正漢學研究.xlsx\"\n", "    workbook.save(exportFile)\n", "\n", "    # 關閉 workbook\n", "    workbook.close()\n", "\n", "# 下載 PDF\n", "def downloadPDF():\n", "    with open(f\"{folderPath}/{json_path}\", \"r\", encoding='utf-8') as fp:\n", "        #取得 json 字串\n", "        strJson = fp.read()\n", "        \n", "        # 將 json 轉成 list (裡面是 dict 集合)\n", "        listResult = json.loads(strJson)\n", "        \n", "        # 下載所有檔案\n", "        for obj in listResult:\n", "            # 每個 dict 底下，有個 key 叫作 sub，值裡面也是 dict\n", "            for o in obj['sub']:\n", "                # 下載 PDF\n", "                os.system(f\"curl {o['sub_link']} -o {folderPath}/{o['sub_title']}.pdf\")\n", "    \n", "'''\n", "執行區域\n", "'''\n", "if __name__ == \"__main__\":\n", "    getMainLinks()\n", "    getSubLinks()\n", "    save<PERSON><PERSON><PERSON><PERSON>()\n", "#     saveToDB()\n", "#     saveToExcel()\n", "#     downloadPDF()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}}, "nbformat": 4, "nbformat_minor": 5}