<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>

    <!-- leaflet css 設定 -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin="" />
    
    <!-- 自訂 css -->
    <style>
        table, thead, th, tbody, tr, td {
            border: 1px solid;
        }

        #map { height: 480px; }
    </style>
</head>
<body>
    <button id="btn_request">取得咖啡廳列表</button>

    <div id="map"></div>

    <table>
        <thead>
            <tr>
                <th>id</th>
                <th>city</th>
                <th>name</th>
                <th>address</th>
                <th>url</th>
            </tr>
        </thead>
        <tbody></tbody>
    </table>

    <!-- leaflet JS cdn -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
    
    <!-- 自訂 js -->
    <script>
        //引入地圖
        let map = L.map('map').setView([25.0339145, 121.5412233], 13);

        //初始化地圖圖層(預設)
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);

        //設定圖層群組
        let layerGroup = null;
        let arrMarkers = [];
        
        // 按鈕事件，取得 Web API 的回傳資料
        document.querySelector('button#btn_request').addEventListener('click', function(event){
            /**
             * 非同步傳輸的工具 - fetch
             * 透過 ajax (xml http request) 技術，
             * 做到不切換頁面也能請求資料/取得回應的方法
             * 
             * 參考網頁:
             * 1. 鐵人賽：ES6 原生 Fetch 遠端資料方法
             * https://www.casper.tw/javascript/2017/12/28/javascript-fetch/
             */
            fetch('http://localhost:5000/cafe_taipei',{
                method: "GET"
            }).then(function(response){
                /**
                 * response.text() - 純文字 or html
                 * response.blob() - 通常用於 base64 編碼後的 img 內容
                 * response.json() - 若回傳的格式為 json 文字，自動轉成物件 (Object)
                 */
                return response.json();
            }).then(function(arr){
                //刪除先前的 markers
                if( layerGroup != null && map.hasLayer(layerGroup) ) {
                    layerGroup.clearLayers();
                    map.removeLayer(layerGroup);

                    //變數初始化
                    layerGroup = null;
                    arrMarkers = [];
                }

                // 取得 tbody 元素
                let tbody = document.querySelector('table > tbody');

                // 清空 tbody 底下既有元素，如 tr td 等
                tbody.innerHTML = '';

                // 將回傳資料動態生成在網頁上
                for(let o of arr){
                    //輸出對應的 html tag
                    let tr = document.createElement("tr");
                    tr.innerHTML = `<td>${o['id']}</td>
                                    <td>${o['city']}</td>
                                    <td>${o['name']}</td>
                                    <td>${o['address']}</td>
                                    <td><a href="${o['url']}" target="_blank">連結</a></td>`;
                    tbody.appendChild(tr);

                    //建立 markers
                    let marker = L.marker([o['latitude'], o['longitude']])
                    .bindPopup(`${o['name']}<br><a href="${o['url']}" target="_blank">連結</a>`);

                    //自訂事件
                    marker.addEventListener('click', function(event){
                        console.log(o);
                    });

                    //將 markers 各別放到空陣列 arrMarkers 當中
                    arrMarkers.push(marker);
                }

                //迴圈執行完畢後，將 arrMarkers 放到 layerGroup 當中
                layerGroup = L.layerGroup(arrMarkers);

                //將 layerGroup 放到 map 當中
                layerGroup.addTo(map);
            });
        });
    </script>
</body>
</html>