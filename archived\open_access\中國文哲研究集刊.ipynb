{"cells": [{"cell_type": "markdown", "id": "b60706be", "metadata": {}, "source": ["# 套件安裝"]}, {"cell_type": "code", "execution_count": null, "id": "dbd8b0a9", "metadata": {}, "outputs": [], "source": ["!pip install -U openpyxl selenium beautifulsoup4 lxml requests"]}, {"cell_type": "markdown", "id": "297fae13", "metadata": {}, "source": ["# 設定初始化"]}, {"cell_type": "code", "execution_count": null, "id": "27626a73", "metadata": {}, "outputs": [], "source": ["'''\n", "注意事項:\n", "下載對應的 ChromeDriver (web driver) 到程式檔案同一個目錄下後解壓縮，下載前記得對應版本編號。\n", "連結: https://chromedriver.chromium.org/downloads\n", "\n", "參考網頁:\n", "[1] 中央研究院中國文哲研究所\n", "https://www.litphil.sinica.edu.tw/publications/bulletin\n", "'''\n", "\n", "\n", "'''\n", "匯入套件\n", "'''\n", "# HTML parser\n", "from bs4 import BeautifulSoup as bs\n", "\n", "# 操作 browser 的 API\n", "from selenium import webdriver\n", "\n", "# 處理逾時例外的工具\n", "from selenium.common.exceptions import TimeoutException\n", "\n", "# 面對動態網頁，等待某個元素出現的工具，通常與 exptected_conditions 搭配\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "\n", "# 搭配 WebDriverWait 使用，對元素狀態的一種期待條件，若條件發生，則等待結束，往下一行執行\n", "from selenium.webdriver.support import expected_conditions as EC\n", "\n", "# 期待元素出現要透過什麼方式指定，通常與 EC、WebDriverWait 一起使用\n", "from selenium.webdriver.common.by import By\n", "\n", "# 處理下拉式選單的工具\n", "from selenium.webdriver.support.ui import Select\n", "\n", "# 強制等待 (執行期間休息一下)\n", "from time import sleep\n", "\n", "# pretty-print\n", "from pprint import pprint\n", "\n", "# 隨機\n", "from random import randint\n", "\n", "# 計時\n", "import time\n", "\n", "# 整理 json 使用的工具\n", "import json\n", "\n", "# 執行 shell command 的時候用的\n", "import os\n", "\n", "# 子處理程序，用來取代 os.system 的功能\n", "import subprocess\n", "\n", "# 正規表達式\n", "import re\n", "\n", "# 編碼\n", "from urllib.parse import quote\n", "\n", "# 存取 Excel 的工具\n", "from openpyxl import load_workbook\n", "from openpyxl import Workbook\n", "\n", "# 啟動瀏覽器工具的選項\n", "my_options = webdriver.ChromeOptions()\n", "# my_options.add_argument(\"--headless\")             #不開啟實體瀏覽器背景執行\n", "my_options.add_argument(\"--start-maximized\")        #最大化視窗\n", "my_options.add_argument(\"--incognito\")              #開啟無痕模式\n", "my_options.add_argument(\"--disable-popup-blocking\") #禁用彈出攔截\n", "my_options.add_argument(\"--disable-notifications\")  #取消通知\n", "my_options.add_argument(\"--lang=zh-TW\")  #設定為正體中文\n", "\n", "# 指定 chromedriver 檔案的路徑\n", "driver_exec_path = './chromedriver.exe'\n", "\n", "# 給 web driver 用的變數\n", "driver = None\n", "\n", "# 來源首頁\n", "prefix_url = 'https://www.litphil.sinica.edu.tw/'\n", "url = prefix_url + 'publications/bulletin'\n", "\n", "# 指定 sheet name\n", "sheetName = 'litphil_sinica'\n", "\n", "# 指定 excel 檔名\n", "excelFileName = 'litphil_sinica.xlsx'\n", "\n", "# 指定 json 檔名\n", "jsonFileName = f'{sheetName}.json'\n", "\n", "# 建立儲存圖片、影片的資料夾\n", "folderPath = f'./{sheetName}'\n", "if not os.path.exists(folderPath):\n", "    os.makedirs(folderPath)\n", "\n", "# 判斷 excel 檔案是否存在，不存在就新增\n", "filePath = folderPath + '/' + excelFileName\n", "if not os.path.exists(filePath):\n", "    workbook = Workbook() # 動態新增檔案\n", "    worksheet = workbook.create_sheet(sheetName, 0) # 建立並取得 active sheet\n", "else:\n", "    workbook = load_workbook(filename = filePath)\n", "    worksheet = workbook[sheetName] # 取得 active sheet\n", "\n", "#預設下載路徑\n", "my_options.add_experimental_option(\"prefs\", {\n", "    \"download.default_directory\": folderPath\n", "})\n", "    \n", "# excel 標題\n", "worksheet['A1'] = \"流水號\"\n", "worksheet['B1'] = \"期刊名稱\"\n", "worksheet['C1'] = \"期數\"\n", "worksheet['D1'] = '網頁連結'\n", "worksheet['E1'] = \"出版商\"\n", "worksheet['F1'] = \"出版日期\"\n", "worksheet['G1'] = \"論文名稱\"\n", "worksheet['H1'] = \"作者名稱\"\n", "worksheet['I1'] = \"論文連結_原始\"\n", "worksheet['J1'] = \"論文連結_curl可用\"\n", "\n", "# 放置爬取的資料\n", "listData = []"]}, {"cell_type": "markdown", "id": "14b48047", "metadata": {}, "source": ["# 自訂函式 (網路爬蟲執行流程)"]}, {"cell_type": "code", "execution_count": null, "id": "5f8a15ec", "metadata": {}, "outputs": [], "source": ["'''\n", "函式\n", "'''\n", "# 初始化 Web Driver\n", "def init():\n", "     global driver\n", "    # 使用 Chrome 的 WebDriver\n", "    driver = webdriver.Chrome( \n", "        options = my_options, \n", "        executable_path = driver_exec_path\n", "    )\n", "\n", "# 為 pagination 開啟 tabs\n", "def openTabs():\n", "    global driver\n", "    driver.get(url) #進入來源網頁\n", "    try:\n", "        # 等待目標元素出現\n", "        WebDriverWait(driver, 10).until(\n", "            EC.presence_of_element_located(\n", "                (By.CSS_SELECTOR, 'ul.pagination > li')\n", "            )\n", "        )\n", "        \n", "        # 取得分頁列表\n", "        li_elms = driver.find_elements(\n", "            By.CSS_SELECTOR, 'ul.pagination > li'\n", "        )\n", "        \n", "        # 開啟分頁\n", "        for li in li_elms:\n", "            # 開啟新分頁\n", "            driver.execute_script(f'window.open(\"{url}\", \"_blank\");')\n", "            \n", "    except TimeoutException as e:\n", "        print('等待逾時: openTabs')\n", "        \n", "# 設定 tabs 內容\n", "def setTabs():\n", "    global driver\n", "    try:\n", "        # 切換 tab，同時各別抓取資料\n", "        windows = driver.window_handles\n", "        for index in range(len(windows)):\n", "            # 跳過主要分頁\n", "            if index == 0: continue\n", "                \n", "            # 跳到指定分頁\n", "            driver.switch_to.window(windows[index])\n", "\n", "            # 取得分頁列表\n", "            li_elms = driver.find_elements(\n", "                By.CSS_SELECTOR, 'ul.pagination > li'\n", "            )\n", "            \n", "            # 按下每一個分頁\n", "            li_elms[index - 1].click()\n", "            \n", "            # 等待一下\n", "            sleep(randint(1,2))\n", "            \n", "            # 切換到初始分頁\n", "            driver.switch_to.window(driver.window_handles[0])\n", "                \n", "    except TimeoutException as e:\n", "        print('等待逾時: setTabs')\n", "    \n", "# 剖析內容\n", "def parse():\n", "    global driver\n", "    global listData\n", "    try:\n", "        # 流水號\n", "        sn = 1\n", "        \n", "        # 切換 tab，同時各別抓取資料\n", "        windows = driver.window_handles\n", "        for index in range(len(windows)):\n", "            # 跳過主要分頁\n", "            if index == 0: continue\n", "                \n", "            # 跳到指定分頁\n", "            driver.switch_to.window(windows[index])\n", "            \n", "            # 放置期刊編號列表\n", "            listJournalNum = []\n", "\n", "            # 各別取得內頁資訊\n", "            for li in driver.find_elements(By.CSS_SELECTOR, 'ul.publications.gridView.ng-scope > li'):\n", "                # 取得期刊號碼對應數字\n", "                strStyle = li.find_element(By.CSS_SELECTOR, 'div.cover').get_attribute('style')\n", "                regexNum = r'\\/(\\d{2,})\\/'\n", "                strNum = re.search(regexNum, strStyle)[1]\n", "                listJournalNum.append(strNum)\n", "                \n", "            for num in listJournalNum:\n", "                # 前往內頁取得資料\n", "                driver.get(f'https://www.litphil.sinica.edu.tw/publications/bulletin/{num}')\n", "                sleep(randint(1,2))\n", "                html = driver.page_source\n", "                soup = bs(html, 'lxml')\n", "                \n", "                # 取得期刊名稱與期數\n", "                div_name = soup.select_one('div.name.ng-binding')\n", "                strJournalSubtitle = div_name.find('span').text\n", "                strJournalTitle = div_name.text.replace(div_name.find('span').text, '')\n", "                \n", "                # 取得出版商與出版日期\n", "                strPublisher = strDate = \"\"\n", "                if len(soup.select('span.ng-binding.ng-scope[ng-if=\"doc.publisher\"]')) > 0:\n", "                    span_publisher = soup.select_one('span.ng-binding.ng-scope[ng-if=\"doc.publisher\"]')\n", "                    strPublisher = span_publisher.text.replace(span_publisher.find('b').text, '')\n", "                if len(soup.select('span.ng-binding.ng-scope[ng-if=\"doc.date\"]')) > 0:\n", "                    span_date = soup.select_one('span.ng-binding.ng-scope[ng-if=\"doc.date\"]')\n", "                    strDate = span_date.text.replace(span_date.find('b').text, '')\n", "                \n", "                # 取得超連結\n", "                a_elms = soup.select('li.ng-scope[ng-repeat=\"section in doc.sections\"] li.ng-scope[ng-repeat=\"article in section.articles\"] a.ng-binding')\n", "                for a in a_elms:\n", "                    # 取得有 href 屬性的 a 連結\n", "                    if a.has_attr('href') and 'pdf' in a['href']:\n", "                        # 處理論文名稱問題\n", "                        strAuthor = a.find('span').text.replace('／', '')\n", "                        strName = a.text.replace(a.find('span').text, '')\n", "                        regexName = r\"\\s|\\n\"\n", "                        strName = re.sub(regexName, '', strName)\n", "                        \n", "                        # 論文連結處理\n", "                        if 'http' in a['href']:\n", "                            strPdfOriginPath = a['href']\n", "                        else:\n", "                            strPdfOriginPath = prefix_url + a['href']\n", "                        \n", "                        # 有些連結有誤，經觀察，將 site 改成 www 即可\n", "                        if 'http://site.' in strPdfOriginPath:\n", "                            strPdfOriginPath = strPdfOriginPath.replace(\"http://site.\", \"http://www.\")\n", "                            \n", "                        regexPdfPath = r'(https?:\\/\\/.+(?<=\\d\\/))(.+)\\.pdf'\n", "                        matchPdfPath = re.search(regexPdfPath, strPdfOriginPath)\n", "                        if '%' in matchPdfPath[2]:\n", "                            strPdfCurlPath = matchPdfPath[1] + matchPdfPath[2] + '.pdf'\n", "                        else:\n", "                            strPdfCurlPath = matchPdfPath[1] + quote(matchPdfPath[2]) + '.pdf'\n", "\n", "                        # 整理資料\n", "                        listData.append({\n", "                            \"流水號\": sn,\n", "                            \"期刊名稱\": strJournalTitle,\n", "                            \"期數\": strJournalSubtitle,\n", "                            \"網頁連結\": f'https://www.litphil.sinica.edu.tw/publications/bulletin/{num}',\n", "                            \"出版商\": strPublisher,\n", "                            \"出版日期\": strDate,\n", "                            \"論文名稱\": str<PERSON><PERSON>,\n", "                            \"作者名稱\": str<PERSON><PERSON><PERSON>,\n", "                            \"論文連結_原始\": strPdfOriginPath,\n", "                            \"論文連結_curl可用\": strPdfCurlPath\n", "                        })\n", "\n", "                        # 遞增流水號\n", "                        sn += 1\n", "            \n", "    except TimeoutException as e:\n", "        print('等待逾時: parse')\n", "        \n", "# 關閉瀏覽器\n", "def close():\n", "    driver.quit()\n", "        \n", "# 儲存成 json\n", "def save<PERSON><PERSON>():\n", "    global listData\n", "    with open(f\"{folderPath}/{jsonFileName}\", \"w\", encoding=\"utf-8\") as file:\n", "        file.write( json.dumps( listData, ensure_ascii=False, indent=4 ) )\n", "\n", "# 儲存成 excel\n", "def saveExcel():\n", "    with open(f\"{folderPath}/{jsonFileName}\", \"r\", encoding=\"utf-8\") as file:\n", "        # 從 excel 列號 2 開始寫入資料\n", "        row_num = 2\n", "        \n", "        # 取得 json 內容\n", "        strJson = file.read()\n", "        \n", "        # 將 json 轉成 list\n", "        listJson = json.loads(strJson)\n", "        \n", "        # 逐列寫入\n", "        for myDict in list<PERSON><PERSON>:\n", "            worksheet['A' + str(row_num)] = myDict[\"流水號\"]\n", "            worksheet['B' + str(row_num)] = myDict[\"期刊名稱\"]\n", "            worksheet['C' + str(row_num)] = myDict[\"期數\"]\n", "            worksheet['D' + str(row_num)] = myDict['網頁連結']\n", "            worksheet['E' + str(row_num)] = myDict[\"出版商\"]\n", "            worksheet['F' + str(row_num)] = myDict[\"出版日期\"]\n", "            worksheet['G' + str(row_num)] = myDict[\"論文名稱\"]\n", "            worksheet['H' + str(row_num)] = myDict[\"作者名稱\"]\n", "            worksheet['I' + str(row_num)] = myDict[\"論文連結_原始\"]\n", "            worksheet['J' + str(row_num)] = myDict[\"論文連結_curl可用\"]\n", "            row_num += 1\n", "    \n", "    # 儲存 workbook\n", "    workbook.save(filePath)\n", "\n", "    # 關閉 workbook\n", "    workbook.close()\n", "    \n", "# 下載\n", "def download():\n", "    with open(f\"{folderPath}/{jsonFileName}\", \"r\", encoding=\"utf-8\") as file:      \n", "        # 取得 json 內容\n", "        strJson = file.read()\n", "        \n", "        # 將 json 轉成 list\n", "        listJson = json.loads(strJson)\n", "        \n", "        for myDict in list<PERSON><PERSON>:\n", "            # 等待\n", "            sleep(randint(1,3))\n", "            \n", "            # 下載 pdf\n", "            cmd = ['curl', '-L', myDict[\"論文連結_curl可用\"], '-o', f'{folderPath}/sn_{myDict[\"流水號\"]}.pdf']\n", "            result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT)\n", "            #output = result.stdout\n", "            #pprint(output)\n", "            print(f'{folderPath}/sn_{myDict[\"流水號\"]}.pdf')"]}, {"cell_type": "markdown", "id": "11bc8210", "metadata": {}, "source": ["# 以下函式，請各別依情況分別、陸續執行"]}, {"cell_type": "code", "execution_count": null, "id": "bc553b19", "metadata": {}, "outputs": [], "source": ["# 初始化 Web Driver\n", "init()"]}, {"cell_type": "code", "execution_count": null, "id": "1b8f747f", "metadata": {}, "outputs": [], "source": ["# 為 pagination 開啟 tabs\n", "openTabs()"]}, {"cell_type": "code", "execution_count": null, "id": "99c38b65", "metadata": {}, "outputs": [], "source": ["# 設定 tabs 內容\n", "setTabs()"]}, {"cell_type": "code", "execution_count": null, "id": "ebf04d5d", "metadata": {"scrolled": false}, "outputs": [], "source": ["# 剖析資料\n", "parse()"]}, {"cell_type": "code", "execution_count": null, "id": "d50c0d42", "metadata": {}, "outputs": [], "source": ["# 關閉瀏覽器\n", "close()"]}, {"cell_type": "code", "execution_count": null, "id": "f0f72510", "metadata": {}, "outputs": [], "source": ["# 儲存成 json\n", "<PERSON><PERSON><PERSON>()"]}, {"cell_type": "code", "execution_count": null, "id": "88ff61f6", "metadata": {}, "outputs": [], "source": ["# 儲存成 excel\n", "saveExcel()"]}, {"cell_type": "code", "execution_count": null, "id": "f219f0f9", "metadata": {}, "outputs": [], "source": ["# 下載\n", "time_begin = time.time()\n", "download()\n", "time_end = time.time()\n", "print(f\"總共執行了 { time_end - time_begin } 秒\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}}, "nbformat": 4, "nbformat_minor": 5}