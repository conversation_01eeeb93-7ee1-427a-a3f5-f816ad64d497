{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# SQLite\n", "參考連結\n", "- [SQLite 教程](https://www.runoob.com/sqlite/sqlite-tutorial.html)\n", "  - [SQLite - Python](https://www.runoob.com/sqlite/sqlite-python.html)\n", "- [DB Browser for SQLite](https://sqlitebrowser.org/)\n", "  - [下載](https://sqlitebrowser.org/dl/)\n", "- [Python 學習筆記 : 資料庫存取測試 (一) SQLite](https://yhhuang1966.blogspot.com/2018/04/python-sqlite_28.html)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 匯入套件\n", "import sqlite3"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 建立資料庫連線 (沒有資料庫時會自動建立)\n", "# 註: check_same_thread=False 是為了避免多執行緒時出現錯誤，可加可不加。\n", "conn = sqlite3.connect('test.db', check_same_thread=False)\n", "\n", "# 簡單顯示訊息，不成功會出現錯誤訊息\n", "print('資料庫連線建立成功')\n", "\n", "# 關閉資料庫連線\n", "conn.close()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 資料型態 (Date Type) 常用欄位簡介\n", "| 欄位 | 說明 |\n", "| --- | --- |\n", "| INTEGER | 整數 |\n", "| REAL | 浮點數 |\n", "| TEXT | 文字 |\n", "| BLOB | 二進位資料 |\n", "| NUMERIC | 數值 |\n", "| NULL | 空值 |\n", "\n", "註：\n", "- 如果使用 `INTEGER PRIMARY KEY` 作為 Primary Key，則該欄位會自動遞增。（INT 無法在 Primary Key 的情況下設定 AutoIncrement）\n", "- 如果數值是 30000.0，則會被視為 REAL，如果數值是 30000，則會被視為 INTEGER。\n", "- 如果數值是 30000.0，而欄位型態是 NUMERIC，則會為視為 INTEGER 格式儲存 (以 30000 儲存)。\n", "\n", "## 更進階的資料型態\n", "數字\n", "| 欄位 | 說明 |\n", "| --- | --- |\n", "| INTEGER | 32-bit 整數 |\n", "| INT | 32-bit 整數 (不能設定 Primary Key 的 AutoIncrement) |\n", "| TINYINT | 8-bit 整數 |\n", "| SMALLINT | 16-bit 整數 |\n", "| MEDIUMINT | 24-bit 整數 |\n", "| BIGINT | 64-bit 整數 |\n", "| UNSIGNED BIG INT | 64-bit 整數 |\n", "| INT2 | 16-bit 整數 |\n", "| INT8 | 64-bit 整數 |\n", "| REAL | 浮點數 |\n", "| DOUBLE | 浮點數 |\n", "| DOUBLE PRECISION | 浮點數 |\n", "| FLOAT | 浮點數 |\n", "\n", "\n", "文字\n", "| 欄位 | 說明 |\n", "| --- | --- |\n", "| CHARACTER(20) | 固定長度的字串，最多可以存放20個字元 | \n", "| VARCHAR(255) | 可變長度的字串，最多可以存放255個字元 | \n", "| VARYING CHARACTER(255) | 可變長度的字串，最多可以存放255個字元 |\n", "| NCHAR(55) | 固定長度的Unicode字串，最多可以存放55個字元 | \n", "| NATIVE CHARACTER(70) | 固定長度的本地字串，最多可以存放70個字元 | \n", "| NVARCHAR(100) | 可變長度的Unicode字串，最多可以存放100個字元 | \n", "| TEXT | 長度不限的字串 | \n", "| CLOB | 長度不限的字串，用於存放大量文本數據 |\n", "| DATE | 日期，格式為YYYY-MM-DD |\n", "| DATETIME | 日期和時間，格式為YYYY-MM-DD HH:MM:SS |\n", "\n", "註：\n", "- 在 MySQL 當中，CHARACTER 字數不足會補空格，VARCHAR 字數不足依然可正常儲存，不會補空格，超過會截斷。\n", "- 在 SQLite 中，這兩者的實際儲存方式沒有太大差異，因為 SQLite 不會嚴格區分這些類型。SQLite 會根據實際儲存的內容來決定儲存方式，因此在大多數情況下，CHARACTER 和 VARCHAR 的行為會非常相似。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 新增資料表\n", "語法\n", "```sql\n", "CREATE TABLE database_name.table_name(\n", "   column1 datatype  PRIMARY KEY,\n", "   column2 datatype,\n", "   column3 datatype,\n", "   .....\n", "   columnN datatype,\n", ");\n", "```\n", "\n", "\n", "範例\n", "```sql\n", "CREATE TABLE COMPANY(\n", "   ID             INT       PRIMARY KEY     NOT NULL,\n", "   NAME           TEXT      NOT NULL,\n", "   AGE            INT       NOT NULL,\n", "   ADDRESS        CHAR(50),\n", "   SALARY         REAL\n", ");\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 建立資料庫連線\n", "conn = sqlite3.connect('test.db')\n", "\n", "print (\"資料庫連線建立成功\")\n", "\n", "# cursor 物件，作用是執行 SQL 語句\n", "cursor = conn.cursor()\n", "\n", "# 建立資料表\n", "sql = '''\n", "CREATE TABLE COMPANY (\n", "    ID        INT PRIMARY KEY     NOT NULL,\n", "    NAME      TEXT    NOT NULL,\n", "    AGE       INT     NOT NULL,\n", "    ADDRESS   CHAR(50),\n", "    SALARY    REAL\n", ");\n", "'''\n", "cursor.execute(sql)\n", "\n", "print (\"資料表建立成功\")\n", "\n", "# 如果 execute 沒有錯誤，就會順利執行 commit，將資料寫入資料庫 \n", "conn.commit()\n", "\n", "# 關閉資料庫連線\n", "conn.close()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## CRUD (Create, Read, Update, Delete) 操作\n", "\n", "### CREATE\n", "```sql\n", "INSERT INTO TABLE_NAME (column1, column2, column3,...columnN)  \n", "VALUES (value1, value2, value3,...valueN);\n", "```\n", "\n", "範例\n", "```sql\n", "INSERT INTO COMPANY (ID, NAME, AGE, ADDRESS, SALARY)\n", "VALUES (1, '<PERSON>', 32, 'California', 20000.00 );\n", "\n", "INSERT INTO COMPANY (ID, NAME, AGE, ADDRESS, SALARY)\n", "VALUES (2, '<PERSON>', 25, 'Texas', 15000.00 );\n", "\n", "INSERT INTO COMPANY (ID, NAME, AGE, ADDRESS, SALARY)\n", "VALUES (3, '<PERSON>', 23, 'Norway', 20000.00 );\n", "\n", "INSERT INTO COMPANY (ID, NAME, AGE, ADDRESS, SALARY)\n", "VALUES (4, '<PERSON>', 25, '<PERSON><PERSON>Mon<PERSON> ', 65000.00 );\n", "\n", "INSERT INTO COMPANY (ID, NAME, AGE, ADDRESS, SALARY)\n", "VALUES (5, '<PERSON>', 27, 'Texas', 85000.00 );\n", "\n", "INSERT INTO COMPANY (ID, NAME, AGE, ADDRESS, SALARY)\n", "VALUES (6, '<PERSON>', 22, 'South-Hall', 45000.00 );\n", "\n", "INSERT INTO COMPANY (ID, NAME, AGE, ADDRESS, SALARY)\n", "VALUES (7, '<PERSON>', 24, '<PERSON>', 10000.00 );\n", "```\n", "\n", "### UPDATE\n", "```sql\n", "UPDATE table_name\n", "SET column1 = value1, column2 = value2...., columnN = valueN\n", "WHERE [condition];\n", "```\n", "\n", "範例\n", "```sql\n", "UPDATE COMPANY SET ADDRESS = 'Texas' WHERE ID = 6;\n", "```\n", "\n", "### DELETE\n", "```sql\n", "DELETE FROM table_name\n", "WHERE [condition];\n", "```\n", "\n", "範例\n", "```sql\n", "DELETE FROM COMPANY WHERE ID = 7;\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 執行 SQL\n", "```python\n", "# 只執行一句 SQL\n", "cursor.execute(sql)\n", "\n", "# 執行多句 SQL\n", "cursor.executescript(sql)\n", "```\n", "\n", "## SQL 參數化\n", "```python\n", "# 只執行一句 SQL\n", "cursor.execute(\"insert into people values (?, ?)\", (who, age))\n", "\n", "# 寫入多筆資料 (假設欄位是 id, name, age)\n", "sql = \"insert into people values (?, ?, ?)\"\n", "seq_of_parameters = [\n", "    (1, '<PERSON>', 30),\n", "    (2, '<PERSON>', 25),\n", "    (3, '<PERSON>', 35)\n", "]\n", "cursor.executemany(sql, seq_of_parameters)\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 資料庫連線\n", "conn = sqlite3.connect('test.db')\n", "\n", "# cursor 物件，作用是執行 SQL 語句\n", "cursor = conn.cursor()\n", "\n", "try:\n", "    # 定義 SQL 語句\n", "    sql_insert = '''\n", "    INSERT INTO COMPANY (ID, NAME, AGE, ADDRESS, SALARY)\n", "    VALUES (1, '<PERSON>', 32, 'California', 20000.00 );\n", "\n", "    INSERT INTO COMPANY (ID, NAME, AGE, ADDRESS, SALARY)\n", "    VALUES (2, '<PERSON>', 25, 'Texas', 15000.00 );\n", "\n", "    INSERT INTO COMPANY (ID, NAME, AGE, ADDRESS, SALARY)\n", "    VALUES (3, '<PERSON>', 23, 'Norway', 20000.00 );\n", "\n", "    INSERT INTO COMPANY (ID, NAME, AGE, ADDRESS, SALARY)\n", "    VALUES (4, '<PERSON>', 25, '<PERSON><PERSON>Mon<PERSON> ', 65000.00 );\n", "\n", "    INSERT INTO COMPANY (ID, NAME, AGE, ADDRESS, SALARY)\n", "    VALUES (5, '<PERSON>', 27, 'Texas', 85000.00 );\n", "\n", "    INSERT INTO COMPANY (ID, NAME, AGE, ADDRESS, SALARY)\n", "    VALUES (6, '<PERSON>', 22, 'South-Hall', 45000.00 );\n", "\n", "    INSERT INTO COMPANY (ID, NAME, AGE, ADDRESS, SALARY)\n", "    VALUES (7, '<PERSON>', 24, '<PERSON>', 10000.00 );\n", "    '''\n", "\n", "    # 註：也可以在這裡加入其它 SQL 語句，例如 UPDATE、DELETE 等等，只要用分號隔開即可\n", "\n", "    print('執行成功')\n", "\n", "    # 執行 SQL 語句\n", "    cursor.executescript(sql_insert)\n", "except sqlite3.OperationalError as e:\n", "    # 如果有錯誤，就回滾\n", "    conn.rollback()\n", "    print('執行失敗：', e)\n", "finally:\n", "    conn.close()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 查詢資料\n", "基本語法\n", "```sql\n", "SELECT column1, column2, ..., columnN FROM table_name;\n", "```\n", "\n", "不限定欄位\n", "```sql\n", "SELECT * FROM table_name;\n", "```\n", "\n", "比較運算\n", "```sql\n", "SELECT column1, column2, ..., columnN\n", "FROM table_name\n", "WHERE CONDITION;\n", "```\n", "參考網頁：[where 子句](https://www.runoob.com/sqlite/sqlite-where-clause.html)\n", "\n", "\n", "### 範例\n", "#### 薪水高於 60000 元的員工所有資料\n", "```sql\n", "SELECT * FROM COMPANY WHERE SALARY > 60000;\n", "```\n", "#### 年齡大於等於 25 歲的員工姓名\n", "```sql\n", "SELECT NAME FROM COMPANY WHERE AGE >= 25;\n", "```\n", "#### 年齡大於 25 歲且薪水高於 60000 元的員工姓名與地址\n", "```sql\n", "SELECT NAME, ADDRESS FROM COMPANY WHERE AGE >= 25 AND SALARY > 60000;\n", "```\n", "#### 取得 ID 為 1 的員工姓名\n", "```sql\n", "SELECT NAME FROM COMPANY WHERE ID = 1;\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["'''取得所有員工資料的範例'''\n", "\n", "# 資料庫連線\n", "conn = sqlite3.connect('test.db')\n", "\n", "# cursor 物件，作用是執行 SQL 語句\n", "cursor = conn.cursor()\n", "\n", "'''\n", "設定 cursor 的 row_factory 屬性\n", "在這裡設定成 sqlite3.Row，可以讓資料以 dict 的方式取得\n", "'''\n", "# cursor.row_factory = sqlite3.Row\n", "\n", "# 定義 SQL 語句\n", "sql = '''\n", "SELECT NAME, ADDRESS FROM COMPANY WHERE AGE >= 25 AND SALARY > 60000;\n", "'''\n", "\n", "# 執行 SQL 語句\n", "cursor.execute(sql)\n", "\n", "# 取得所有資料\n", "rows = cursor.fetchall()\n", "\n", "# 顯示資料\n", "for row in rows:\n", "    print(row)\n", "\n", "# 關閉資料庫連線\n", "conn.close()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["'''取得一筆員工資料的範例'''\n", "\n", "# 資料庫連線\n", "conn = sqlite3.connect('test.db')\n", "\n", "# cursor 物件，作用是執行 SQL 語句\n", "cursor = conn.cursor()\n", "\n", "'''\n", "設定 cursor 的 row_factory 屬性\n", "在這裡設定成 sqlite3.Row，可以讓資料以 dict 的方式取得\n", "'''\n", "# cursor.row_factory = sqlite3.Row\n", "\n", "# 定義 SQL 語句\n", "sql = '''\n", "SELECT NAME FROM COMPANY WHERE ID = 1;\n", "'''\n", "\n", "# 執行 SQL 語句\n", "cursor.execute(sql)\n", "\n", "# 取得一筆資料\n", "row = cursor.fetchone()\n", "print(row)\n", "\n", "# 關閉資料庫連線\n", "conn.close()"]}], "metadata": {"kernelspec": {"display_name": "test", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 2}