{"cells": [{"cell_type": "code", "execution_count": null, "id": "d2dc61bd", "metadata": {}, "outputs": [], "source": ["# 匯入套件\n", "import requests as req\n", "from bs4 import BeautifulSoup as bs\n", "import re, json\n", "from time import sleep\n", "from random import randint\n", "\n", "# 隨機取得 User-Agent\n", "'''\n", "# 從外部資料來取得清單，清單預設儲存路徑: /tmp\n", "ua = UserAgent(use_external_data=True)\n", "# 從外部資料來取得清單，儲存在指定路徑\n", "ua = UserAgent(use_external_data=True, cache_path=/home/<USER>\n", "\n", "更詳細的說明，請見以下網頁:\n", "https://pypi.org/project/fake-useragent/\n", "'''\n", "from fake_useragent import UserAgent\n", "ua = UserAgent(use_external_data=True)\n", "\n", "# 設定請求標頭\n", "my_headers = {\n", "    'user-agent': ua.random\n", "}\n", "\n", "# 設定 cookie\n", "my_cookies = {\n", "    'age_checked_for': \"12190\"\n", "}\n", "\n", "# 整理所有取得資料的變數\n", "listData = []\n", "\n", "# 欲抓取資料的網址\n", "domainName = 'https://www.zeczec.com/'\n", "\n", "# 指定最後頁數\n", "pages = 1\n", "\n", "# 取得首頁列表資訊\n", "def getMainData():\n", "    for page in range(1, pages + 1):\n", "        # 取得回應\n", "        res = req.get(url = f'{domainName}categories?page={page}', headers = my_headers)\n", "\n", "        # 初始化 soup 物件\n", "        soup = bs(res.text, 'lxml')\n", "\n", "        # 取得所有超連結\n", "        for a in soup.select('div.flex.gutter3-l a.db'):\n", "            # 取得圖片連結\n", "            strStyle = a.select_one('div.aspect-ratio-project-cover')['data-bg']\n", "            regexImg = r\"https:\\/\\/assets\\.zeczec\\.com\\/asset_\\d+_image_big\\.(jpe?g|png)\"\n", "            matchImg = re.match(regexImg, strStyle)\n", "            strImg = matchImg[0]\n", "\n", "            # 取得超連結\n", "            strLink = domainName + a['href']\n", "\n", "            # 取得標題文字\n", "            strTitle = a.select_one('h3.b').get_text()\n", "\n", "            # 整理首頁資料\n", "            listData.append({\n", "                \"cover\": strImg,\n", "                \"link\": strLink,\n", "                \"title\": str<PERSON><PERSON>le\n", "            })\n", "\n", "# 取得詳細頁面資訊\n", "def getDetailData():\n", "    # 走訪每一個頁面\n", "    for index, _dict in enumerate(listData):        \n", "        # 輸出網址，以便於 debug\n", "        print(_dict['link'])\n", "        \n", "        # 取得回應\n", "        res = req.get(url = _dict['link'], headers = my_headers, cookies = my_cookies)\n", "\n", "        # 初始化 soup 物件\n", "        soup = bs(res.text, 'lxml')\n", "        \n", "        # 取得價格\n", "        strPrice = soup.select_one('div.js-sum-raised').get_text()\n", "        regexPrice = r\"\\D\"\n", "        strPrice = re.sub(regexPrice, \"\", strPrice)\n", "        \n", "        # 取得贊助人數\n", "        strBacker = soup.select_one('span.js-backers-count').get_text()\n", "        \n", "        # 取得剩餘時間\n", "        strTime = \"longterm\"\n", "        if soup.select_one('span.js-time-left') != None:\n", "            strTime = soup.select_one('span.js-time-left').get_text()\n", "            regexTime = r\"\\d+\"\n", "            strTime = re.search(regexTime, strTime)[0]\n", "            \n", "        # 取得持續時間\n", "        dictDuration = {\"begin\": \"\", \"end\": \"\"}\n", "        strDuration = soup.select_one('div.mb2.f7').get_text()\n", "        regexDuration = r\"(\\d{4}\\/\\d{2}\\/\\d{2}\\s\\d{2}:\\d{2})(\\s–\\s(\\d{4}\\/\\d{2}\\/\\d{2}\\s\\d{2}:\\d{2}))?\"\n", "        matchDuration = re.search(regexDuration, strDuration)\n", "        dictDuration['begin'] = matchDuration[1]\n", "        if matchDuration[3] != None:\n", "            dictDuration['end'] = matchDuration[3]\n", "            \n", "        # 整理詳細頁面資料\n", "        listData[index]['price'] = strPrice\n", "        listData[index]['backer'] = strBacker\n", "        listData[index]['time'] = strTime\n", "        listData[index]['duration'] = dictDuration\n", "        \n", "        # 隨機等待\n", "        sleep(randint(5,10))\n", "\n", "# 儲存成 json 檔案\n", "def save<PERSON><PERSON>():\n", "    with open(\"crowdfunding.json\", \"w\", encoding=\"utf-8\") as file:\n", "        file.write( json.dumps(listData, ensure_ascii=False, indent=4) )"]}, {"cell_type": "code", "execution_count": null, "id": "c7ef6fb5", "metadata": {}, "outputs": [], "source": ["# 主程式\n", "if __name__ == \"__main__\":\n", "    getMainData()\n", "    getDetailData()\n", "    <PERSON><PERSON><PERSON>()"]}], "metadata": {"kernelspec": {"display_name": "Python 3.9.13 ('base')", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}, "vscode": {"interpreter": {"hash": "585a938ec471c889bf0cce0aed741a99eaf47ca09c0fa8393793bc5bfe77ba11"}}}, "nbformat": 4, "nbformat_minor": 5}