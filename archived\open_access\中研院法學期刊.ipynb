{"cells": [{"cell_type": "code", "execution_count": null, "id": "40bcb98c", "metadata": {}, "outputs": [], "source": ["!pip install -U openpyxl selenium beautifulsoup4 lxml requests tika"]}, {"cell_type": "code", "execution_count": null, "id": "f084f330", "metadata": {}, "outputs": [], "source": ["'''\n", "注意事項:\n", "下載對應的 ChromeDriver (web driver) 到程式檔案同一個目錄下後解壓縮，下載前記得對應版本編號。\n", "連結: https://chromedriver.chromium.org/downloads\n", "\n", "參考網頁:\n", "[1] 中研院法學期刊\n", "https://www.iias.sinica.edu.tw/publication_list/9\n", "[2] sqlite3 --- SQLite 数据库 DB-API 2.0 接口模块\n", "https://docs.python.org/zh-tw/3/library/sqlite3.html\n", "[3] Selenium give file name when downloading\n", "https://stackoverflow.com/questions/34548041/selenium-give-file-name-when-downloading\n", "[4] Convert Images to PDF using Python\n", "https://datatofish.com/images-to-pdf-python/\n", "'''\n", "\n", "\n", "'''\n", "匯入套件\n", "'''\n", "# 網路請求工具\n", "import requests as req\n", "\n", "# HTML parser\n", "from bs4 import BeautifulSoup as bs\n", "\n", "# 操作 browser 的 API\n", "from selenium import webdriver\n", "\n", "# 處理逾時例外的工具\n", "from selenium.common.exceptions import TimeoutException\n", "\n", "# 面對動態網頁，等待某個元素出現的工具，通常與 exptected_conditions 搭配\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "\n", "# 搭配 WebDriverWait 使用，對元素狀態的一種期待條件，若條件發生，則等待結束，往下一行執行\n", "from selenium.webdriver.support import expected_conditions as EC\n", "\n", "# 期待元素出現要透過什麼方式指定，通常與 EC、WebDriverWait 一起使用\n", "from selenium.webdriver.common.by import By\n", "\n", "# 處理下拉式選單的工具\n", "from selenium.webdriver.support.ui import Select\n", "\n", "# 強制等待 (執行期間休息一下)\n", "from time import sleep\n", "\n", "# pretty-print\n", "from pprint import pprint\n", "\n", "# 隨機\n", "from random import randint\n", "\n", "# 計時\n", "import time\n", "\n", "# 整理 json 使用的工具\n", "import json\n", "\n", "# 執行 shell command 的時候用的\n", "import os\n", "\n", "# 取得錯誤訊息\n", "import sys, traceback\n", "\n", "# 子處理程序，用來取代 os.system 的功能\n", "import subprocess\n", "\n", "# 正規表達式\n", "import re\n", "\n", "# 編碼\n", "from urllib.parse import quote\n", "\n", "# SQLite 資料庫\n", "import sqlite3\n", "\n", "# 存取 Excel 的工具\n", "from openpyxl import load_workbook\n", "from openpyxl import Workbook\n", "\n", "# 取得系統時間的工具\n", "from datetime import datetime\n", "\n", "# 引入 hashlib 模組\n", "import hashlib\n", "\n", "# 高階文件操作工具\n", "import shutil\n", "\n", "# 檔案剖析工具\n", "from tika import parser"]}, {"cell_type": "code", "execution_count": null, "id": "8d4af6b7", "metadata": {}, "outputs": [], "source": ["# 啟動瀏覽器工具的選項\n", "my_options = webdriver.ChromeOptions()\n", "# my_options.add_argument(\"--headless\")             #不開啟實體瀏覽器背景執行\n", "my_options.add_argument(\"--start-maximized\")        #最大化視窗\n", "my_options.add_argument(\"--incognito\")              #開啟無痕模式\n", "my_options.add_argument(\"--disable-popup-blocking\") #禁用彈出攔截\n", "my_options.add_argument(\"--disable-notifications\")  #取消通知\n", "my_options.add_argument(\"--lang=zh-TW\")  #設定為正體中文\n", "my_options.add_argument('--disable-gpu')\n", "my_options.add_argument('--disable-software-rasterizer')\n", "my_options.add_argument('--user-agent=\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Safari/537.36\"')\n", "\n", "# 給 web driver 用的變數\n", "driver = None\n", "\n", "# 來源首頁\n", "root_url = 'https://www.iias.sinica.edu.tw'\n", "prefix_url = root_url + '/publication_list'\n", "path_url = prefix_url + '/9'\n", "url = path_url + ''\n", "\n", "# 指定 sheet name\n", "folderName = sheetName = 'www_iias_sinica_edu_tw'\n", "\n", "# 指定 json 檔名\n", "jsonFileName = f'{folderName}.json'\n", "\n", "# 建立儲存檔案用的資料夾\n", "folderPath = f'./{folderName}'\n", "if not os.path.exists(folderPath):\n", "    os.makedirs(folderPath)\n", "    \n", "# 設定 Chrome 下載路徑 (需要絕對路徑)\n", "fullDownloadPath = os.getcwd() + '\\\\' + folderName\n", "\n", "#預設下載路徑\n", "my_options.add_experimental_option(\"prefs\", {\n", "    \"download.default_directory\": fullDownloadPath,\n", "    \"download.prompt_for_download\": <PERSON>als<PERSON>,\n", "    \"download.directory_upgrade\": True,\n", "    \"safebrowsing_for_trusted_sources_enabled\": False,\n", "    \"safebrowsing.enabled\": <PERSON><PERSON><PERSON>,\n", "    \"plugins.always_open_pdf_externally\": True\n", "})\n", "\n", "# 請求標頭\n", "my_headers = {\n", "    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.54 Safari/537.36'\n", "}\n", "\n", "# 放置爬取的資料\n", "listData = []\n", "\n", "# 建立一個 Set 物件，準備 add 所有 tuple，這些 tuple 裡面都有 dict_items 物件\n", "mySet = set()"]}, {"cell_type": "code", "execution_count": null, "id": "86fae03f", "metadata": {}, "outputs": [], "source": ["'''\n", "函式\n", "'''\n", "# md5 (用來為每一筆資料建立唯一代號)\n", "def md5(string):\n", "    m = hashlib.md5()\n", "    m.update(string.encode(\"utf-8\"))\n", "    return m.hexdigest()\n", "\n", "# 初始化設定\n", "def init():\n", "    global listData, mySet\n", "    listData = []\n", "    mySet = set()\n", "    \n", "# 走訪頁面\n", "def visit():\n", "    global listData, mySet\n", "    \n", "    # 取得 html 並轉成 soup 物件\n", "    res = req.get(url = url, headers = my_headers)\n", "    res.encoding = 'utf-8'\n", "    soup = bs(res.text, 'lxml')\n", "    \n", "    # 判斷期刊連結是否存在\n", "    if len( soup.select('div.content a[href*=publication_post]') ) > 0:\n", "        for a in soup.select('div.content a[href*=publication_post]'):\n", "            # 判斷是否重複取得 link\n", "            if a['href'] not in mySet:\n", "                # 加入 set，作為後續判斷\n", "                mySet.add(a['href'])\n", "                \n", "                # 整理資料\n", "                listData.append({\n", "                    \"journal_title\": \"中研院法學期刊\",\n", "                    \"journal_sub_title\": \"\",\n", "                    \"publish_num\": a['title'],\n", "                    \"link\": f\"{root_url}/{a['href']}\",\n", "                    \"sub\": []\n", "                })\n", "\n", "        # 變數初始化\n", "        mySet = set()\n", "    \n", "# 剖析元素資料\n", "def parse():\n", "    global listData\n", "    \n", "    # 走訪內頁\n", "    for index, myDict in enumerate(listData):\n", "        # 取得 html 與 soup 物件\n", "        res = req.get(url = myDict['link'], headers = my_headers)\n", "        res.encoding = 'utf-8'\n", "        soup = bs(res.text, 'lxml')\n", "\n", "        # 變數初始化\n", "        journal_name = publish_date = pdf_link = author = ''\n", "        \n", "        # 取得出版日期\n", "        regexPublishDate = r'\\d{4}-\\d{1,2}' \n", "        str_publish_date = soup.select_one('div.content div.publication-info.share ul:not([class])').get_text()\n", "        matchPublishDate = re.search(regexPublishDate, str_publish_date)\n", "        if matchPublishDate != None: \n", "            listData[index]['publish_date'] = publish_date = matchPublishDate[0]\n", "            \n", "        # 取得 篇名、作者、pdf 連結\n", "        for div in soup.select('div.chapter'):\n", "            # 有 pdf 連結的 div 才進行擷取\n", "            if len(div.select('ul.download li a[href*=pdf]')) > 0:\n", "                # 篇名\n", "                journal_name = div.select_one('h2').get_text()\n", "                \n", "                # 作者\n", "                author = div.select('ul.author li')[0].get_text()\n", "                author = re.sub(r'作者|\\s|&nbsp;', '', author)\n", "                \n", "                # pdf 連結\n", "                pdf_link = div.select_one('ul.download li a[href*=pdf]')['href']\n", "                \n", "                # 整理資料\n", "                listData[index]['sub'].append({\n", "                    \"id\": md5(pdf_link),\n", "                    \"journal_name\": journal_name,\n", "                    \"author\": author,\n", "                    \"pdf_link\": pdf_link\n", "                })\n", "        \n", "        \n", "# 儲存成 json\n", "def save<PERSON><PERSON>():\n", "    global listData\n", "    with open(f\"{folderPath}/{jsonFileName}\", \"w\", encoding=\"utf-8\") as file:\n", "        file.write( json.dumps( listData, ensure_ascii=False, indent=4 ) )\n", "\n", "# 建立 .db\n", "def makeDB():\n", "    conn = sqlite3.connect(f\"{folderPath}/{folderName}.db\")\n", "    cursor = conn.cursor()\n", "    sql = '''\n", "    CREATE TABLE \"journals\" (\n", "        \"sn\"    INTEGER,\n", "        \"id\"    TEXT UNIQUE,\n", "        \"journal_title\"    TEXT,\n", "        \"journal_sub_title\"    TEXT,\n", "        \"publish_num\"    TEXT,\n", "        \"publish_date\"    TEXT,\n", "        \"journal_name\"    TEXT,\n", "        \"author\"    <PERSON><PERSON><PERSON>,\n", "        \"link\"    TEXT,\n", "        \"pdf_link\"    TEXT,\n", "        \"is_downloaded\"    INTEGER,\n", "        \"created_at\"    TEXT,\n", "        \"updated_at\"    TEXT,\n", "        PRIMARY KEY(\"sn\" AUTOINCREMENT)\n", "    );\n", "    '''\n", "    cursor.execute(sql)\n", "    conn.commit()\n", "    \n", "    # 關閉 sqlite\n", "    conn.close()\n", "    \n", "    \n", "# 儲存 .db\n", "def saveDB():\n", "    with open(f\"{folderPath}/{jsonFileName}\", \"r\", encoding=\"utf-8\") as file:      \n", "        # 取得 json 內容\n", "        strJson = file.read()\n", "        \n", "        # 將 json 轉成 list\n", "        listJson = json.loads(strJson)\n", "        \n", "    # 寫入對話記錄\n", "    conn = sqlite3.connect(f\"{folderPath}/{folderName}.db\")\n", "    \n", "    # 建立 cursor 物件\n", "    cursor = conn.cursor()\n", "\n", "    # 執行 SQL 語法\n", "    try:\n", "        # 查詢特定資料，看看是否已經存在於資料表當中\n", "        sql_query = f'''\n", "        SELECT 1\n", "        FROM journals\n", "        WHERE id = ?\n", "        '''\n", "        \n", "        # 寫入資料\n", "        sql_insert = f'''\n", "        INSERT INTO journals (\n", "            id, journal_title, journal_sub_title, publish_num, publish_date, \n", "            journal_name, author, link, pdf_link, is_downloaded, \n", "            created_at, updated_at\n", "        ) VALUES ( \n", "            ?,?,?,?,?,\n", "            ?,?,?,?,?,\n", "            ?,?\n", "        )\n", "        '''\n", "        \n", "        # 放置準備寫入的資料\n", "        list_insert = []\n", "        \n", "        # 將 json 資料一筆一筆找出來\n", "        for index, myDict in enumerate(listJson):\n", "            for d in listJson[index]['sub']:\n", "                # 如果資料庫沒有這筆資料(透過 id)，則將資料以 tuple 格式放到 list 當中，方便新增 bulk 資料\n", "                if cursor.execute(sql_query, (d[\"id\"],)).fetchone() == None:\n", "                    # 整合所有需要寫入的資料\n", "                    list_insert.append((\n", "                        d['id'],\n", "                        myDict['journal_title'],\n", "                        myDict['journal_sub_title'],\n", "                        myDict['publish_num'],\n", "                        myDict['publish_date'],\n", "                        d['journal_name'],\n", "                        d['author'],\n", "                        myDict['link'],\n", "                        d['pdf_link'],\n", "                        0,\n", "                        datetime.today().strftime(\"%Y-%m-%d %H-%M-%S\"),\n", "                        datetime.today().strftime(\"%Y-%m-%d %H-%M-%S\")\n", "                    ))\n", "        \n", "        # 新增資料到資料庫當中\n", "        cursor.executemany(sql_insert, list_insert)\n", "        \n", "        # 執行 SQL 語法\n", "        conn.commit()\n", "    except sqlite3.<PERSON>rror as err: \n", "        # 回滾\n", "        conn.rollback()\n", "\n", "        # SQLite3 例外處理\n", "        exc_type, exc_value, exc_tb = sys.exc_info()\n", "        strErrorMsg = f'''SQLite error: {' '.join(err.args)}\\n\\n\n", "        SQLite traceback: {traceback.format_exception(exc_type, exc_value, exc_tb)}\n", "        '''\n", "        print(strErrorMsg)\n", "    finally:\n", "        # 關閉 sqlite\n", "        conn.close()\n", "        \n", "# 下載\n", "def download():\n", "    # 寫入對話記錄\n", "    conn = sqlite3.connect(f\"{folderPath}/{folderName}.db\")\n", "    \n", "    # 將查詢出來的結果 (tuple)，變成 key-value 型式 (dict)\n", "    conn.row_factory = sqlite3.Row\n", "    \n", "    # 建立 cursor 物件\n", "    cursor = conn.cursor()\n", "\n", "    # 執行 SQL 語法\n", "    try:\n", "        # 查詢尚未下載的資料\n", "        sql_query = f'''\n", "        SELECT sn, id, pdf_link\n", "        FROM journals\n", "        WHERE `is_downloaded` = 0\n", "        '''\n", "        \n", "        # 更新資料的欄位(是否下載過)\n", "        sql_update = f'''\n", "        UPDATE `journals` \n", "        SET \n", "            `is_downloaded` = 1 ,\n", "            `updated_at` = ?\n", "        WHERE `id` = ?\n", "        '''\n", "            \n", "        # 取得所有未下載的資料\n", "        for myDict in cursor.execute(sql_query).fetchall():\n", "            # 等待\n", "            sleep(randint(1,2))\n", "\n", "            # 下載 pdf\n", "            cmd = [\n", "                'curl', \n", "                '-k', '-L', \n", "                '-H', 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Safari/537.36',\n", "                myDict[\"pdf_link\"], \n", "                '-o', f'{folderPath}/{myDict[\"id\"]}.pdf'\n", "            ]\n", "            result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT)\n", "            pprint(result.stdout)\n", "            print(\"=\" * 50)\n", "\n", "            # 將 is_downloaded 改成 1，代表已下載過\n", "            cursor.execute(sql_update, (datetime.today().strftime(\"%Y-%m-%d %H-%M-%S\"), myDict[\"id\"],))\n", "                \n", "        conn.commit()\n", "    except sqlite3.<PERSON>rror as err: \n", "        # 回滾\n", "        conn.rollback()\n", "\n", "        # SQLite3 例外處理\n", "        exc_type, exc_value, exc_tb = sys.exc_info()\n", "        strErrorMsg = f'''SQLite error: {' '.join(err.args)}\\n\\n\n", "        SQLite traceback: {traceback.format_exception(exc_type, exc_value, exc_tb)}\n", "        '''\n", "        print(strErrorMsg)\n", "    finally:\n", "        # 關閉 sqlite\n", "        conn.close()"]}, {"cell_type": "code", "execution_count": null, "id": "e896e867", "metadata": {}, "outputs": [], "source": ["init()"]}, {"cell_type": "code", "execution_count": null, "id": "c054551f", "metadata": {}, "outputs": [], "source": ["visit()"]}, {"cell_type": "code", "execution_count": null, "id": "71150803", "metadata": {}, "outputs": [], "source": ["parse()"]}, {"cell_type": "code", "execution_count": null, "id": "a1cd1a9f", "metadata": {}, "outputs": [], "source": ["<PERSON><PERSON><PERSON>()"]}, {"cell_type": "code", "execution_count": null, "id": "8a6c836c", "metadata": {}, "outputs": [], "source": ["makeDB()"]}, {"cell_type": "code", "execution_count": null, "id": "7549a265", "metadata": {}, "outputs": [], "source": ["saveDB()"]}, {"cell_type": "code", "execution_count": null, "id": "0de16701", "metadata": {}, "outputs": [], "source": ["download()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}}, "nbformat": 4, "nbformat_minor": 5}